#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硅基流动索引生成器 - 主运行脚本
功能：
1. 整合所有组件
2. 实现完整的索引生成和同步流程
3. 提供命令行和配置文件支持
4. 监控和日志记录
"""

import asyncio
import argparse
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import yaml

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from siliconflow_index_generator import SiliconFlowIndexGenerator, KnowledgeSegment, ProductAttribute
from fastgpt_api_client import FastGPTAPIClient, FastGPTDataItem
from config.siliconflow_config import SiliconFlowConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/index_generator_main.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class IndexGeneratorRunner:
    """索引生成器运行器"""
    
    def __init__(self, config_file: str = None):
        """
        初始化运行器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self.load_config()
        
        # 验证配置
        self.validate_config()
        
        # 初始化组件
        self.siliconflow_config = SiliconFlowConfig()
        self.generator = None
        self.fastgpt_client = None
        
        # 运行统计
        self.run_stats = {
            'start_time': datetime.now(),
            'total_documents': 0,
            'processed_documents': 0,
            'generated_indexes': 0,
            'synced_indexes': 0,
            'failed_operations': 0,
            'business_blocks_processed': [],
            'errors': []
        }
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置
        
        Returns:
            配置字典
        """
        # 默认配置
        default_config = {
            'siliconflow': {
                'api_key': 'sk-kmaipghbqavpzfnhpuuybpgrimcroynvsqlfkbnhcjcdulxj',
                'models': {
                    'text': 'Qwen/Qwen2.5-72B-Instruct',
                    'embedding': 'BAAI/bge-large-zh-v1.5'
                },
                'temperature': 0.3,
                'max_tokens': 2000
            },
            'fastgpt': {
                'api_url': 'https://api.fastgpt.in/api',
                'api_key': '',
                'dataset_id': ''
            },
            'processing': {
                'batch_size': 5,
                'delay_between_batches': 1.0,
                'max_retries': 3,
                'timeout': 30
            },
            'business_blocks': {
                '门禁系统': ['门禁控制器', '读卡器', '门禁软件', '门禁配件'],
                '考勤系统': ['考勤机', '考勤软件', '考勤配件'],
                '访客系统': ['访客机', '访客软件', '访客配件'],
                '消费系统': ['消费机', '消费软件', '消费配件'],
                '通道系统': ['通道闸', '通道控制器', '通道软件'],
                '停车系统': ['停车设备', '停车软件', '停车配件']
            },
            'output': {
                'save_results': True,
                'results_dir': 'results',
                'export_format': ['json', 'csv']
            }
        }
        
        # 如果指定了配置文件，则加载
        if self.config_file and os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    if self.config_file.endswith('.yaml') or self.config_file.endswith('.yml'):
                        file_config = yaml.safe_load(f)
                    else:
                        file_config = json.load(f)
                
                # 合并配置
                self.merge_config(default_config, file_config)
                logger.info(f"已加载配置文件: {self.config_file}")
                
            except Exception as e:
                logger.warning(f"加载配置文件失败: {str(e)}，使用默认配置")
        
        # 从环境变量覆盖敏感配置
        if os.getenv('SILICONFLOW_API_KEY'):
            default_config['siliconflow']['api_key'] = os.getenv('SILICONFLOW_API_KEY')
        
        if os.getenv('FASTGPT_API_KEY'):
            default_config['fastgpt']['api_key'] = os.getenv('FASTGPT_API_KEY')
        
        if os.getenv('FASTGPT_DATASET_ID'):
            default_config['fastgpt']['dataset_id'] = os.getenv('FASTGPT_DATASET_ID')
        
        return default_config
    
    def merge_config(self, base: Dict, override: Dict):
        """
        合并配置
        
        Args:
            base: 基础配置
            override: 覆盖配置
        """
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self.merge_config(base[key], value)
            else:
                base[key] = value
    
    def validate_config(self):
        """
        验证配置
        """
        required_keys = [
            'siliconflow.api_key',
            'fastgpt.api_url',
            'fastgpt.api_key',
            'fastgpt.dataset_id'
        ]
        
        for key_path in required_keys:
            keys = key_path.split('.')
            current = self.config
            
            for key in keys:
                if key not in current:
                    raise ValueError(f"缺少必需的配置项: {key_path}")
                current = current[key]
            
            if not current:
                logger.warning(f"配置项为空: {key_path}")
    
    async def initialize_components(self):
        """
        初始化组件
        """
        try:
            # 初始化硅基流动索引生成器
            self.generator = SiliconFlowIndexGenerator(
                api_key=self.config['siliconflow']['api_key'],
                fastgpt_config=self.config['fastgpt']
            )
            
            # 初始化FastGPT客户端
            self.fastgpt_client = FastGPTAPIClient(
                api_url=self.config['fastgpt']['api_url'],
                api_key=self.config['fastgpt']['api_key'],
                dataset_id=self.config['fastgpt']['dataset_id']
            )
            
            logger.info("组件初始化完成")
            
        except Exception as e:
            logger.error(f"组件初始化失败: {str(e)}")
            raise
    
    async def test_connections(self) -> bool:
        """
        测试连接
        
        Returns:
            是否所有连接都成功
        """
        logger.info("开始测试连接...")
        
        try:
            # 测试FastGPT连接
            async with self.fastgpt_client as client:
                success, message = await client.test_connection()
                if success:
                    logger.info(f"FastGPT连接成功: {message}")
                else:
                    logger.error(f"FastGPT连接失败: {message}")
                    return False
            
            # 测试硅基流动API（通过简单调用）
            async with self.generator as gen:
                # 这里可以添加简单的API测试
                logger.info("硅基流动API配置正常")
            
            return True
            
        except Exception as e:
            logger.error(f"连接测试失败: {str(e)}")
            return False
    
    async def process_sample_data(self) -> Dict[str, Any]:
        """
        处理示例数据
        
        Returns:
            处理结果
        """
        logger.info("开始处理示例数据...")
        
        # 生成示例产品数据
        sample_products = self.generate_sample_products()
        
        results = {
            'business_blocks': {},
            'total_indexes': 0,
            'successful_syncs': 0,
            'failed_syncs': 0
        }
        
        try:
            async with self.generator as gen, self.fastgpt_client as client:
                # 按业务块处理
                for business_block, products_data in sample_products.items():
                    logger.info(f"处理业务块: {business_block}")
                    
                    # 生成索引
                    block_result = await gen.batch_process_by_business_block(
                        business_block, products_data
                    )
                    
                    # 同步到FastGPT
                    sync_results = await self.sync_indexes_to_fastgpt(
                        client, block_result['products_results']
                    )
                    
                    # 统计结果
                    results['business_blocks'][business_block] = {
                        'generation_result': block_result,
                        'sync_results': sync_results
                    }
                    
                    results['total_indexes'] += block_result['total_indexes']
                    results['successful_syncs'] += sum(1 for r in sync_results if r.success)
                    results['failed_syncs'] += sum(1 for r in sync_results if not r.success)
                    
                    # 更新运行统计
                    self.run_stats['business_blocks_processed'].append(business_block)
                    self.run_stats['generated_indexes'] += block_result['successful_indexes']
                    self.run_stats['synced_indexes'] += sum(1 for r in sync_results if r.success)
                    
                    logger.info(f"业务块 {business_block} 处理完成")
            
            logger.info(f"示例数据处理完成 - 总索引: {results['total_indexes']}, 同步成功: {results['successful_syncs']}")
            return results
            
        except Exception as e:
            logger.error(f"处理示例数据失败: {str(e)}")
            self.run_stats['errors'].append(str(e))
            raise
    
    def generate_sample_products(self) -> Dict[str, Dict[str, List[Dict]]]:
        """
        生成示例产品数据
        
        Returns:
            示例产品数据
        """
        sample_data = {}
        
        for business_block, product_types in self.config['business_blocks'].items():
            sample_data[business_block] = {}
            
            for product_type in product_types:
                # 为每个产品类型生成示例文档
                sample_data[business_block][product_type] = [
                    {
                        'title': f'{product_type}产品规格说明',
                        'content': f'''
{product_type}详细技术规格

产品概述：
{product_type}是{business_block}的核心组件，具有高性能、高可靠性的特点。

主要功能：
1. 核心功能模块
2. 通讯接口支持
3. 数据处理能力
4. 系统集成特性

技术参数：
- 工作电压：DC 12V
- 工作温度：-20°C ~ +60°C
- 通讯方式：TCP/IP、RS485
- 存储容量：根据型号不同

应用场景：
适用于各种{business_block}应用场景，包括企业办公、工厂车间、学校医院等。

安装维护：
产品安装简便，维护方便，支持远程监控和管理。
                        ''',
                        'name': product_type,
                        'category': business_block,
                        'specifications': {
                            '型号': f'{product_type}-STD',
                            '版本': 'V2.0',
                            '认证': 'CE, FCC',
                            '保修': '2年'
                        },
                        'features': [
                            '高性能处理器',
                            '多种通讯接口',
                            '友好用户界面',
                            '远程管理功能'
                        ]
                    },
                    {
                        'title': f'{product_type}安装指南',
                        'content': f'''
{product_type}安装配置指南

安装前准备：
1. 确认安装环境符合要求
2. 准备必要的安装工具
3. 检查产品包装完整性

安装步骤：
1. 选择合适的安装位置
2. 固定设备支架
3. 连接电源和通讯线缆
4. 进行系统配置
5. 测试设备功能

配置说明：
- 网络配置：设置IP地址、子网掩码、网关
- 系统参数：根据实际需求调整
- 用户权限：设置管理员和普通用户权限

调试测试：
完成安装后，进行全面的功能测试，确保设备正常工作。
                        ''',
                        'name': f'{product_type}安装指南',
                        'category': f'{business_block}安装',
                        'document_type': '安装指南'
                    }
                ]
        
        return sample_data
    
    async def sync_indexes_to_fastgpt(self, client: FastGPTAPIClient, 
                                    products_results: List[Dict]) -> List:
        """
        同步索引到FastGPT
        
        Args:
            client: FastGPT客户端
            products_results: 产品处理结果
            
        Returns:
            同步结果列表
        """
        all_sync_results = []
        
        for product_result in products_results:
            for index_info in product_result.get('generated_indexes', []):
                try:
                    # 创建FastGPT数据项
                    data_item = FastGPTDataItem(
                        q=index_info['title'],
                        a=f"这是关于{product_result['product_name']}的详细信息。",
                        indexes=[
                            {
                                'text': f"{index_info['title']} {' '.join(index_info.get('tags', []))}",
                                'dataId': index_info['id']
                            }
                        ],
                        metadata={
                            'product_name': product_result['product_name'],
                            'business_block': product_result['business_block'],
                            'source': 'siliconflow_generator',
                            'generated_at': datetime.now().isoformat()
                        },
                        tags=index_info.get('tags', []),
                        dataId=index_info['id']
                    )
                    
                    # 同步到FastGPT
                    sync_result = await client.insert_data_item(data_item)
                    all_sync_results.append(sync_result)
                    
                except Exception as e:
                    logger.error(f"同步索引失败: {str(e)}")
                    self.run_stats['failed_operations'] += 1
        
        return all_sync_results
    
    def save_results(self, results: Dict[str, Any]):
        """
        保存结果
        
        Args:
            results: 处理结果
        """
        if not self.config['output']['save_results']:
            return
        
        # 创建结果目录
        results_dir = Path(self.config['output']['results_dir'])
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存JSON格式
        if 'json' in self.config['output']['export_format']:
            json_file = results_dir / f'index_results_{timestamp}.json'
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"结果已保存到: {json_file}")
        
        # 保存运行统计
        stats_file = results_dir / f'run_stats_{timestamp}.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(self.run_stats, f, ensure_ascii=False, indent=2, default=str)
        logger.info(f"运行统计已保存到: {stats_file}")
    
    def print_summary(self, results: Dict[str, Any]):
        """
        打印摘要
        
        Args:
            results: 处理结果
        """
        runtime = datetime.now() - self.run_stats['start_time']
        
        print("\n" + "="*60)
        print("🤖 硅基流动索引生成器 - 运行摘要")
        print("="*60)
        
        print(f"⏱️  运行时间: {runtime}")
        print(f"📊 业务块数: {len(self.run_stats['business_blocks_processed'])}")
        print(f"📄 生成索引: {self.run_stats['generated_indexes']}")
        print(f"✅ 同步成功: {self.run_stats['synced_indexes']}")
        print(f"❌ 失败操作: {self.run_stats['failed_operations']}")
        
        if self.run_stats['generated_indexes'] > 0:
            success_rate = (self.run_stats['synced_indexes'] / self.run_stats['generated_indexes']) * 100
            print(f"📈 成功率: {success_rate:.1f}%")
        
        print(f"\n🏢 处理的业务块:")
        for block in self.run_stats['business_blocks_processed']:
            print(f"   • {block}")
        
        if self.run_stats['errors']:
            print(f"\n⚠️  错误信息:")
            for error in self.run_stats['errors'][-3:]:  # 只显示最后3个错误
                print(f"   • {error}")
        
        print("\n" + "="*60)
    
    async def run(self):
        """
        运行主流程
        """
        try:
            logger.info("🚀 启动硅基流动索引生成器")
            
            # 初始化组件
            await self.initialize_components()
            
            # 测试连接
            if not await self.test_connections():
                logger.error("连接测试失败，程序退出")
                return
            
            # 处理数据
            results = await self.process_sample_data()
            
            # 保存结果
            self.save_results(results)
            
            # 打印摘要
            self.print_summary(results)
            
            logger.info("✅ 索引生成器运行完成")
            
        except Exception as e:
            logger.error(f"运行失败: {str(e)}")
            self.run_stats['errors'].append(str(e))
            raise

def create_sample_config():
    """
    创建示例配置文件
    """
    config = {
        'siliconflow': {
            'api_key': 'sk-kmaipghbqavpzfnhpuuybpgrimcroynvsqlfkbnhcjcdulxj',
            'models': {
                'text': 'Qwen/Qwen2.5-72B-Instruct',
                'embedding': 'BAAI/bge-large-zh-v1.5'
            },
            'temperature': 0.3,
            'max_tokens': 2000
        },
        'fastgpt': {
            'api_url': 'https://api.fastgpt.in/api',
            'api_key': 'your-fastgpt-api-key',
            'dataset_id': 'your-dataset-id'
        },
        'processing': {
            'batch_size': 5,
            'delay_between_batches': 1.0,
            'max_retries': 3,
            'timeout': 30
        }
    }
    
    with open('config_sample.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("示例配置文件已创建: config_sample.json")
    print("请修改其中的API密钥和数据集ID")

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='硅基流动索引生成器')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--create-config', action='store_true', help='创建示例配置文件')
    parser.add_argument('--test-only', action='store_true', help='仅测试连接')
    
    args = parser.parse_args()
    
    if args.create_config:
        create_sample_config()
        return
    
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    os.makedirs('results', exist_ok=True)
    
    try:
        # 创建运行器
        runner = IndexGeneratorRunner(config_file=args.config)
        
        if args.test_only:
            # 仅测试连接
            async def test_connections():
                await runner.initialize_components()
                success = await runner.test_connections()
                if success:
                    print("✅ 所有连接测试成功")
                else:
                    print("❌ 连接测试失败")
                    sys.exit(1)
            
            asyncio.run(test_connections())
        else:
            # 运行完整流程
            asyncio.run(runner.run())
    
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()