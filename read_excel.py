#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取Excel文件内容的脚本
用于分析产品结构和集合数据
"""

import pandas as pd
import sys

def analyze_excel_files():
    """
    详细分析两个Excel文件的结构和内容
    """
    try:
        # 读取国内产品结构细化表
        print("=== 国内产品结构细化表.xlsx 详细分析 ===")
        df1 = pd.read_excel('D:/sync-db-fastgpt/国内产品结构细化表.xlsx')
        print(f"总行数: {len(df1)}")
        print(f"总列数: {len(df1.columns)}")
        print(f"列名: {list(df1.columns)}")
        print("\n各列数据类型:")
        print(df1.dtypes)
        print("\n各列非空值统计:")
        print(df1.count())
        
        # 分析产品分类
        print("\n产品分类统计:")
        if '产品分类' in df1.columns:
            print(df1['产品分类'].value_counts())
        
        # 分析类别
        print("\n类别统计:")
        if '类别' in df1.columns:
            print(df1['类别'].value_counts())
            
        # 分析收集情况
        print("\n收集情况统计:")
        if '收集情况' in df1.columns:
            print(df1['收集情况'].value_counts())
        
        print("\n前10行完整数据:")
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        print(df1.head(10))
        
        print("\n" + "="*80 + "\n")
        
        # 读取allcollections文件
        print("=== allcollections.xlsx 详细分析 ===")
        df2 = pd.read_excel('D:/sync-db-fastgpt/allcollections.xlsx')
        print(f"总行数: {len(df2)}")
        print(f"总列数: {len(df2.columns)}")
        print(f"列名: {list(df2.columns)}")
        print("\n各列数据类型:")
        print(df2.dtypes)
        print("\n各列非空值统计:")
        print(df2.count())
        
        # 分析文档类型
        print("\n文档类型统计(type):")
        if 'type' in df2.columns:
            print(df2['type'].value_counts())
        
        # 分析产品型号
        print("\n产品型号统计(cpxh):")
        if 'cpxh' in df2.columns:
            print(df2['cpxh'].value_counts().head(20))
            
        # 分析产品名称
        print("\n产品名称统计(cpmc):")
        if 'cpmc' in df2.columns:
            print(df2['cpmc'].value_counts().head(20))
        
        print("\n前10行完整数据:")
        print(df2.head(10))
        
        return df1, df2
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    analyze_excel_files()