# -*- coding: utf-8 -*-
"""
基于硅基流动最新模型的增强文档处理器

集成硅基流动的最新AI模型，提供更智能的文档处理、图片分析和内容优化功能。
主要特性：
1. 使用Qwen2-VL-72B进行图片相关性检测和描述生成
2. 使用Qwen2.5-72B进行内容优化和结构化处理
3. 智能成本控制和性能优化
4. 增强的质量评估和错误处理
"""

import os
import json
import time
import hashlib
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
from datetime import datetime
import base64
from PIL import Image
import io

# 导入配置
from config.siliconflow_config import SiliconFlowConfig
from enhanced_document_processor import DocumentInfo, EnhancedDocumentProcessor

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('siliconflow_processor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ProcessingStats:
    """
    处理统计信息
    """
    total_documents: int = 0
    processed_documents: int = 0
    total_images: int = 0
    relevant_images: int = 0
    removed_images: int = 0
    api_calls: int = 0
    total_tokens: int = 0
    total_cost: float = 0.0
    processing_time: float = 0.0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

@dataclass
class ImageAnalysisResult:
    """
    图片分析结果
    """
    is_relevant: bool
    relevance_score: float
    description: str
    keywords: List[str]
    confidence: float
    processing_time: float
    model_used: str

class SiliconFlowEnhancedProcessor(EnhancedDocumentProcessor):
    """
    基于硅基流动最新模型的增强文档处理器
    
    继承原有处理器功能，集成硅基流动的最新AI模型
    """
    
    def __init__(self, db_config: Dict[str, Any], api_config: Dict[str, Any], 
                 local_data_config: Dict[str, Any]):
        """
        初始化处理器
        
        Args:
            db_config: 数据库配置
            api_config: API配置
            local_data_config: 本地数据配置
        """
        super().__init__(db_config, api_config, local_data_config)
        
        # 初始化硅基流动配置
        self.sf_config = SiliconFlowConfig()
        
        # 验证配置
        if not self.sf_config.validate_config():
            raise ValueError("硅基流动配置无效，请检查API密钥和模型配置")
        
        # 初始化会话
        self.session = None
        
        # 处理统计
        self.stats = ProcessingStats()
        
        # 缓存
        self._cache = {}
        
        logger.info(f"硅基流动增强处理器初始化完成: {self.sf_config}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.sf_config.REQUEST_CONFIG['timeout']),
            headers=self.sf_config.get_headers()
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def _get_cache_key(self, content: str, task_type: str) -> str:
        """
        生成缓存键
        
        Args:
            content: 内容
            task_type: 任务类型
            
        Returns:
            缓存键
        """
        content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:16]
        return f"{self.sf_config.CACHE_CONFIG['cache_key_prefix']}{task_type}_{content_hash}"
    
    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """
        从缓存获取数据
        
        Args:
            cache_key: 缓存键
            
        Returns:
            缓存的数据或None
        """
        if not self.sf_config.CACHE_CONFIG['enable_cache']:
            return None
        
        cache_data = self._cache.get(cache_key)
        if cache_data:
            timestamp, data = cache_data
            if time.time() - timestamp < self.sf_config.CACHE_CONFIG['cache_ttl']:
                return data
            else:
                # 缓存过期，删除
                del self._cache[cache_key]
        
        return None
    
    def _set_cache(self, cache_key: str, data: Any) -> None:
        """
        设置缓存数据
        
        Args:
            cache_key: 缓存键
            data: 要缓存的数据
        """
        if not self.sf_config.CACHE_CONFIG['enable_cache']:
            return
        
        # 检查缓存大小限制
        max_size = self.sf_config.CACHE_CONFIG['max_cache_size']
        if len(self._cache) >= max_size:
            # 删除最旧的缓存项
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k][0])
            del self._cache[oldest_key]
        
        self._cache[cache_key] = (time.time(), data)
    
    def _encode_image_to_base64(self, image_path: str) -> Optional[str]:
        """
        将图片编码为base64格式
        
        Args:
            image_path: 图片路径
            
        Returns:
            base64编码的图片数据或None
        """
        try:
            # 检查文件大小
            file_size = os.path.getsize(image_path) / (1024 * 1024)  # MB
            if file_size > self.sf_config.QUALITY_CONFIG['max_image_size_mb']:
                logger.warning(f"图片文件过大: {image_path} ({file_size:.2f}MB)")
                return None
            
            # 检查文件格式
            file_ext = Path(image_path).suffix.lower().lstrip('.')
            if file_ext not in self.sf_config.QUALITY_CONFIG['supported_image_formats']:
                logger.warning(f"不支持的图片格式: {image_path} ({file_ext})")
                return None
            
            # 读取并压缩图片（如果需要）
            with Image.open(image_path) as img:
                # 转换为RGB（如果是RGBA）
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # 如果图片太大，进行压缩
                max_dimension = 1024
                if max(img.size) > max_dimension:
                    ratio = max_dimension / max(img.size)
                    new_size = tuple(int(dim * ratio) for dim in img.size)
                    img = img.resize(new_size, Image.Resampling.LANCZOS)
                
                # 转换为base64
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85, optimize=True)
                img_data = buffer.getvalue()
                
                return base64.b64encode(img_data).decode('utf-8')
        
        except Exception as e:
            logger.error(f"图片编码失败 {image_path}: {e}")
            return None
    
    async def _call_siliconflow_api(self, model_type: str, messages: List[Dict], 
                                   task_name: str = "") -> Optional[Dict]:
        """
        调用硅基流动API
        
        Args:
            model_type: 模型类型
            messages: 消息列表
            task_name: 任务名称（用于日志）
            
        Returns:
            API响应或None
        """
        if not self.session:
            logger.error("会话未初始化，请使用异步上下文管理器")
            return None
        
        try:
            # 获取模型配置
            model_config = self.sf_config.get_model_config(model_type)
            
            # 构建请求数据
            request_data = {
                "model": model_config['model_name'],
                "messages": messages,
                "max_tokens": model_config['max_tokens'],
                "temperature": model_config['temperature'],
                "top_p": model_config['top_p'],
                "stream": False
            }
            
            # 记录API调用
            start_time = time.time()
            self.stats.api_calls += 1
            
            if self.sf_config.LOGGING_CONFIG['log_api_calls']:
                logger.info(f"调用硅基流动API: {task_name} - 模型: {model_config['model_name']}")
            
            # 发送请求
            url = f"{self.sf_config.API_BASE_URL}/chat/completions"
            async with self.session.post(url, json=request_data) as response:
                response_time = time.time() - start_time
                
                if self.sf_config.LOGGING_CONFIG['log_response_time']:
                    logger.info(f"API响应时间: {response_time:.2f}秒")
                
                if response.status == 200:
                    result = await response.json()
                    
                    # 记录token使用情况
                    if 'usage' in result and self.sf_config.LOGGING_CONFIG['log_token_usage']:
                        usage = result['usage']
                        total_tokens = usage.get('total_tokens', 0)
                        self.stats.total_tokens += total_tokens
                        
                        # 计算成本
                        cost = self.sf_config.get_cost_estimate(model_type, total_tokens)
                        self.stats.total_cost += cost
                        
                        logger.info(f"Token使用: {total_tokens}, 成本: ${cost:.4f}")
                    
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"API调用失败: {response.status} - {error_text}")
                    self.stats.errors.append(f"API错误: {response.status} - {error_text}")
                    return None
        
        except Exception as e:
            logger.error(f"API调用异常: {e}")
            self.stats.errors.append(f"API异常: {str(e)}")
            return None
    
    async def analyze_image_relevance_advanced(self, image_path: str, 
                                             document_content: str) -> ImageAnalysisResult:
        """
        使用硅基流动最新模型进行高级图片相关性分析
        
        Args:
            image_path: 图片路径
            document_content: 文档内容
            
        Returns:
            图片分析结果
        """
        start_time = time.time()
        
        # 检查缓存
        cache_key = self._get_cache_key(f"{image_path}_{document_content[:500]}", "image_relevance")
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            logger.info(f"使用缓存的图片分析结果: {image_path}")
            return ImageAnalysisResult(**cached_result)
        
        try:
            # 编码图片
            image_base64 = self._encode_image_to_base64(image_path)
            if not image_base64:
                return ImageAnalysisResult(
                    is_relevant=False,
                    relevance_score=0.0,
                    description="图片编码失败",
                    keywords=[],
                    confidence=0.0,
                    processing_time=time.time() - start_time,
                    model_used="none"
                )
            
            # 构建消息
            messages = [
                {
                    "role": "system",
                    "content": """你是一个专业的图片内容分析专家。请分析图片与给定文档内容的相关性。

分析要求：
1. 评估图片内容与文档主题的相关程度（0-10分）
2. 判断是否应该保留这张图片（true/false）
3. 生成图片的详细描述
4. 提取图片中的关键词
5. 给出分析的置信度（0-1）

请以JSON格式返回结果：
{
  "relevance_score": 8.5,
  "is_relevant": true,
  "description": "图片的详细描述",
  "keywords": ["关键词1", "关键词2"],
  "confidence": 0.95,
  "reasoning": "分析推理过程"
}"""
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"""请分析这张图片与以下文档内容的相关性：

文档内容摘要：
{document_content[:2000]}...

请根据图片内容和文档主题，给出详细的相关性分析。"""
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
            
            # 调用API
            response = await self._call_siliconflow_api(
                'vision', messages, f"图片相关性分析: {Path(image_path).name}"
            )
            
            if response and 'choices' in response:
                content = response['choices'][0]['message']['content']
                
                try:
                    # 解析JSON结果
                    result_data = json.loads(content)
                    
                    # 创建结果对象
                    result = ImageAnalysisResult(
                        is_relevant=result_data.get('is_relevant', False),
                        relevance_score=float(result_data.get('relevance_score', 0.0)),
                        description=result_data.get('description', ''),
                        keywords=result_data.get('keywords', []),
                        confidence=float(result_data.get('confidence', 0.0)),
                        processing_time=time.time() - start_time,
                        model_used=self.sf_config.MODELS['vision']['model_name']
                    )
                    
                    # 应用质量控制
                    min_confidence = self.sf_config.QUALITY_CONFIG['min_confidence_score']
                    if result.confidence < min_confidence:
                        logger.warning(f"图片分析置信度过低: {result.confidence} < {min_confidence}")
                        result.is_relevant = False
                    
                    # 缓存结果
                    self._set_cache(cache_key, asdict(result))
                    
                    return result
                
                except json.JSONDecodeError as e:
                    logger.error(f"解析API响应失败: {e}")
                    logger.debug(f"原始响应: {content}")
            
            # 如果API调用失败，返回默认结果
            return ImageAnalysisResult(
                is_relevant=False,
                relevance_score=0.0,
                description="API分析失败",
                keywords=[],
                confidence=0.0,
                processing_time=time.time() - start_time,
                model_used="none"
            )
        
        except Exception as e:
            logger.error(f"图片相关性分析异常: {e}")
            return ImageAnalysisResult(
                is_relevant=False,
                relevance_score=0.0,
                description=f"分析异常: {str(e)}",
                keywords=[],
                confidence=0.0,
                processing_time=time.time() - start_time,
                model_used="none"
            )
    
    async def optimize_content_structure(self, content: str, 
                                       content_type: str = "markdown") -> str:
        """
        使用硅基流动模型优化内容结构
        
        Args:
            content: 原始内容
            content_type: 内容类型
            
        Returns:
            优化后的内容
        """
        # 检查内容长度
        if len(content) > self.sf_config.QUALITY_CONFIG['max_content_length']:
            logger.warning(f"内容过长，将进行分段处理: {len(content)} 字符")
            return await self._optimize_long_content(content, content_type)
        
        # 检查缓存
        cache_key = self._get_cache_key(content, "content_optimization")
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            logger.info("使用缓存的内容优化结果")
            return cached_result
        
        try:
            messages = [
                {
                    "role": "system",
                    "content": f"""你是一个专业的文档内容优化专家。请优化以下{content_type}格式的内容：

优化要求：
1. 保持原有信息的完整性和准确性
2. 改善内容结构和可读性
3. 优化标题层级和段落组织
4. 增强关键信息的突出显示
5. 保持原有的格式标记
6. 添加适当的目录结构（如果内容较长）
7. 确保专业术语的一致性

请直接返回优化后的内容，不要添加额外的说明。"""
                },
                {
                    "role": "user",
                    "content": f"请优化以下内容：\n\n{content}"
                }
            ]
            
            response = await self._call_siliconflow_api(
                'text', messages, "内容结构优化"
            )
            
            if response and 'choices' in response:
                optimized_content = response['choices'][0]['message']['content']
                
                # 缓存结果
                self._set_cache(cache_key, optimized_content)
                
                return optimized_content
            
            return content  # 如果优化失败，返回原内容
        
        except Exception as e:
            logger.error(f"内容优化异常: {e}")
            return content
    
    async def _optimize_long_content(self, content: str, content_type: str) -> str:
        """
        优化长内容（分段处理）
        
        Args:
            content: 长内容
            content_type: 内容类型
            
        Returns:
            优化后的内容
        """
        # 将内容分段
        max_chunk_size = self.sf_config.QUALITY_CONFIG['max_content_length'] // 2
        chunks = []
        
        # 按段落分割
        paragraphs = content.split('\n\n')
        current_chunk = ""
        
        for paragraph in paragraphs:
            if len(current_chunk) + len(paragraph) < max_chunk_size:
                current_chunk += paragraph + "\n\n"
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = paragraph + "\n\n"
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        # 优化每个分段
        optimized_chunks = []
        for i, chunk in enumerate(chunks):
            logger.info(f"优化内容分段 {i+1}/{len(chunks)}")
            optimized_chunk = await self.optimize_content_structure(chunk, content_type)
            optimized_chunks.append(optimized_chunk)
            
            # 添加延迟以避免API限制
            if i < len(chunks) - 1:
                await asyncio.sleep(1)
        
        return "\n\n".join(optimized_chunks)
    
    async def generate_qa_pairs(self, content: str, num_pairs: int = 5) -> List[Dict[str, str]]:
        """
        基于内容生成问答对
        
        Args:
            content: 文档内容
            num_pairs: 生成的问答对数量
            
        Returns:
            问答对列表
        """
        try:
            messages = [
                {
                    "role": "system",
                    "content": f"""你是一个专业的问答生成专家。请基于给定的文档内容生成{num_pairs}个高质量的问答对。

要求：
1. 问题应该涵盖文档的关键信息点
2. 答案应该准确、完整且易于理解
3. 问题类型要多样化（事实性、概念性、应用性等）
4. 确保问答对有助于知识库的检索和理解

请以JSON格式返回：
{
  "qa_pairs": [
    {
      "question": "问题1",
      "answer": "答案1"
    },
    ...
  ]
}"""
                },
                {
                    "role": "user",
                    "content": f"请基于以下内容生成问答对：\n\n{content[:3000]}..."
                }
            ]
            
            response = await self._call_siliconflow_api(
                'text', messages, "问答对生成"
            )
            
            if response and 'choices' in response:
                content_result = response['choices'][0]['message']['content']
                
                try:
                    result_data = json.loads(content_result)
                    return result_data.get('qa_pairs', [])
                except json.JSONDecodeError:
                    logger.error("解析问答对JSON失败")
                    return []
            
            return []
        
        except Exception as e:
            logger.error(f"生成问答对异常: {e}")
            return []
    
    async def process_document_enhanced(self, doc_info: DocumentInfo) -> Dict[str, Any]:
        """
        增强的文档处理方法
        
        Args:
            doc_info: 文档信息
            
        Returns:
            处理结果
        """
        start_time = time.time()
        self.stats.total_documents += 1
        
        try:
            logger.info(f"开始增强处理文档: {doc_info.file_path}")
            
            # 1. 基础文档处理（使用父类方法）
            base_result = await asyncio.to_thread(
                self.process_single_document, doc_info.product_name
            )
            
            if not base_result:
                logger.error(f"基础文档处理失败: {doc_info.file_path}")
                return None
            
            # 2. 内容结构优化
            logger.info("开始内容结构优化...")
            optimized_content = await self.optimize_content_structure(
                base_result.get('content', ''), 'markdown'
            )
            
            # 3. 高级图片分析
            image_analysis_results = []
            images = base_result.get('images', [])
            self.stats.total_images += len(images)
            
            if images:
                logger.info(f"开始高级图片分析，共{len(images)}张图片...")
                
                for image_info in images:
                    image_path = image_info.get('path', '')
                    if os.path.exists(image_path):
                        analysis_result = await self.analyze_image_relevance_advanced(
                            image_path, optimized_content
                        )
                        image_analysis_results.append({
                            'image_info': image_info,
                            'analysis': asdict(analysis_result)
                        })
                        
                        if analysis_result.is_relevant:
                            self.stats.relevant_images += 1
                        else:
                            self.stats.removed_images += 1
            
            # 4. 生成问答对
            logger.info("生成问答对...")
            qa_pairs = await self.generate_qa_pairs(optimized_content, 5)
            
            # 5. 构建增强结果
            enhanced_result = {
                **base_result,
                'content': optimized_content,
                'image_analysis': image_analysis_results,
                'qa_pairs': qa_pairs,
                'processing_stats': {
                    'processing_time': time.time() - start_time,
                    'api_calls': self.stats.api_calls,
                    'total_tokens': self.stats.total_tokens,
                    'total_cost': self.stats.total_cost,
                    'model_versions': {
                        'vision': self.sf_config.MODELS['vision']['model_name'],
                        'text': self.sf_config.MODELS['text']['model_name']
                    }
                },
                'quality_metrics': {
                    'content_length': len(optimized_content),
                    'image_relevance_rate': (self.stats.relevant_images / max(self.stats.total_images, 1)) * 100,
                    'qa_pairs_count': len(qa_pairs)
                }
            }
            
            self.stats.processed_documents += 1
            self.stats.processing_time += time.time() - start_time
            
            logger.info(f"文档增强处理完成: {doc_info.file_path}")
            return enhanced_result
        
        except Exception as e:
            logger.error(f"文档增强处理异常: {e}")
            self.stats.errors.append(f"文档处理异常: {str(e)}")
            return None
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """
        获取处理摘要统计
        
        Returns:
            处理摘要
        """
        return {
            'statistics': asdict(self.stats),
            'performance_metrics': {
                'avg_processing_time': self.stats.processing_time / max(self.stats.processed_documents, 1),
                'success_rate': (self.stats.processed_documents / max(self.stats.total_documents, 1)) * 100,
                'image_relevance_rate': (self.stats.relevant_images / max(self.stats.total_images, 1)) * 100,
                'cost_per_document': self.stats.total_cost / max(self.stats.processed_documents, 1)
            },
            'model_info': {
                'vision_model': self.sf_config.MODELS['vision']['model_name'],
                'text_model': self.sf_config.MODELS['text']['model_name'],
                'api_base': self.sf_config.API_BASE_URL
            },
            'cache_stats': {
                'cache_size': len(self._cache),
                'cache_enabled': self.sf_config.CACHE_CONFIG['enable_cache']
            }
        }

# 使用示例
async def main():
    """
    主函数示例
    """
    # 配置信息
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'password',
        'database': 'fastgpt_sync'
    }
    
    api_config = {
        'textin_api_key': 'your_textin_api_key'
    }
    
    local_data_config = {
        'base_path': './data',
        'download_path': './downloads'
    }
    
    # 创建处理器实例
    async with SiliconFlowEnhancedProcessor(db_config, api_config, local_data_config) as processor:
        # 处理单个文档
        doc_info = DocumentInfo(
            product_name="示例产品",
            file_path="./data/example.pdf",
            file_format="pdf",
            file_hash="example_hash",
            priority=1
        )
        
        result = await processor.process_document_enhanced(doc_info)
        
        if result:
            print("文档处理成功！")
            print(f"内容长度: {len(result['content'])}")
            print(f"图片分析: {len(result['image_analysis'])}")
            print(f"问答对: {len(result['qa_pairs'])}")
        
        # 获取处理摘要
        summary = processor.get_processing_summary()
        print("\n处理摘要:")
        print(json.dumps(summary, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    asyncio.run(main())