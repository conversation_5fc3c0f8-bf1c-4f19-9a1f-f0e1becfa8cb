# -*- coding: utf-8 -*-
"""
统一数据库管理模块
提供数据库连接、初始化、操作等功能
"""

import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2.pool import ThreadedConnectionPool
import contextlib
from typing import Dict, List, Any, Optional, Union
import json
from datetime import datetime
from unified_config import config_manager

logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.config = config_manager.get_database_config()
        self.pool = None
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        try:
            self.pool = ThreadedConnectionPool(
                minconn=1,
                maxconn=20,
                **self.config
            )
            logger.info("数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise
    
    @contextlib.contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = None
        try:
            conn = self.pool.getconn()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if conn:
                self.pool.putconn(conn)
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    return result[0] == 1
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False
    
    def initialize_database(self) -> bool:
        """初始化数据库表结构"""
        logger.info("开始初始化数据库表结构")
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 创建表
                    self._create_tables(cursor)
                    
                    # 创建索引
                    self._create_indexes(cursor)
                    
                    # 插入初始数据
                    self._insert_initial_data(cursor)
                    
                    conn.commit()
                    logger.info("数据库初始化完成")
                    return True
                    
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            return False
    
    def _create_tables(self, cursor):
        """创建数据库表"""
        
        # 创建产品分类表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ProductCategory (
                category_id SERIAL PRIMARY KEY,
                category_name VARCHAR(100) NOT NULL,
                parent_id INTEGER REFERENCES ProductCategory(category_id),
                category_level INTEGER DEFAULT 1,
                sort_order INTEGER DEFAULT 0,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建产品主表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS Product (
                product_id VARCHAR(50) PRIMARY KEY,
                product_category VARCHAR(50) NOT NULL,
                product_type VARCHAR(50) NOT NULL,
                product_series VARCHAR(50),
                product_model VARCHAR(100) NOT NULL,
                product_name VARCHAR(200),
                description TEXT,
                introduction TEXT,
                small_img VARCHAR(500),
                banner VARCHAR(500),
                attribute VARCHAR(100),
                label VARCHAR(100),
                show_for TEXT,
                use_to TEXT,
                price DECIMAL(10,2) DEFAULT 0.00,
                like_count INTEGER DEFAULT 0,
                favorite_count INTEGER DEFAULT 0,
                view_count INTEGER DEFAULT 0,
                is_suggest BOOLEAN DEFAULT FALSE,
                guide TEXT,
                details TEXT,
                other_attachments TEXT,
                site_id INTEGER,
                fastgpt_id VARCHAR(100),
                document_name VARCHAR(500),
                material_usage VARCHAR(50),
                business_scope VARCHAR(255),
                launch_time TIMESTAMP,
                supported_software TEXT,
                collection_status VARCHAR(50) DEFAULT '未开始',
                external_id INTEGER,
                sync_status VARCHAR(20) DEFAULT 'pending',
                last_sync_time TIMESTAMP,
                data_source VARCHAR(20) DEFAULT 'manual',
                status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建资料类型表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS DocumentType (
                type_id SERIAL PRIMARY KEY,
                type_name VARCHAR(100) NOT NULL,
                type_code VARCHAR(50) UNIQUE NOT NULL,
                description TEXT,
                is_required BOOLEAN DEFAULT FALSE,
                sort_order INTEGER DEFAULT 0,
                document_category VARCHAR(50),
                priority INTEGER DEFAULT 0,
                template_path VARCHAR(500),
                file_extensions VARCHAR(255),
                max_file_size BIGINT,
                quality_requirements JSONB,
                review_required BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建产品资料表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ProductDocument (
                document_id VARCHAR(50) PRIMARY KEY,
                product_id VARCHAR(50) NOT NULL,
                type_id INTEGER,
                document_name VARCHAR(500) NOT NULL,
                file_path VARCHAR(1000),
                file_size BIGINT,
                file_format VARCHAR(20),
                file_extension VARCHAR(10),
                file_type VARCHAR(50),
                mime_type VARCHAR(100),
                file_hash VARCHAR(64),
                version VARCHAR(50),
                language VARCHAR(10) DEFAULT 'zh-CN',
                business_unit VARCHAR(100),
                consultation_type VARCHAR(50),
                upload_date TIMESTAMP,
                fastgpt_id VARCHAR(100),
                original_filename VARCHAR(500),
                material_usage VARCHAR(50),
                business_scope VARCHAR(255),
                collection_status VARCHAR(50) DEFAULT '未收集',
                collector VARCHAR(100),
                collection_date TIMESTAMP,
                review_status VARCHAR(50),
                reviewer VARCHAR(100),
                review_date TIMESTAMP,
                review_comments TEXT,
                data_source VARCHAR(20) DEFAULT 'manual',
                is_deprecated BOOLEAN DEFAULT FALSE,
                local_file_path VARCHAR(1000),
                relative_path VARCHAR(500),
                metadata JSONB,
                status VARCHAR(20) DEFAULT 'draft',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES Product(product_id),
                FOREIGN KEY (type_id) REFERENCES DocumentType(type_id)
            )
        """)
        
        # 创建软件关联表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ProductSoftware (
                id SERIAL PRIMARY KEY,
                product_id VARCHAR(50) NOT NULL,
                software_name VARCHAR(100) NOT NULL,
                software_version VARCHAR(50),
                compatibility_level VARCHAR(20) DEFAULT 'full',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES Product(product_id)
            )
        """)
        
        # 创建同步状态表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sync_status (
                id SERIAL PRIMARY KEY,
                source_type VARCHAR(50) NOT NULL,
                source_id VARCHAR(255) NOT NULL,
                content_hash VARCHAR(64) NOT NULL,
                sync_status VARCHAR(20) DEFAULT 'pending',
                fastgpt_data_id VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                approved_at TIMESTAMP,
                approved_by VARCHAR(100),
                sync_error TEXT,
                UNIQUE(source_type, source_id)
            )
        """)
        
        # 创建待同步数据表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS pending_sync_data (
                id SERIAL PRIMARY KEY,
                source_type VARCHAR(50) NOT NULL,
                source_id VARCHAR(255) NOT NULL,
                title VARCHAR(500) NOT NULL,
                content TEXT NOT NULL,
                metadata JSONB,
                content_hash VARCHAR(64) NOT NULL,
                change_type VARCHAR(20) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(source_type, source_id)
            )
        """)
        
        # 创建同步日志表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sync_logs (
                id SERIAL PRIMARY KEY,
                source_type VARCHAR(50) NOT NULL,
                source_id VARCHAR(255) NOT NULL,
                action VARCHAR(50) NOT NULL,
                details JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建处理任务表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS processing_tasks (
                task_id VARCHAR(50) PRIMARY KEY,
                task_type VARCHAR(50) NOT NULL,
                task_name VARCHAR(200) NOT NULL,
                task_status VARCHAR(20) DEFAULT 'pending',
                progress INTEGER DEFAULT 0,
                total_items INTEGER DEFAULT 0,
                processed_items INTEGER DEFAULT 0,
                failed_items INTEGER DEFAULT 0,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                error_message TEXT,
                task_config JSONB,
                created_by VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        logger.info("数据库表创建完成")
    
    def _create_indexes(self, cursor):
        """创建数据库索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_product_category ON Product(product_category)",
            "CREATE INDEX IF NOT EXISTS idx_product_model ON Product(product_model)",
            "CREATE INDEX IF NOT EXISTS idx_product_fastgpt_id ON Product(fastgpt_id)",
            "CREATE INDEX IF NOT EXISTS idx_product_data_source ON Product(data_source)",
            "CREATE INDEX IF NOT EXISTS idx_document_product_id ON ProductDocument(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_document_fastgpt_id ON ProductDocument(fastgpt_id)",
            "CREATE INDEX IF NOT EXISTS idx_document_data_source ON ProductDocument(data_source)",
            "CREATE INDEX IF NOT EXISTS idx_document_metadata ON ProductDocument USING GIN(metadata)",
            "CREATE INDEX IF NOT EXISTS idx_sync_status_source ON sync_status(source_type, source_id)",
            "CREATE INDEX IF NOT EXISTS idx_pending_sync_source ON pending_sync_data(source_type, source_id)",
            "CREATE INDEX IF NOT EXISTS idx_sync_logs_source ON sync_logs(source_type, source_id)",
            "CREATE INDEX IF NOT EXISTS idx_processing_tasks_status ON processing_tasks(task_status)",
            "CREATE INDEX IF NOT EXISTS idx_processing_tasks_type ON processing_tasks(task_type)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        logger.info("数据库索引创建完成")
    
    def _insert_initial_data(self, cursor):
        """插入初始数据"""
        # 插入文档类型
        document_types = [
            ('brochure', '宣传彩页', '产品宣传资料', True, '彩页'),
            ('quick_guide', '入门指南', '产品快速入门指南', True, '入门指南'),
            ('user_manual', '用户手册', '产品详细使用手册', True, '用户手册'),
            ('tech_spec', '技术规格书', '产品技术规格说明', True, '技术文档'),
            ('install_guide', '安装指南', '产品安装说明', False, '技术文档'),
            ('troubleshooting', '故障排除指南', '常见问题解决方案', False, '技术文档')
        ]
        
        for type_code, type_name, description, is_required, category in document_types:
            cursor.execute("""
                INSERT INTO DocumentType (type_code, type_name, description, is_required, document_category)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (type_code) DO NOTHING
            """, (type_code, type_name, description, is_required, category))
        
        logger.info("初始数据插入完成")
    
    def execute_query(self, sql: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """执行查询"""
        try:
            with self.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute(sql, params)
                    return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            return []
    
    def execute_update(self, sql: str, params: Optional[tuple] = None) -> bool:
        """执行更新操作"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    conn.commit()
                    return True
        except Exception as e:
            logger.error(f"更新执行失败: {e}")
            return False
    
    def get_table_stats(self) -> Dict[str, int]:
        """获取表统计信息"""
        tables = [
            'Product', 'ProductDocument', 'ProductCategory', 
            'DocumentType', 'ProductSoftware', 'sync_status',
            'pending_sync_data', 'sync_logs', 'processing_tasks'
        ]
        
        stats = {}
        for table in tables:
            try:
                result = self.execute_query(f"SELECT COUNT(*) as count FROM {table}")
                stats[table] = result[0]['count'] if result else 0
            except:
                stats[table] = 0
        
        return stats
    
    def close(self):
        """关闭连接池"""
        if self.pool:
            self.pool.closeall()
            logger.info("数据库连接池已关闭")

# 全局数据库管理器实例
db_manager = DatabaseManager()
