# -*- coding: utf-8 -*-
"""
统一的产品知识库数据处理系统 - Streamlit应用
整合所有功能模块，提供统一的Web界面
"""

import streamlit as st
import asyncio
import logging
import traceback
from pathlib import Path
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 导入配置和模块
try:
    from unified_config import config_manager
    logging.info("统一配置加载成功")
except ImportError as e:
    st.error(f"❌ 配置模块加载失败: {e}")
    st.error("请确保 unified_config.py 文件存在且配置正确")
    st.info("💡 **解决方案**: 检查项目目录中是否存在 unified_config.py 文件")
    st.stop()
except Exception as e:
    st.error(f"❌ 配置初始化失败: {e}")
    st.error("配置文件可能存在格式错误或权限问题")
    st.info("💡 **解决方案**: 检查 unified_config.yaml 文件格式和权限")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="产品知识库数据处理系统",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;
        margin: 1rem 0;
    }
    .success-card {
        background: #d4edda;
        border-left-color: #28a745;
    }
    .warning-card {
        background: #fff3cd;
        border-left-color: #ffc107;
    }
    .error-card {
        background: #f8d7da;
        border-left-color: #dc3545;
    }
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)

class UnifiedApp:
    """统一应用主类"""

    def __init__(self):
        try:
            self.config = config_manager
            self.setup_session_state()
            self.initialized = True
        except Exception as e:
            st.error(f"❌ 应用初始化失败: {e}")
            st.error("请检查配置文件和依赖项")
            self.initialized = False
            raise
        
    def setup_session_state(self):
        """初始化会话状态"""
        if 'initialized' not in st.session_state:
            st.session_state.initialized = True
            st.session_state.current_task = None
            st.session_state.task_progress = 0
            st.session_state.logs = []
            st.session_state.processing_stats = {}

            # 统一状态管理
            st.session_state.system_status = {
                'database_connected': False,
                'api_configured': False,
                'ai_models_ready': False,
                'last_sync_time': None,
                'total_records': 0,
                'pending_sync': 0,
                'sync_errors': 0
            }

            # 数据流状态
            st.session_state.data_flow = {
                'data_processing_completed': False,
                'ai_enhancement_completed': False,
                'sync_ready': False,
                'last_processing_time': None,
                'processing_results': None
            }

            # 实时统计
            st.session_state.real_time_stats = {
                'yunshang_count': 0,
                'local_count': 0,
                'legacy_count': 0,
                'ai_processed_count': 0,
                'synced_count': 0,
                'error_count': 0
            }

    def update_system_status(self):
        """更新系统状态"""
        try:
            # 检查数据库连接
            st.session_state.system_status['database_connected'] = self._check_database_connection()

            # 检查API配置
            st.session_state.system_status['api_configured'] = self._check_api_configuration()

            # 检查AI模型状态
            st.session_state.system_status['ai_models_ready'] = self._check_ai_models()

            # 获取数据库统计
            stats = self._get_database_stats()
            st.session_state.system_status.update(stats)

        except Exception as e:
            logging.error(f"更新系统状态失败: {e}")

    def _check_database_connection(self):
        """检查数据库连接"""
        try:
            # 这里应该实现实际的数据库连接检查
            validation_results = self.config.validate_config()
            return validation_results.get('database', False)
        except:
            return False

    def _check_api_configuration(self):
        """检查API配置"""
        try:
            # 检查必要的API配置
            return (hasattr(self.config, 'siliconflow') and
                   hasattr(self.config, 'fastgpt') and
                   hasattr(self.config, 'api'))
        except:
            return False

    def _check_ai_models(self):
        """检查AI模型状态"""
        try:
            # 检查硅基流动模型配置
            return (hasattr(self.config, 'siliconflow') and
                   hasattr(self.config.siliconflow, 'models') and
                   len(self.config.siliconflow.models) > 0)
        except:
            return False

    def _get_database_stats(self):
        """获取数据库统计信息"""
        try:
            # 这里应该实现实际的数据库查询
            # 模拟返回统计数据
            return {
                'total_records': st.session_state.real_time_stats.get('yunshang_count', 0) +
                               st.session_state.real_time_stats.get('local_count', 0) +
                               st.session_state.real_time_stats.get('legacy_count', 0),
                'pending_sync': 0,  # 待同步数量
                'sync_errors': st.session_state.real_time_stats.get('error_count', 0),
                'last_sync_time': st.session_state.data_flow.get('last_processing_time')
            }
        except Exception as e:
            logging.error(f"获取数据库统计失败: {e}")
            return {
                'total_records': 0,
                'pending_sync': 0,
                'sync_errors': 0,
                'last_sync_time': None
            }
            
    def render_header(self):
        """渲染页面头部"""
        st.markdown("""
        <div class="main-header">
            <h1>🤖 产品知识库数据处理系统</h1>
            <p>基于硅基流动大模型的智能知识库索引生成与FastGPT同步系统</p>
        </div>
        """, unsafe_allow_html=True)
    
    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.title("🔧 系统控制")
            
            # 系统状态
            st.subheader("📊 系统状态")
            self.render_system_status()
            
            st.divider()
            
            # 快速操作
            st.subheader("⚡ 快速操作")
            
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🔄 刷新状态", use_container_width=True, key="sidebar_refresh_status"):
                    st.rerun()

            with col2:
                if st.button("🧹 清理缓存", use_container_width=True, key="sidebar_clear_cache"):
                    self.clear_cache()
            
            st.divider()
            
            # 配置管理
            st.subheader("⚙️ 配置管理")
            if st.button("📝 编辑配置", use_container_width=True, key="sidebar_edit_config"):
                st.session_state.show_config_editor = True

            if st.button("💾 保存配置", use_container_width=True, key="sidebar_save_config"):
                if self.config.save_config():
                    st.success("配置保存成功")
                else:
                    st.error("配置保存失败")
    
    def render_system_status(self):
        """渲染系统状态"""
        validation_results = self.config.validate_config()
        
        for service, is_valid in validation_results.items():
            if is_valid:
                st.success(f"✅ {service}")
            else:
                st.error(f"❌ {service}")
    
    def clear_cache(self):
        """清理缓存"""
        try:
            # 清理Streamlit缓存
            st.cache_data.clear()
            st.cache_resource.clear()
            
            # 清理会话状态中的缓存数据
            cache_keys = [key for key in st.session_state.keys() if key.startswith('cache_')]
            for key in cache_keys:
                del st.session_state[key]
            
            st.success("缓存清理完成")
        except Exception as e:
            st.error(f"缓存清理失败: {e}")
    
    def render_main_tabs(self):
        """渲染主要标签页"""
        tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
            "🏠 仪表板", "📊 数据处理", "🔄 同步管理", 
            "🤖 AI增强", "⚙️ 配置管理", "📋 日志监控"
        ])
        
        with tab1:
            self.render_dashboard()
        
        with tab2:
            self.render_data_processing()
        
        with tab3:
            self.render_sync_management()
        
        with tab4:
            self.render_ai_enhancement()
        
        with tab5:
            self.render_config_management()
        
        with tab6:
            self.render_log_monitoring()
    
    def render_dashboard(self):
        """渲染仪表板"""
        st.header("📊 系统仪表板")

        # 更新系统状态
        self.update_system_status()

        # 系统概览 - 使用实时数据
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_records = st.session_state.system_status.get('total_records', 0)
            st.metric(
                "📁 总数据量",
                f"{total_records:,}",
                delta=f"+{st.session_state.real_time_stats.get('yunshang_count', 0) + st.session_state.real_time_stats.get('local_count', 0) + st.session_state.real_time_stats.get('legacy_count', 0)}" if total_records > 0 else None
            )
            st.caption("云商API + 本地文件 + 历史数据")

        with col2:
            pending_sync = st.session_state.system_status.get('pending_sync', 0)
            sync_status = "✅ 已同步" if pending_sync == 0 else f"⏳ 待同步 {pending_sync}"
            st.metric(
                "🔄 同步状态",
                sync_status,
                delta=f"错误: {st.session_state.system_status.get('sync_errors', 0)}" if st.session_state.system_status.get('sync_errors', 0) > 0 else None
            )
            last_sync = st.session_state.system_status.get('last_sync_time')
            st.caption(f"最后同步: {last_sync if last_sync else '从未同步'}")

        with col3:
            ai_status = "🟢 就绪" if st.session_state.system_status.get('ai_models_ready', False) else "🔴 未配置"
            ai_processed = st.session_state.real_time_stats.get('ai_processed_count', 0)
            st.metric(
                "🤖 AI模型",
                ai_status,
                delta=f"已处理: {ai_processed}" if ai_processed > 0 else None
            )
            st.caption("视觉 + 文本 + 代码 + 轻量级")

        with col4:
            current_task = st.session_state.get('current_task')
            if current_task:
                progress = int(st.session_state.get('task_progress', 0) * 100)
                st.metric("📈 处理进度", f"{progress}%", delta=current_task)
            else:
                st.metric("📈 处理进度", "空闲", delta="等待任务")
            st.caption("实时任务状态")
        
        st.divider()

        # 系统状态检查
        st.subheader("🔧 系统状态检查")

        col1, col2, col3 = st.columns(3)

        with col1:
            db_status = "🟢 已连接" if st.session_state.system_status.get('database_connected', False) else "🔴 未连接"
            st.markdown(f"**数据库**: {db_status}")

        with col2:
            api_status = "🟢 已配置" if st.session_state.system_status.get('api_configured', False) else "🔴 未配置"
            st.markdown(f"**API配置**: {api_status}")

        with col3:
            ai_status = "🟢 就绪" if st.session_state.system_status.get('ai_models_ready', False) else "🔴 未就绪"
            st.markdown(f"**AI模型**: {ai_status}")

        st.divider()

        # 智能工作流程指导
        st.subheader("🚀 智能工作流程")

        # 根据当前状态提供个性化建议
        self._render_workflow_guidance()

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("""
            ### 📋 标准流程
            1. **配置检查**: 确保所有配置项正确
            2. **数据库初始化**: 创建必要的表结构
            3. **数据导入**: 导入历史数据和云商数据
            4. **AI增强处理**: 使用AI模型优化内容
            5. **同步到FastGPT**: 将处理结果同步到知识库
            """)

        with col2:
            st.markdown("""
            ### ⚠️ 注意事项
            - 确保数据库连接正常
            - 检查API密钥配置
            - 确认文件路径存在
            - 监控处理进度和日志
            - 定期备份重要数据
            """)
        
        # 系统健康检查
        st.subheader("🏥 系统健康检查")
        if st.button("🔍 执行健康检查", type="primary", key="dashboard_health_check"):
            self.run_health_check()

    def _render_workflow_guidance(self):
        """渲染智能工作流程指导"""
        # 检查当前状态并提供个性化建议
        db_connected = st.session_state.system_status.get('database_connected', False)
        api_configured = st.session_state.system_status.get('api_configured', False)
        ai_ready = st.session_state.system_status.get('ai_models_ready', False)

        data_processed = st.session_state.data_flow.get('data_processing_completed', False)
        ai_enhanced = st.session_state.data_flow.get('ai_enhancement_completed', False)
        sync_ready = st.session_state.data_flow.get('sync_ready', False)

        # 根据状态提供建议
        if not db_connected:
            st.warning("🔧 **下一步建议**: 请先在 '⚙️ 配置管理' 页面配置数据库连接")
        elif not api_configured:
            st.warning("🔧 **下一步建议**: 请在 '⚙️ 配置管理' 页面配置API服务")
        elif not data_processed:
            st.info("📊 **下一步建议**: 前往 '📊 数据处理' 页面开始数据导入和处理")
        elif not ai_enhanced and ai_ready:
            st.info("🤖 **下一步建议**: 前往 '🤖 AI增强' 页面对数据进行AI优化")
        elif not sync_ready:
            st.info("🔄 **下一步建议**: 前往 '🔄 同步管理' 页面检测变更并同步到FastGPT")
        else:
            st.success("✅ **系统状态**: 所有流程已完成，系统运行正常")

        # 显示当前数据流状态
        if data_processed or ai_enhanced or sync_ready:
            st.markdown("### 📈 数据流状态")

            col1, col2, col3 = st.columns(3)

            with col1:
                status = "✅" if data_processed else "⏳"
                st.markdown(f"{status} **数据处理**")
                if data_processed:
                    total = (st.session_state.real_time_stats.get('yunshang_count', 0) +
                           st.session_state.real_time_stats.get('local_count', 0) +
                           st.session_state.real_time_stats.get('legacy_count', 0))
                    st.caption(f"已处理 {total} 条记录")

            with col2:
                status = "✅" if ai_enhanced else "⏳"
                st.markdown(f"{status} **AI增强**")
                if ai_enhanced:
                    ai_count = st.session_state.real_time_stats.get('ai_processed_count', 0)
                    st.caption(f"已优化 {ai_count} 条记录")

            with col3:
                status = "✅" if sync_ready else "⏳"
                st.markdown(f"{status} **同步就绪**")
                if sync_ready:
                    sync_count = st.session_state.real_time_stats.get('synced_count', 0)
                    st.caption(f"已同步 {sync_count} 条记录")
    
    def render_data_processing(self):
        """渲染数据处理页面"""
        st.header("📊 数据处理中心")
        
        # 数据源选择
        st.subheader("📂 数据源管理")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("### 🌐 云商API")
            yunshang_enabled = st.checkbox("启用云商API数据获取", value=True)
            if yunshang_enabled:
                st.info("将从云商API获取最新产品数据")
                if st.button("🔄 测试API连接", key="data_test_yunshang_api"):
                    self.test_yunshang_api()
        
        with col2:
            st.markdown("### 📁 本地文件")
            local_enabled = st.checkbox("启用本地文件扫描", value=True)
            if local_enabled:
                local_path = st.text_input(
                    "本地文件路径", 
                    value=self.config.file_paths.local_files_base
                )
                if st.button("📂 浏览文件", key="data_browse_local_files"):
                    self.browse_local_files(local_path)
        
        with col3:
            st.markdown("### 📚 历史数据")
            legacy_enabled = st.checkbox("启用历史数据导入", value=True)
            if legacy_enabled:
                st.info("将导入allcollections.json中的历史数据")
                if st.button("📋 预览数据", key="data_preview_legacy_data"):
                    self.preview_legacy_data()
        
        st.divider()
        
        # 处理控制
        st.subheader("🎛️ 处理控制")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # 处理选项
            st.markdown("#### 处理选项")
            batch_size = st.slider("批处理大小", 10, 500, self.config.process.batch_size)
            max_retries = st.slider("最大重试次数", 1, 10, self.config.process.max_retries)
            timeout = st.slider("超时时间(秒)", 10, 300, self.config.process.timeout)
        
        with col2:
            st.markdown("#### 执行操作")
            
            # 检查是否有正在进行的任务
            is_processing = st.session_state.get('current_task') is not None

            if st.button(
                "🚀 开始完整处理" if not is_processing else "⏳ 处理中...",
                type="primary",
                use_container_width=True,
                disabled=is_processing,
                key="data_start_full_processing"
            ):
                # 检查应用初始化状态
                if not hasattr(self, 'initialized') or not self.initialized:
                    st.error("❌ 应用未正确初始化，请刷新页面重试")
                    return

                # 检查是否至少启用了一个数据源
                if not any([yunshang_enabled, local_enabled, legacy_enabled]):
                    st.error("❌ 请至少启用一个数据源")
                    return

                # 开始处理
                self.start_full_processing(
                    yunshang_enabled, local_enabled, legacy_enabled,
                    batch_size, max_retries, timeout
                )
            
            if st.button("🔄 增量更新", use_container_width=True, key="data_incremental_update"):
                self.start_incremental_update()

            if st.button("🛑 停止处理", use_container_width=True, key="data_stop_processing"):
                self.stop_processing()
        
        # 处理进度
        if st.session_state.get('current_task'):
            st.subheader("📈 处理进度")

            # 进度条
            progress = st.session_state.get('task_progress', 0)
            progress_bar = st.progress(progress)

            # 状态信息
            col1, col2 = st.columns([3, 1])

            with col1:
                st.write(f"**当前任务**: {st.session_state.current_task}")
                st.write(f"**进度**: {int(progress * 100)}%")

                # 显示处理统计
                if 'processing_results' in st.session_state:
                    results = st.session_state.processing_results
                    st.write(f"**已处理**: {results.get('total_processed', 0)} 条记录")
                    if results.get('errors', 0) > 0:
                        st.write(f"**错误**: {results['errors']} 个")

            with col2:
                if st.button("🛑 停止处理", use_container_width=True, key="progress_stop_processing"):
                    self.stop_processing()

            # 自动刷新进度
            if progress < 1.0:
                time.sleep(0.5)
                st.rerun()
    
    def render_sync_management(self):
        """渲染同步管理页面"""
        st.header("🔄 同步管理中心")

        # 检查数据处理状态
        data_processed = st.session_state.data_flow.get('data_processing_completed', False)
        if not data_processed:
            st.warning("⚠️ 请先在 '📊 数据处理' 页面完成数据处理，然后再进行同步管理")
            return

        # 同步状态概览 - 使用实时数据
        st.subheader("📊 同步状态概览")

        col1, col2, col3, col4 = st.columns(4)

        # 计算同步相关统计
        total_processed = (st.session_state.real_time_stats.get('yunshang_count', 0) +
                          st.session_state.real_time_stats.get('local_count', 0) +
                          st.session_state.real_time_stats.get('legacy_count', 0))
        synced_count = st.session_state.real_time_stats.get('synced_count', 0)
        pending_sync = total_processed - synced_count
        sync_errors = st.session_state.real_time_stats.get('error_count', 0)

        with col1:
            st.metric("待审核", pending_sync, delta=f"总计: {total_processed}")
        with col2:
            st.metric("已批准", pending_sync, delta="等待同步")
        with col3:
            st.metric("已同步", synced_count, delta=f"+{synced_count}" if synced_count > 0 else None)
        with col4:
            st.metric("同步失败", sync_errors, delta=f"错误率: {(sync_errors/total_processed*100):.1f}%" if total_processed > 0 else None)
        
        st.divider()
        
        # 变更检测
        st.subheader("🔍 变更检测")
        
        col1, col2 = st.columns([3, 1])
        
        with col1:
            st.info("变更检测将扫描所有数据源，识别新增、修改和删除的内容")
        
        with col2:
            if st.button("🔍 检测变更", type="primary", use_container_width=True, key="sync_detect_changes"):
                self.detect_changes()
        
        # 待审核列表
        st.subheader("📋 待审核变更")
        
        # 这里应该显示实际的待审核数据
        st.info("暂无待审核的变更")
        
        # 同步操作
        st.subheader("⚡ 同步操作")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("✅ 批量批准", use_container_width=True, key="sync_batch_approve"):
                self.batch_approve_changes()

        with col2:
            if st.button("🔄 执行同步", use_container_width=True, key="sync_execute_sync"):
                self.execute_sync()

        with col3:
            if st.button("📊 同步报告", use_container_width=True, key="sync_generate_report"):
                self.generate_sync_report()
    
    def render_ai_enhancement(self):
        """渲染AI增强页面"""
        st.header("🤖 AI增强处理")

        # 检查前置条件
        data_processed = st.session_state.data_flow.get('data_processing_completed', False)
        if not data_processed:
            st.warning("⚠️ 请先在 '📊 数据处理' 页面完成数据处理，然后再进行AI增强")
            return

        ai_ready = st.session_state.system_status.get('ai_models_ready', False)
        if not ai_ready:
            st.error("❌ AI模型未就绪，请在 '⚙️ 配置管理' 页面配置硅基流动API")
            return

        # AI模型状态
        st.subheader("🧠 AI模型状态")
        
        models = self.config.siliconflow.models
        
        for model_type, model_name in models.items():
            with st.expander(f"📱 {model_type.title()} 模型: {model_name}"):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write(f"**模型名称**: {model_name}")
                    st.write(f"**类型**: {model_type}")
                    st.write("**状态**: 🟢 可用")
                
                with col2:
                    if st.button(f"🧪 测试 {model_type} 模型", key=f"ai_test_model_{model_type}"):
                        self.test_ai_model(model_type)
        
        st.divider()
        
        # AI处理选项
        st.subheader("⚙️ AI处理选项")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### 图片处理")
            enable_image_analysis = st.checkbox("启用图片相关性分析", value=True)
            image_threshold = st.slider("相关性阈值", 0.0, 10.0, 6.0, 0.1)
            
            st.markdown("#### 内容优化")
            enable_content_optimization = st.checkbox("启用内容结构优化", value=True)
            enable_qa_generation = st.checkbox("启用问答对生成", value=True)
        
        with col2:
            st.markdown("#### 处理控制")
            max_concurrent = st.slider("最大并发请求", 1, 10, 3)
            enable_caching = st.checkbox("启用结果缓存", value=True)
            
            st.markdown("#### 成本控制")
            daily_limit = st.number_input("每日成本限制($)", 0.0, 1000.0, 50.0)
            monthly_limit = st.number_input("每月成本限制($)", 0.0, 10000.0, 1000.0)
        
        st.divider()
        
        # AI处理执行
        st.subheader("🚀 AI处理执行")
        
        if st.button("🤖 开始AI增强处理", type="primary", key="ai_start_enhancement"):
            self.start_ai_enhancement(
                enable_image_analysis, image_threshold,
                enable_content_optimization, enable_qa_generation,
                max_concurrent, enable_caching,
                daily_limit, monthly_limit
            )
    
    def run_health_check(self):
        """执行系统健康检查"""
        with st.spinner("正在执行健康检查..."):
            time.sleep(2)  # 模拟检查过程
            
            # 这里应该实现实际的健康检查逻辑
            st.success("✅ 系统健康检查完成")
            
            # 显示检查结果
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("""
                **✅ 通过的检查:**
                - Python环境正常
                - 配置文件完整
                - 目录结构正确
                - 依赖包已安装
                """)
            
            with col2:
                st.markdown("""
                **⚠️ 需要注意:**
                - 数据库连接需要验证
                - API密钥需要配置
                - 文件路径需要确认
                """)
    
    def test_yunshang_api(self):
        """测试云商API连接"""
        with st.spinner("正在测试API连接..."):
            time.sleep(1)
            # 这里应该实现实际的API测试逻辑
            st.success("✅ API连接测试成功")
    
    def start_full_processing(self, yunshang_enabled, local_enabled, legacy_enabled,
                            batch_size, max_retries, timeout):
        """开始完整处理"""
        try:
            # 验证配置
            if not self._validate_processing_config():
                return

            # 初始化处理状态
            st.session_state.current_task = "完整数据处理"
            st.session_state.task_progress = 0
            st.session_state.processing_errors = []
            st.session_state.processing_results = {
                'yunshang_count': 0,
                'local_count': 0,
                'legacy_count': 0,
                'total_processed': 0,
                'errors': 0
            }

            # 显示处理配置
            st.info(f"""
            **处理配置:**
            - 云商API: {'✅ 启用' if yunshang_enabled else '❌ 禁用'}
            - 本地文件: {'✅ 启用' if local_enabled else '❌ 禁用'}
            - 历史数据: {'✅ 启用' if legacy_enabled else '❌ 禁用'}
            - 批处理大小: {batch_size}
            - 最大重试: {max_retries}
            - 超时时间: {timeout}秒
            """)

            # 开始处理
            with st.spinner("正在启动完整处理..."):
                success = self._execute_full_processing(
                    yunshang_enabled, local_enabled, legacy_enabled,
                    batch_size, max_retries, timeout
                )

                if success:
                    st.success("✅ 完整处理已启动")
                    # 显示处理结果摘要
                    self._display_processing_summary()
                else:
                    st.error("❌ 处理启动失败，请查看错误信息")

        except Exception as e:
            st.error(f"❌ 处理启动异常: {str(e)}")
            st.session_state.current_task = None
            st.session_state.task_progress = 0
            logging.error(f"完整处理启动失败: {e}", exc_info=True)

    def _validate_processing_config(self):
        """验证处理配置"""
        try:
            # 检查配置管理器
            if not hasattr(self, 'config') or self.config is None:
                st.error("❌ 配置管理器未初始化")
                return False

            # 验证数据库配置
            validation_results = self.config.validate_config()

            if not validation_results.get('database', False):
                st.error("❌ 数据库配置无效，请检查配置管理页面")
                return False

            # 检查必要的文件路径
            if hasattr(self.config, 'file_paths'):
                if not Path(self.config.file_paths.allcollections).exists():
                    st.warning("⚠️ 历史数据文件不存在，将跳过历史数据导入")

                if not Path(self.config.file_paths.local_files_base).exists():
                    st.warning("⚠️ 本地文件基础路径不存在，将跳过本地文件扫描")

            return True

        except Exception as e:
            st.error(f"❌ 配置验证失败: {str(e)}")
            return False

    def _execute_full_processing(self, yunshang_enabled, local_enabled, legacy_enabled,
                               batch_size, max_retries, timeout):
        """执行完整处理逻辑"""
        try:
            total_steps = sum([yunshang_enabled, local_enabled, legacy_enabled])
            current_step = 0

            # 1. 处理云商API数据
            if yunshang_enabled:
                current_step += 1
                st.session_state.task_progress = (current_step - 0.5) / total_steps
                success, count = self._process_yunshang_data(batch_size, max_retries, timeout)
                if success:
                    st.session_state.processing_results['yunshang_count'] = count
                    st.info(f"✅ 云商API数据处理完成，处理了 {count} 条记录")
                else:
                    st.session_state.processing_results['errors'] += 1
                    st.error("❌ 云商API数据处理失败")

            # 2. 处理本地文件
            if local_enabled:
                current_step += 1
                st.session_state.task_progress = (current_step - 0.5) / total_steps
                success, count = self._process_local_files(batch_size, max_retries, timeout)
                if success:
                    st.session_state.processing_results['local_count'] = count
                    st.info(f"✅ 本地文件处理完成，处理了 {count} 个文件")
                else:
                    st.session_state.processing_results['errors'] += 1
                    st.error("❌ 本地文件处理失败")

            # 3. 处理历史数据
            if legacy_enabled:
                current_step += 1
                st.session_state.task_progress = (current_step - 0.5) / total_steps
                success, count = self._process_legacy_data(batch_size, max_retries, timeout)
                if success:
                    st.session_state.processing_results['legacy_count'] = count
                    st.info(f"✅ 历史数据处理完成，处理了 {count} 条记录")
                else:
                    st.session_state.processing_results['errors'] += 1
                    st.error("❌ 历史数据处理失败")

            # 完成处理
            st.session_state.task_progress = 1.0

            # 计算总处理数量
            results = st.session_state.processing_results
            results['total_processed'] = (
                results['yunshang_count'] +
                results['local_count'] +
                results['legacy_count']
            )

            # 更新实时统计
            st.session_state.real_time_stats.update({
                'yunshang_count': results['yunshang_count'],
                'local_count': results['local_count'],
                'legacy_count': results['legacy_count'],
                'error_count': results['errors']
            })

            # 更新数据流状态
            if results['total_processed'] > 0:
                st.session_state.data_flow['data_processing_completed'] = True
                st.session_state.data_flow['last_processing_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                st.session_state.data_flow['processing_results'] = results

            return results['errors'] == 0

        except Exception as e:
            st.error(f"❌ 处理执行异常: {str(e)}")
            logging.error(f"处理执行失败: {e}", exc_info=True)
            return False

    def _process_yunshang_data(self, batch_size, max_retries, timeout):
        """处理云商API数据"""
        try:
            # 模拟API调用和数据处理
            time.sleep(1)  # 模拟处理时间

            # 这里应该实现实际的云商API调用逻辑
            # 1. 调用云商API获取数据
            # 2. 数据清洗和格式化
            # 3. 存储到数据库

            # 模拟处理结果
            processed_count = 150  # 模拟处理了150条记录

            return True, processed_count

        except Exception as e:
            logging.error(f"云商API数据处理失败: {e}")
            return False, 0

    def _process_local_files(self, batch_size, max_retries, timeout):
        """处理本地文件"""
        try:
            # 模拟文件扫描和处理
            time.sleep(1)  # 模拟处理时间

            # 这里应该实现实际的本地文件处理逻辑
            # 1. 扫描本地文件目录
            # 2. 文件内容提取和分析
            # 3. 存储到数据库

            # 模拟处理结果
            processed_count = 85  # 模拟处理了85个文件

            return True, processed_count

        except Exception as e:
            logging.error(f"本地文件处理失败: {e}")
            return False, 0

    def _process_legacy_data(self, batch_size, max_retries, timeout):
        """处理历史数据"""
        try:
            # 模拟历史数据导入
            time.sleep(1)  # 模拟处理时间

            # 这里应该实现实际的历史数据处理逻辑
            # 1. 读取allcollections.json文件
            # 2. 数据清洗和转换
            # 3. 存储到数据库

            # 模拟处理结果
            processed_count = 320  # 模拟处理了320条历史记录

            return True, processed_count

        except Exception as e:
            logging.error(f"历史数据处理失败: {e}")
            return False, 0

    def _display_processing_summary(self):
        """显示处理结果摘要"""
        try:
            results = st.session_state.processing_results

            st.subheader("📊 处理结果摘要")

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric(
                    "云商API数据",
                    results['yunshang_count'],
                    delta=f"+{results['yunshang_count']}" if results['yunshang_count'] > 0 else None
                )

            with col2:
                st.metric(
                    "本地文件",
                    results['local_count'],
                    delta=f"+{results['local_count']}" if results['local_count'] > 0 else None
                )

            with col3:
                st.metric(
                    "历史数据",
                    results['legacy_count'],
                    delta=f"+{results['legacy_count']}" if results['legacy_count'] > 0 else None
                )

            with col4:
                st.metric(
                    "总计处理",
                    results['total_processed'],
                    delta=f"错误: {results['errors']}" if results['errors'] > 0 else "✅ 成功"
                )

            # 显示下一步建议
            if results['total_processed'] > 0:
                st.info("💡 **下一步建议**: 前往 '🤖 AI增强' 页面对处理的数据进行AI优化，然后在 '🔄 同步管理' 页面同步到FastGPT")

        except Exception as e:
            st.error(f"显示处理摘要失败: {str(e)}")

    def detect_changes(self):
        """检测变更"""
        with st.spinner("正在检测变更..."):
            time.sleep(2)

            # 模拟变更检测
            total_records = (st.session_state.real_time_stats.get('yunshang_count', 0) +
                           st.session_state.real_time_stats.get('local_count', 0) +
                           st.session_state.real_time_stats.get('legacy_count', 0))

            if total_records > 0:
                # 更新同步就绪状态
                st.session_state.data_flow['sync_ready'] = True
                st.success(f"✅ 变更检测完成，发现 {total_records} 条待同步记录")
            else:
                st.info("ℹ️ 未发现需要同步的变更")
    
    def start_ai_enhancement(self, *args):
        """开始AI增强处理"""
        with st.spinner("正在启动AI增强处理..."):
            time.sleep(2)

            # 模拟AI处理
            total_records = (st.session_state.real_time_stats.get('yunshang_count', 0) +
                           st.session_state.real_time_stats.get('local_count', 0) +
                           st.session_state.real_time_stats.get('legacy_count', 0))

            # 模拟处理一部分数据
            ai_processed = int(total_records * 0.8)  # 假设处理了80%的数据

            # 更新状态
            st.session_state.real_time_stats['ai_processed_count'] = ai_processed
            st.session_state.data_flow['ai_enhancement_completed'] = True

            st.success(f"✅ AI增强处理已完成，处理了 {ai_processed} 条记录")
            st.info("💡 **下一步建议**: 前往 '🔄 同步管理' 页面检测变更并同步到FastGPT")
    
    def test_ai_model(self, model_type):
        """测试AI模型"""
        with st.spinner(f"正在测试{model_type}模型..."):
            time.sleep(1)
            # 这里应该实现实际的模型测试逻辑
            st.success(f"✅ {model_type}模型测试成功")

    def render_config_management(self):
        """渲染配置管理页面"""
        st.header("⚙️ 配置管理")

        # 配置分类
        config_tab1, config_tab2, config_tab3, config_tab4 = st.tabs([
            "🗄️ 数据库", "🌐 API服务", "📁 文件路径", "🔧 系统设置"
        ])

        with config_tab1:
            self.render_database_config()

        with config_tab2:
            self.render_api_config()

        with config_tab3:
            self.render_file_path_config()

        with config_tab4:
            self.render_system_config()

    def render_database_config(self):
        """渲染数据库配置"""
        st.subheader("🗄️ 数据库配置")

        with st.form("database_config"):
            col1, col2 = st.columns(2)

            with col1:
                host = st.text_input("主机地址", value=self.config.database.host)
                port = st.number_input("端口", value=self.config.database.port, min_value=1, max_value=65535)
                user = st.text_input("用户名", value=self.config.database.user)

            with col2:
                password = st.text_input("密码", value=self.config.database.password, type="password")
                dbname = st.text_input("数据库名", value=self.config.database.dbname)

            col1, col2, col3 = st.columns(3)

            with col1:
                if st.form_submit_button("💾 保存配置", type="primary"):
                    self.config.database.host = host
                    self.config.database.port = port
                    self.config.database.user = user
                    self.config.database.password = password
                    self.config.database.dbname = dbname

                    if self.config.save_config():
                        st.success("数据库配置保存成功")
                    else:
                        st.error("数据库配置保存失败")

            with col2:
                if st.form_submit_button("🔍 测试连接"):
                    self.test_database_connection(host, port, user, password, dbname)

            with col3:
                if st.form_submit_button("🏗️ 初始化数据库"):
                    self.initialize_database()

    def render_api_config(self):
        """渲染API配置"""
        st.subheader("🌐 API服务配置")

        # 硅基流动配置
        with st.expander("🤖 硅基流动配置", expanded=True):
            with st.form("siliconflow_config"):
                api_key = st.text_input(
                    "API密钥",
                    value=self.config.siliconflow.api_key,
                    type="password"
                )
                api_base_url = st.text_input(
                    "API基础URL",
                    value=self.config.siliconflow.api_base_url
                )

                if st.form_submit_button("💾 保存硅基流动配置"):
                    self.config.siliconflow.api_key = api_key
                    self.config.siliconflow.api_base_url = api_base_url

                    if self.config.save_config():
                        st.success("硅基流动配置保存成功")
                    else:
                        st.error("硅基流动配置保存失败")

        # FastGPT配置
        with st.expander("📚 FastGPT配置", expanded=True):
            with st.form("fastgpt_config"):
                fastgpt_api_url = st.text_input(
                    "API地址",
                    value=self.config.fastgpt.api_url
                )
                fastgpt_api_key = st.text_input(
                    "API密钥",
                    value=self.config.fastgpt.api_key,
                    type="password"
                )
                fastgpt_dataset_id = st.text_input(
                    "数据集ID",
                    value=self.config.fastgpt.dataset_id
                )

                if st.form_submit_button("💾 保存FastGPT配置"):
                    self.config.fastgpt.api_url = fastgpt_api_url
                    self.config.fastgpt.api_key = fastgpt_api_key
                    self.config.fastgpt.dataset_id = fastgpt_dataset_id

                    if self.config.save_config():
                        st.success("FastGPT配置保存成功")
                    else:
                        st.error("FastGPT配置保存失败")

        # 云商API配置
        with st.expander("🏢 云商API配置", expanded=False):
            with st.form("yunshang_config"):
                yunshang_base_url = st.text_input(
                    "API基础URL",
                    value=self.config.api.base_url
                )
                yunshang_username = st.text_input(
                    "用户名",
                    value=self.config.api.username
                )
                yunshang_password = st.text_input(
                    "密码",
                    value=self.config.api.password,
                    type="password"
                )

                if st.form_submit_button("💾 保存云商API配置"):
                    self.config.api.base_url = yunshang_base_url
                    self.config.api.username = yunshang_username
                    self.config.api.password = yunshang_password

                    if self.config.save_config():
                        st.success("云商API配置保存成功")
                    else:
                        st.error("云商API配置保存失败")

    def render_file_path_config(self):
        """渲染文件路径配置"""
        st.subheader("📁 文件路径配置")

        with st.form("file_path_config"):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("#### 数据文件")
                allcollections = st.text_input(
                    "历史数据文件",
                    value=self.config.file_paths.allcollections
                )
                product_structure = st.text_input(
                    "产品结构文件",
                    value=self.config.file_paths.product_structure
                )
                local_files_base = st.text_input(
                    "本地文件基础路径",
                    value=self.config.file_paths.local_files_base
                )

            with col2:
                st.markdown("#### 工作目录")
                logs_dir = st.text_input(
                    "日志目录",
                    value=self.config.file_paths.logs_dir
                )
                downloads_dir = st.text_input(
                    "下载目录",
                    value=self.config.file_paths.downloads_dir
                )
                uploads_dir = st.text_input(
                    "上传目录",
                    value=self.config.file_paths.uploads_dir
                )
                temp_dir = st.text_input(
                    "临时目录",
                    value=self.config.file_paths.temp_dir
                )
                results_dir = st.text_input(
                    "结果目录",
                    value=self.config.file_paths.results_dir
                )

            col1, col2 = st.columns(2)

            with col1:
                if st.form_submit_button("💾 保存路径配置", type="primary"):
                    self.config.file_paths.allcollections = allcollections
                    self.config.file_paths.product_structure = product_structure
                    self.config.file_paths.local_files_base = local_files_base
                    self.config.file_paths.logs_dir = logs_dir
                    self.config.file_paths.downloads_dir = downloads_dir
                    self.config.file_paths.uploads_dir = uploads_dir
                    self.config.file_paths.temp_dir = temp_dir
                    self.config.file_paths.results_dir = results_dir

                    if self.config.save_config():
                        st.success("文件路径配置保存成功")
                    else:
                        st.error("文件路径配置保存失败")

            with col2:
                if st.form_submit_button("📂 创建目录"):
                    self.config.create_directories()
                    st.success("目录创建完成")

    def render_system_config(self):
        """渲染系统配置"""
        st.subheader("🔧 系统设置")

        with st.form("system_config"):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("#### 处理设置")
                batch_size = st.slider(
                    "批处理大小",
                    10, 1000,
                    self.config.process.batch_size
                )
                max_retries = st.slider(
                    "最大重试次数",
                    1, 10,
                    self.config.process.max_retries
                )
                timeout = st.slider(
                    "超时时间(秒)",
                    10, 300,
                    self.config.process.timeout
                )
                max_concurrent = st.slider(
                    "最大并发请求",
                    1, 20,
                    self.config.process.max_concurrent_requests
                )

            with col2:
                st.markdown("#### 功能开关")
                enable_local_scan = st.checkbox(
                    "启用本地文件扫描",
                    value=self.config.process.enable_local_scan
                )
                enable_yunshang_api = st.checkbox(
                    "启用云商API",
                    value=self.config.process.enable_yunshang_api
                )
                enable_legacy_import = st.checkbox(
                    "启用历史数据导入",
                    value=self.config.process.enable_legacy_import
                )
                cache_enabled = st.checkbox(
                    "启用缓存",
                    value=self.config.process.cache_enabled
                )
                cache_ttl = st.number_input(
                    "缓存生存时间(秒)",
                    60, 86400,
                    self.config.process.cache_ttl
                )

            if st.form_submit_button("💾 保存系统配置", type="primary"):
                self.config.process.batch_size = batch_size
                self.config.process.max_retries = max_retries
                self.config.process.timeout = timeout
                self.config.process.max_concurrent_requests = max_concurrent
                self.config.process.enable_local_scan = enable_local_scan
                self.config.process.enable_yunshang_api = enable_yunshang_api
                self.config.process.enable_legacy_import = enable_legacy_import
                self.config.process.cache_enabled = cache_enabled
                self.config.process.cache_ttl = cache_ttl

                if self.config.save_config():
                    st.success("系统配置保存成功")
                else:
                    st.error("系统配置保存失败")

    def render_log_monitoring(self):
        """渲染日志监控页面"""
        st.header("📋 日志监控")

        # 日志级别选择
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            log_level = st.selectbox(
                "日志级别",
                ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                index=1
            )

        with col2:
            auto_refresh = st.checkbox("自动刷新", value=False)

        with col3:
            if st.button("🔄 刷新日志", key="log_refresh_logs"):
                st.rerun()

        # 日志显示
        st.subheader("📄 实时日志")

        # 这里应该读取实际的日志文件
        log_content = self.get_recent_logs(log_level)

        if log_content:
            st.code(log_content, language="text")
        else:
            st.info("暂无日志内容")

        # 日志统计
        st.subheader("📊 日志统计")

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("总日志条数", "0")
        with col2:
            st.metric("错误数", "0", "0")
        with col3:
            st.metric("警告数", "0", "0")
        with col4:
            st.metric("今日日志", "0")

        # 自动刷新
        if auto_refresh:
            time.sleep(5)
            st.rerun()

    def test_database_connection(self, host, port, user, password, dbname):
        """测试数据库连接"""
        with st.spinner("正在测试数据库连接..."):
            try:
                # 这里应该实现实际的数据库连接测试
                time.sleep(1)
                st.success("✅ 数据库连接测试成功")
            except Exception as e:
                st.error(f"❌ 数据库连接测试失败: {e}")

    def initialize_database(self):
        """初始化数据库"""
        with st.spinner("正在初始化数据库..."):
            try:
                # 这里应该实现实际的数据库初始化逻辑
                time.sleep(2)
                st.success("✅ 数据库初始化成功")
            except Exception as e:
                st.error(f"❌ 数据库初始化失败: {e}")

    def get_recent_logs(self, level):
        """获取最近的日志"""
        try:
            log_file = Path(self.config.log.file)
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # 返回最后50行
                    return ''.join(lines[-50:])
            else:
                return "日志文件不存在"
        except Exception as e:
            return f"读取日志失败: {e}"

    def browse_local_files(self, path):
        """浏览本地文件"""
        try:
            file_path = Path(path)
            if file_path.exists():
                files = list(file_path.glob("**/*"))[:100]  # 限制显示100个文件
                st.write(f"找到 {len(files)} 个文件:")
                for file in files:
                    st.write(f"📄 {file}")
            else:
                st.error("路径不存在")
        except Exception as e:
            st.error(f"浏览文件失败: {e}")

    def preview_legacy_data(self):
        """预览历史数据"""
        try:
            legacy_file = Path(self.config.file_paths.allcollections)
            if legacy_file.exists():
                with open(legacy_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    st.write(f"历史数据包含 {len(data)} 条记录")
                    if data:
                        st.json(data[0])  # 显示第一条记录作为示例
            else:
                st.error("历史数据文件不存在")
        except Exception as e:
            st.error(f"预览历史数据失败: {e}")

    def update_progress_display(self, progress_bar, status_text):
        """更新进度显示"""
        if st.session_state.current_task:
            progress_bar.progress(st.session_state.task_progress)
            status_text.text(f"当前任务: {st.session_state.current_task}")

    def stop_processing(self):
        """停止处理"""
        try:
            # 清理处理状态
            st.session_state.current_task = None
            st.session_state.task_progress = 0

            # 保存处理结果（如果有的话）
            if 'processing_results' in st.session_state:
                results = st.session_state.processing_results
                if results.get('total_processed', 0) > 0:
                    st.warning(f"⚠️ 处理已停止，已处理 {results['total_processed']} 条记录")
                else:
                    st.info("ℹ️ 处理已停止")
            else:
                st.info("ℹ️ 处理已停止")

            # 清理错误状态
            if 'processing_errors' in st.session_state:
                del st.session_state.processing_errors

        except Exception as e:
            st.error(f"停止处理时发生错误: {str(e)}")
            # 强制清理状态
            st.session_state.current_task = None
            st.session_state.task_progress = 0

    def start_incremental_update(self):
        """开始增量更新"""
        st.session_state.current_task = "增量更新"
        st.session_state.task_progress = 0
        st.success("增量更新已启动")

    def batch_approve_changes(self):
        """批量批准变更"""
        with st.spinner("正在批量批准变更..."):
            time.sleep(1)
            st.success("批量批准完成")

    def execute_sync(self):
        """执行同步"""
        with st.spinner("正在执行同步..."):
            time.sleep(2)

            # 模拟同步过程
            total_records = (st.session_state.real_time_stats.get('yunshang_count', 0) +
                           st.session_state.real_time_stats.get('local_count', 0) +
                           st.session_state.real_time_stats.get('legacy_count', 0))

            if total_records > 0:
                # 更新同步统计
                st.session_state.real_time_stats['synced_count'] = total_records
                st.success(f"✅ 同步执行完成，已同步 {total_records} 条记录到FastGPT")
            else:
                st.info("ℹ️ 没有需要同步的数据")

    def generate_sync_report(self):
        """生成同步报告"""
        with st.spinner("正在生成同步报告..."):
            time.sleep(1)
            st.success("同步报告生成完成")

def main():
    """主函数"""
    try:
        # 显示加载状态
        with st.spinner("正在初始化应用..."):
            app = UnifiedApp()

        # 检查初始化状态
        if not hasattr(app, 'initialized') or not app.initialized:
            st.error("❌ 应用初始化失败")
            st.stop()

        # 渲染页面
        app.render_header()
        app.render_sidebar()
        app.render_main_tabs()

    except Exception as e:
        st.error(f"❌ 应用启动失败: {e}")

        # 显示详细错误信息
        with st.expander("🔍 查看详细错误信息"):
            st.code(traceback.format_exc())

        # 提供解决建议
        st.markdown("""
        ### 💡 可能的解决方案:
        1. **检查配置文件**: 确保 `unified_config.yaml` 存在且格式正确
        2. **检查依赖**: 确保所有必需的Python包已安装
        3. **检查权限**: 确保应用有读取配置文件的权限
        4. **重启应用**: 尝试重新启动Streamlit应用
        5. **查看日志**: 检查终端输出的详细错误信息
        """)

        # 提供快速修复按钮
        if st.button("🔄 重新加载应用", key="main_reload_app"):
            st.rerun()

if __name__ == "__main__":
    main()
