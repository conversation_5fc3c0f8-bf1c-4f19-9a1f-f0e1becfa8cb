# TextIn 通用文档解析 API 文档

## 功能描述

### PDF转Markdown
上传图片/PDF/Word/HTML/Excel/PPT/TXT，进行版面检测，文字识别，表格识别，版面分析等操作，并生成markdown文档。

## 接口信息

### 请求URL
```
https://api.textin.com/ai/service/v1/pdf_to_markdown
```

### HTTP请求方法
```
HTTP POST
```

## 请求头说明（Request Headers）

请在HTTP请求中添加以下自定义标头（Header）：

| Header名 | 值 | 说明 |
|----------|----|---------|
| x-ti-app-id | 请登录后前往 "工作台-账号设置-开发者信息" 查看 | 应用ID |
| x-ti-secret-code | 请登录后前往 "工作台-账号设置-开发者信息" 查看 | 密钥代码 |

## URL参数（Parameters）

URL参数指以 `{参数名}={参数值}` 形式拼接到 URL 上的键值对。它以 `?` 开头，不同参数之间使用 `&` 连接。

形如：`?p1=v1&p2=v2`

### 参数列表

| 参数名 | 数据类型 | 是否必填 | 允许的值 | 描述 |
|--------|----------|----------|----------|------|
| pdf_pwd | string | 否 | 见描述 | 当PDF为加密文档时，需要提供密码。备注：对前端封装该接口时，需要自行对密码进行安全防护 |
| char_details | integer | 否 | 见描述 | 当为1或true时，可以返回char_pos字段，保存了每一行的位置信息。默认关闭 |
| page_details | integer | 否 | 见描述 | 当为1或true时，可以返回pages字段，保存了每一页更加详细的解析结果。默认打开 |
| catalog_details | integer | 否 | 见描述 | 当为1或true时，可以返回catalog details |
| dpi | integer | 否 | 见描述 | PDF文档的坐标基准，默认144 dpi。当parse_mode=auto时，默认动态，支持72，144，216；当parse_mode=scan时，默认144，支持72，144，216 |
| page_start | integer | 否 | 见描述 | 当上传的是PDF时，page_start表示从第几页开始转换 |

## 使用示例

### Python示例

```python
import requests

def pdf_to_markdown(file_path, app_id, secret_code, **kwargs):
    """
    将PDF文件转换为Markdown格式
    
    Args:
        file_path (str): PDF文件路径
        app_id (str): TextIn应用ID
        secret_code (str): TextIn密钥代码
        **kwargs: 其他可选参数
    
    Returns:
        dict: API响应结果
    """
    url = "https://api.textin.com/ai/service/v1/pdf_to_markdown"
    
    headers = {
        'x-ti-app-id': app_id,
        'x-ti-secret-code': secret_code
    }
    
    # 构建URL参数
    params = {}
    if 'pdf_pwd' in kwargs:
        params['pdf_pwd'] = kwargs['pdf_pwd']
    if 'char_details' in kwargs:
        params['char_details'] = kwargs['char_details']
    if 'page_details' in kwargs:
        params['page_details'] = kwargs['page_details']
    if 'catalog_details' in kwargs:
        params['catalog_details'] = kwargs['catalog_details']
    if 'dpi' in kwargs:
        params['dpi'] = kwargs['dpi']
    if 'page_start' in kwargs:
        params['page_start'] = kwargs['page_start']
    
    # 上传文件
    with open(file_path, 'rb') as f:
        files = {'file': f}
        response = requests.post(url, headers=headers, params=params, files=files)
    
    return response.json()

# 使用示例
if __name__ == "__main__":
    app_id = "your_app_id"
    secret_code = "your_secret_code"
    file_path = "example.pdf"
    
    result = pdf_to_markdown(
        file_path=file_path,
        app_id=app_id,
        secret_code=secret_code,
        page_details=1,
        char_details=1,
        dpi=144
    )
    
    print(result)
```

### JavaScript示例

```javascript
/**
 * 将PDF文件转换为Markdown格式
 * @param {File} file - PDF文件对象
 * @param {string} appId - TextIn应用ID
 * @param {string} secretCode - TextIn密钥代码
 * @param {Object} options - 可选参数
 * @returns {Promise} API响应结果
 */
async function pdfToMarkdown(file, appId, secretCode, options = {}) {
    const url = 'https://api.textin.com/ai/service/v1/pdf_to_markdown';
    
    // 构建URL参数
    const params = new URLSearchParams();
    if (options.pdfPwd) params.append('pdf_pwd', options.pdfPwd);
    if (options.charDetails) params.append('char_details', options.charDetails);
    if (options.pageDetails) params.append('page_details', options.pageDetails);
    if (options.catalogDetails) params.append('catalog_details', options.catalogDetails);
    if (options.dpi) params.append('dpi', options.dpi);
    if (options.pageStart) params.append('page_start', options.pageStart);
    
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch(`${url}?${params}`, {
        method: 'POST',
        headers: {
            'x-ti-app-id': appId,
            'x-ti-secret-code': secretCode
        },
        body: formData
    });
    
    return await response.json();
}

// 使用示例
const fileInput = document.getElementById('fileInput');
const file = fileInput.files[0];

pdfToMarkdown(file, 'your_app_id', 'your_secret_code', {
    pageDetails: 1,
    charDetails: 1,
    dpi: 144
}).then(result => {
    console.log(result);
}).catch(error => {
    console.error('Error:', error);
});
```

## 响应格式

API返回JSON格式的响应，包含以下主要字段：

- **markdown**: 转换后的Markdown内容
- **pages**: 每页详细解析结果（当page_details=1时返回）
- **char_pos**: 字符位置信息（当char_details=1时返回）
- **catalog**: 目录信息（当catalog_details=1时返回）

## 注意事项

1. **安全性**: 对于加密PDF，需要提供密码参数，前端封装时需要对密码进行安全防护
2. **文件大小**: 请注意API对文件大小的限制
3. **并发限制**: 请遵守API的并发请求限制
4. **错误处理**: 建议在代码中添加适当的错误处理机制
5. **认证信息**: 确保x-ti-app-id和x-ti-secret-code的安全性，不要在客户端代码中暴露

## 支持的文件格式

- PDF
- Word (DOC/DOCX)
- Excel (XLS/XLSX)
- PowerPoint (PPT/PPTX)
- HTML
- TXT
- 图片格式（JPG、PNG等）

## 应用场景

1. **文档数字化**: 将纸质文档转换为可编辑的Markdown格式
2. **知识库建设**: 批量处理文档，构建结构化知识库
3. **内容管理**: 将各种格式的文档统一转换为Markdown进行管理
4. **数据提取**: 从复杂文档中提取结构化信息
5. **自动化处理**: 集成到工作流中，实现文档处理自动化

## 最佳实践

1. **批量处理**: 对于大量文档，建议实现队列机制避免并发限制
2. **结果缓存**: 对于相同文档，可以缓存转换结果提高效率
3. **质量检查**: 转换后建议进行人工或自动质量检查
4. **格式优化**: 根据具体需求对转换后的Markdown进行格式优化
5. **错误重试**: 实现合理的重试机制处理网络或服务异常

---

*本文档基于TextIn官方API文档整理，具体使用时请参考最新的官方文档。*