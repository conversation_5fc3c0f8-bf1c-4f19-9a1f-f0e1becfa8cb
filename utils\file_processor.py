#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件处理器模块
提供文件读取、写入、格式转换等功能
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import pandas as pd
from docx import Document
import PyPDF2
import openpyxl

class FileProcessor:
    """
    文件处理器类
    负责处理各种格式的文件读取、写入和转换
    """
    
    def __init__(self, encoding: str = 'utf-8'):
        """
        初始化文件处理器
        
        Args:
            encoding: 文件编码格式，默认为utf-8
        """
        self.encoding = encoding
        self.logger = logging.getLogger(__name__)
        
        # 支持的文件格式
        self.supported_formats = {
            'text': ['.txt', '.md', '.py', '.js', '.html', '.css', '.json'],
            'document': ['.docx', '.pdf'],
            'spreadsheet': ['.xlsx', '.xls', '.csv'],
            'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        }
    
    def read_text_file(self, file_path: Union[str, Path]) -> str:
        """
        读取文本文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容字符串
        """
        try:
            with open(file_path, 'r', encoding=self.encoding) as file:
                return file.read()
        except Exception as e:
            self.logger.error(f"读取文本文件失败: {file_path}, 错误: {e}")
            return ""
    
    def write_text_file(self, file_path: Union[str, Path], content: str) -> bool:
        """
        写入文本文件
        
        Args:
            file_path: 文件路径
            content: 文件内容
            
        Returns:
            是否写入成功
        """
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding=self.encoding) as file:
                file.write(content)
            return True
        except Exception as e:
            self.logger.error(f"写入文本文件失败: {file_path}, 错误: {e}")
            return False
    
    def read_json_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        读取JSON文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            JSON数据字典
        """
        try:
            with open(file_path, 'r', encoding=self.encoding) as file:
                return json.load(file)
        except Exception as e:
            self.logger.error(f"读取JSON文件失败: {file_path}, 错误: {e}")
            return {}
    
    def write_json_file(self, file_path: Union[str, Path], data: Dict[str, Any], indent: int = 2) -> bool:
        """
        写入JSON文件
        
        Args:
            file_path: 文件路径
            data: 要写入的数据
            indent: 缩进空格数
            
        Returns:
            是否写入成功
        """
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding=self.encoding) as file:
                json.dump(data, file, ensure_ascii=False, indent=indent)
            return True
        except Exception as e:
            self.logger.error(f"写入JSON文件失败: {file_path}, 错误: {e}")
            return False
    
    def read_excel_file(self, file_path: Union[str, Path], sheet_name: Optional[str] = None) -> pd.DataFrame:
        """
        读取Excel文件
        
        Args:
            file_path: 文件路径
            sheet_name: 工作表名称，默认读取第一个工作表
            
        Returns:
            DataFrame对象
        """
        try:
            return pd.read_excel(file_path, sheet_name=sheet_name)
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {file_path}, 错误: {e}")
            return pd.DataFrame()
    
    def write_excel_file(self, file_path: Union[str, Path], data: pd.DataFrame, sheet_name: str = 'Sheet1') -> bool:
        """
        写入Excel文件
        
        Args:
            file_path: 文件路径
            data: DataFrame数据
            sheet_name: 工作表名称
            
        Returns:
            是否写入成功
        """
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            data.to_excel(file_path, sheet_name=sheet_name, index=False)
            return True
        except Exception as e:
            self.logger.error(f"写入Excel文件失败: {file_path}, 错误: {e}")
            return False
    
    def read_docx_file(self, file_path: Union[str, Path]) -> str:
        """
        读取Word文档内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            文档内容字符串
        """
        try:
            doc = Document(file_path)
            content = []
            for paragraph in doc.paragraphs:
                content.append(paragraph.text)
            return '\n'.join(content)
        except Exception as e:
            self.logger.error(f"读取Word文档失败: {file_path}, 错误: {e}")
            return ""
    
    def read_pdf_file(self, file_path: Union[str, Path]) -> str:
        """
        读取PDF文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            PDF内容字符串
        """
        try:
            content = []
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    content.append(page.extract_text())
            return '\n'.join(content)
        except Exception as e:
            self.logger.error(f"读取PDF文件失败: {file_path}, 错误: {e}")
            return ""
    
    def get_file_info(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            path = Path(file_path)
            stat = path.stat()
            
            return {
                'name': path.name,
                'size': stat.st_size,
                'extension': path.suffix.lower(),
                'created_time': stat.st_ctime,
                'modified_time': stat.st_mtime,
                'is_file': path.is_file(),
                'is_dir': path.is_dir(),
                'exists': path.exists()
            }
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {file_path}, 错误: {e}")
            return {}
    
    def is_supported_format(self, file_path: Union[str, Path], format_type: str = None) -> bool:
        """
        检查文件格式是否支持
        
        Args:
            file_path: 文件路径
            format_type: 格式类型（text, document, spreadsheet, image）
            
        Returns:
            是否支持该格式
        """
        extension = Path(file_path).suffix.lower()
        
        if format_type:
            return extension in self.supported_formats.get(format_type, [])
        else:
            # 检查所有支持的格式
            all_formats = []
            for formats in self.supported_formats.values():
                all_formats.extend(formats)
            return extension in all_formats
    
    def batch_process_files(self, directory: Union[str, Path], 
                          file_pattern: str = "*", 
                          processor_func: callable = None) -> List[Dict[str, Any]]:
        """
        批量处理文件
        
        Args:
            directory: 目录路径
            file_pattern: 文件匹配模式
            processor_func: 处理函数
            
        Returns:
            处理结果列表
        """
        results = []
        try:
            directory_path = Path(directory)
            files = list(directory_path.glob(file_pattern))
            
            for file_path in files:
                if file_path.is_file():
                    try:
                        if processor_func:
                            result = processor_func(file_path)
                        else:
                            result = self.get_file_info(file_path)
                        
                        results.append({
                            'file_path': str(file_path),
                            'status': 'success',
                            'result': result
                        })
                    except Exception as e:
                        results.append({
                            'file_path': str(file_path),
                            'status': 'error',
                            'error': str(e)
                        })
                        
        except Exception as e:
            self.logger.error(f"批量处理文件失败: {directory}, 错误: {e}")
            
        return results