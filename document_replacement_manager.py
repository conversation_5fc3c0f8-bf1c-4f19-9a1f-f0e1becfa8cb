# -*- coding: utf-8 -*-
"""
文档替代管理器
功能：
1. 检测作废文档
2. 多数据源查找替代资料
3. 版本比较和自动替换
4. 缺失文档通知机制
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import hashlib
import os
from pathlib import Path
from dataclasses import dataclass
import aiofiles
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DocumentInfo:
    """文档信息数据类"""
    id: str
    name: str
    path: str
    product_id: str
    document_type: str
    version: str
    modified_time: datetime
    file_size: int
    hash_value: str
    status: str  # active, obsolete, missing
    source: str  # cloud_api, local_docs, backup_archive

@dataclass
class ReplacementResult:
    """替代结果数据类"""
    found: bool
    action: str  # replace, mark_missing, no_action
    source: str
    old_document: Optional[DocumentInfo]
    new_document: Optional[DocumentInfo]
    reason: str
    confidence_score: float

class DocumentReplacementManager:
    """文档替代管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化文档替代管理器
        
        Args:
            config: 配置信息，包含数据源配置
        """
        self.config = config
        
        # 数据源配置（按优先级排序）
        self.data_sources = {
            'cloud_api': {
                'priority': 1, 
                'enabled': config.get('cloud_api_enabled', True),
                'base_url': config.get('cloud_api_url', ''),
                'api_key': config.get('cloud_api_key', '')
            },
            'local_docs': {
                'priority': 2, 
                'enabled': config.get('local_docs_enabled', True),
                'base_path': config.get('local_docs_path', './documents')
            },
            'backup_archive': {
                'priority': 3, 
                'enabled': config.get('backup_enabled', True),
                'base_path': config.get('backup_path', './backup')
            }
        }
        
        # 版本比较配置
        self.version_config = {
            'time_threshold_days': config.get('time_threshold_days', 30),
            'size_change_threshold': config.get('size_change_threshold', 0.1),
            'hash_comparison_enabled': config.get('hash_comparison_enabled', True)
        }
        
        # 统计信息
        self.stats = {
            'total_checked': 0,
            'obsolete_found': 0,
            'replacements_found': 0,
            'auto_replaced': 0,
            'missing_documents': 0,
            'errors': 0
        }
    
    async def scan_obsolete_documents(self, document_list: List[DocumentInfo]) -> List[DocumentInfo]:
        """扫描作废文档"""
        obsolete_docs = []
        
        for doc in document_list:
            self.stats['total_checked'] += 1
            
            try:
                if await self._is_document_obsolete(doc):
                    doc.status = 'obsolete'
                    obsolete_docs.append(doc)
                    self.stats['obsolete_found'] += 1
                    logger.info(f"发现作废文档: {doc.name}")
                    
            except Exception as e:
                logger.error(f"检查文档状态失败 {doc.name}: {str(e)}")
                self.stats['errors'] += 1
        
        return obsolete_docs
    
    async def _is_document_obsolete(self, doc: DocumentInfo) -> bool:
        """判断文档是否作废"""
        # 检查文件是否存在
        if not os.path.exists(doc.path):
            return True
        
        # 检查修改时间
        threshold_date = datetime.now() - timedelta(days=self.version_config['time_threshold_days'])
        if doc.modified_time < threshold_date:
            # 进一步检查是否有更新版本
            return await self._has_newer_version_available(doc)
        
        return False
    
    async def _has_newer_version_available(self, doc: DocumentInfo) -> bool:
        """检查是否有更新版本可用"""
        for source_name, config in sorted(self.data_sources.items(), 
                                        key=lambda x: x[1]['priority']):
            if not config['enabled']:
                continue
            
            try:
                newer_doc = await self._search_newer_version_in_source(
                    source_name, doc
                )
                
                if newer_doc and self._is_newer_version(newer_doc, doc):
                    return True
                    
            except Exception as e:
                logger.error(f"搜索更新版本失败 {source_name}: {str(e)}")
        
        return False
    
    async def find_replacement_document(self, obsolete_doc: DocumentInfo) -> ReplacementResult:
        """查找替代文档"""
        logger.info(f"开始查找替代文档: {obsolete_doc.name}")
        
        # 按优先级搜索数据源
        for source_name, config in sorted(self.data_sources.items(), 
                                        key=lambda x: x[1]['priority']):
            if not config['enabled']:
                continue
            
            try:
                replacement = await self._search_in_source(
                    source_name, obsolete_doc.product_id, obsolete_doc.document_type
                )
                
                if replacement:
                    # 版本比较
                    if self._is_newer_version(replacement, obsolete_doc):
                        confidence_score = self._calculate_replacement_confidence(
                            obsolete_doc, replacement
                        )
                        
                        self.stats['replacements_found'] += 1
                        
                        return ReplacementResult(
                            found=True,
                            action='replace',
                            source=source_name,
                            old_document=obsolete_doc,
                            new_document=replacement,
                            reason=f"在{source_name}中找到更新版本",
                            confidence_score=confidence_score
                        )
                    
            except Exception as e:
                logger.error(f"搜索数据源失败 {source_name}: {str(e)}")
        
        # 未找到替代文档
        self.stats['missing_documents'] += 1
        
        return ReplacementResult(
            found=False,
            action='mark_missing',
            source='',
            old_document=obsolete_doc,
            new_document=None,
            reason="所有数据源中均未找到替代文档",
            confidence_score=0.0
        )
    
    async def _search_in_source(self, source_name: str, product_id: str, doc_type: str) -> Optional[DocumentInfo]:
        """在指定数据源中搜索文档"""
        if source_name == 'cloud_api':
            return await self._search_in_cloud_api(product_id, doc_type)
        elif source_name == 'local_docs':
            return await self._search_in_local_docs(product_id, doc_type)
        elif source_name == 'backup_archive':
            return await self._search_in_backup_archive(product_id, doc_type)
        else:
            logger.warning(f"未知数据源: {source_name}")
            return None
    
    async def _search_in_cloud_api(self, product_id: str, doc_type: str) -> Optional[DocumentInfo]:
        """在云端API中搜索文档"""
        # 这里应该实现具体的云端API调用逻辑
        # 示例实现
        try:
            # 模拟API调用
            await asyncio.sleep(0.1)  # 模拟网络延迟
            
            # 实际实现中应该调用真实的API
            # response = await self._call_cloud_api(product_id, doc_type)
            
            # 模拟返回结果
            if product_id and doc_type:
                return DocumentInfo(
                    id=f"cloud_{product_id}_{doc_type}",
                    name=f"{product_id}_{doc_type}_latest.pdf",
                    path=f"cloud://documents/{product_id}/{doc_type}.pdf",
                    product_id=product_id,
                    document_type=doc_type,
                    version="2.0",
                    modified_time=datetime.now(),
                    file_size=1024000,
                    hash_value=hashlib.md5(f"{product_id}_{doc_type}_latest".encode()).hexdigest(),
                    status="active",
                    source="cloud_api"
                )
            
        except Exception as e:
            logger.error(f"云端API搜索失败: {str(e)}")
        
        return None
    
    async def _search_in_local_docs(self, product_id: str, doc_type: str) -> Optional[DocumentInfo]:
        """在本地文档库中搜索文档"""
        try:
            base_path = Path(self.data_sources['local_docs']['base_path'])
            
            # 搜索模式：product_id/doc_type/
            search_patterns = [
                base_path / product_id / f"{doc_type}*.pdf",
                base_path / product_id / f"{doc_type}*.docx",
                base_path / product_id / f"{doc_type}*.md",
                base_path / f"{product_id}_{doc_type}*"
            ]
            
            for pattern in search_patterns:
                files = list(pattern.parent.glob(pattern.name)) if pattern.parent.exists() else []
                
                if files:
                    # 选择最新的文件
                    latest_file = max(files, key=lambda f: f.stat().st_mtime)
                    
                    stat = latest_file.stat()
                    
                    # 计算文件哈希
                    hash_value = await self._calculate_file_hash(latest_file)
                    
                    return DocumentInfo(
                        id=f"local_{latest_file.stem}",
                        name=latest_file.name,
                        path=str(latest_file),
                        product_id=product_id,
                        document_type=doc_type,
                        version=self._extract_version_from_filename(latest_file.name),
                        modified_time=datetime.fromtimestamp(stat.st_mtime),
                        file_size=stat.st_size,
                        hash_value=hash_value,
                        status="active",
                        source="local_docs"
                    )
            
        except Exception as e:
            logger.error(f"本地文档搜索失败: {str(e)}")
        
        return None
    
    async def _search_in_backup_archive(self, product_id: str, doc_type: str) -> Optional[DocumentInfo]:
        """在备份归档中搜索文档"""
        try:
            base_path = Path(self.data_sources['backup_archive']['base_path'])
            
            # 搜索备份文件
            backup_patterns = [
                base_path / "**" / f"{product_id}*{doc_type}*",
                base_path / "**" / f"*{product_id}*",
            ]
            
            for pattern in backup_patterns:
                files = list(base_path.rglob(pattern.name))
                
                if files:
                    # 选择最新的备份文件
                    latest_backup = max(files, key=lambda f: f.stat().st_mtime)
                    
                    stat = latest_backup.stat()
                    hash_value = await self._calculate_file_hash(latest_backup)
                    
                    return DocumentInfo(
                        id=f"backup_{latest_backup.stem}",
                        name=latest_backup.name,
                        path=str(latest_backup),
                        product_id=product_id,
                        document_type=doc_type,
                        version=self._extract_version_from_filename(latest_backup.name),
                        modified_time=datetime.fromtimestamp(stat.st_mtime),
                        file_size=stat.st_size,
                        hash_value=hash_value,
                        status="active",
                        source="backup_archive"
                    )
            
        except Exception as e:
            logger.error(f"备份归档搜索失败: {str(e)}")
        
        return None
    
    async def _search_newer_version_in_source(self, source_name: str, doc: DocumentInfo) -> Optional[DocumentInfo]:
        """在指定数据源中搜索更新版本"""
        return await self._search_in_source(source_name, doc.product_id, doc.document_type)
    
    def _is_newer_version(self, new_doc: DocumentInfo, old_doc: DocumentInfo) -> bool:
        """版本比较逻辑"""
        # 1. 比较修改时间
        if new_doc.modified_time > old_doc.modified_time:
            time_diff = (new_doc.modified_time - old_doc.modified_time).days
            if time_diff > 1:  # 至少相差1天
                return True
        
        # 2. 比较版本号
        if self._compare_version_strings(new_doc.version, old_doc.version) > 0:
            return True
        
        # 3. 比较文件大小（可能表示内容更新）
        if old_doc.file_size > 0:
            size_change_ratio = abs(new_doc.file_size - old_doc.file_size) / old_doc.file_size
            if size_change_ratio > self.version_config['size_change_threshold']:
                return True
        
        # 4. 比较文件哈希
        if (self.version_config['hash_comparison_enabled'] and 
            new_doc.hash_value != old_doc.hash_value):
            return True
        
        return False
    
    def _compare_version_strings(self, version1: str, version2: str) -> int:
        """比较版本字符串"""
        try:
            # 简单的版本比较逻辑
            v1_parts = [int(x) for x in version1.split('.') if x.isdigit()]
            v2_parts = [int(x) for x in version2.split('.') if x.isdigit()]
            
            # 补齐长度
            max_len = max(len(v1_parts), len(v2_parts))
            v1_parts.extend([0] * (max_len - len(v1_parts)))
            v2_parts.extend([0] * (max_len - len(v2_parts)))
            
            for v1, v2 in zip(v1_parts, v2_parts):
                if v1 > v2:
                    return 1
                elif v1 < v2:
                    return -1
            
            return 0
            
        except Exception:
            # 如果版本号格式不标准，按字符串比较
            if version1 > version2:
                return 1
            elif version1 < version2:
                return -1
            return 0
    
    def _calculate_replacement_confidence(self, old_doc: DocumentInfo, new_doc: DocumentInfo) -> float:
        """计算替代置信度"""
        confidence = 0.0
        
        # 文件名相似度 (30%)
        name_similarity = self._calculate_name_similarity(old_doc.name, new_doc.name)
        confidence += name_similarity * 0.3
        
        # 产品ID匹配 (25%)
        if old_doc.product_id == new_doc.product_id:
            confidence += 0.25
        
        # 文档类型匹配 (25%)
        if old_doc.document_type == new_doc.document_type:
            confidence += 0.25
        
        # 时间新旧程度 (20%)
        time_diff = (new_doc.modified_time - old_doc.modified_time).days
        if time_diff > 0:
            time_score = min(time_diff / 30, 1.0)  # 最多30天得满分
            confidence += time_score * 0.2
        
        return min(confidence, 1.0)
    
    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """计算文件名相似度"""
        # 简单的相似度计算
        name1_clean = name1.lower().replace('_', ' ').replace('-', ' ')
        name2_clean = name2.lower().replace('_', ' ').replace('-', ' ')
        
        words1 = set(name1_clean.split())
        words2 = set(name2_clean.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _extract_version_from_filename(self, filename: str) -> str:
        """从文件名中提取版本号"""
        import re
        
        # 常见版本号模式
        patterns = [
            r'v(\d+\.\d+\.\d+)',
            r'version[_-]?(\d+\.\d+\.\d+)',
            r'(\d+\.\d+\.\d+)',
            r'v(\d+\.\d+)',
            r'(\d+\.\d+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return "1.0"  # 默认版本
    
    async def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            async with aiofiles.open(file_path, 'rb') as f:
                async for chunk in f:
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {str(e)}")
            return ""
    
    async def auto_replace_document(self, replacement_result: ReplacementResult) -> bool:
        """自动替换文档"""
        if not replacement_result.found or replacement_result.action != 'replace':
            return False
        
        try:
            old_doc = replacement_result.old_document
            new_doc = replacement_result.new_document
            
            logger.info(f"开始自动替换文档: {old_doc.name} -> {new_doc.name}")
            
            # 1. 备份旧文档
            backup_success = await self._backup_old_document(old_doc)
            if not backup_success:
                logger.error("备份旧文档失败，取消替换操作")
                return False
            
            # 2. 下载/复制新文档
            copy_success = await self._copy_new_document(new_doc, old_doc.path)
            if not copy_success:
                logger.error("复制新文档失败")
                return False
            
            # 3. 更新文档记录
            await self._update_document_record(old_doc, new_doc)
            
            self.stats['auto_replaced'] += 1
            logger.info(f"文档替换成功: {old_doc.name}")
            
            return True
            
        except Exception as e:
            logger.error(f"自动替换文档失败: {str(e)}")
            return False
    
    async def _backup_old_document(self, old_doc: DocumentInfo) -> bool:
        """备份旧文档"""
        try:
            backup_dir = Path(self.data_sources['backup_archive']['base_path']) / "auto_backup"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{old_doc.name}_{timestamp}_backup"
            backup_path = backup_dir / backup_filename
            
            # 复制文件到备份目录
            import shutil
            shutil.copy2(old_doc.path, backup_path)
            
            logger.info(f"旧文档已备份到: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"备份旧文档失败: {str(e)}")
            return False
    
    async def _copy_new_document(self, new_doc: DocumentInfo, target_path: str) -> bool:
        """复制新文档"""
        try:
            if new_doc.source == 'cloud_api':
                # 从云端下载
                return await self._download_from_cloud(new_doc, target_path)
            else:
                # 本地复制
                import shutil
                shutil.copy2(new_doc.path, target_path)
                return True
                
        except Exception as e:
            logger.error(f"复制新文档失败: {str(e)}")
            return False
    
    async def _download_from_cloud(self, new_doc: DocumentInfo, target_path: str) -> bool:
        """从云端下载文档"""
        # 这里应该实现具体的云端下载逻辑
        # 示例实现
        try:
            # 模拟下载过程
            await asyncio.sleep(1)
            
            # 实际实现中应该调用真实的下载API
            # await self._download_file_from_cloud(new_doc.path, target_path)
            
            logger.info(f"从云端下载文档完成: {target_path}")
            return True
            
        except Exception as e:
            logger.error(f"从云端下载文档失败: {str(e)}")
            return False
    
    async def _update_document_record(self, old_doc: DocumentInfo, new_doc: DocumentInfo):
        """更新文档记录"""
        # 这里应该更新数据库中的文档记录
        # 示例实现
        logger.info(f"更新文档记录: {old_doc.id} -> {new_doc.id}")
    
    async def batch_process_replacements(self, document_list: List[DocumentInfo]) -> Dict[str, Any]:
        """批量处理文档替换"""
        logger.info(f"开始批量处理 {len(document_list)} 个文档")
        
        # 1. 扫描作废文档
        obsolete_docs = await self.scan_obsolete_documents(document_list)
        
        # 2. 查找替代文档
        replacement_results = []
        for doc in obsolete_docs:
            result = await self.find_replacement_document(doc)
            replacement_results.append(result)
        
        # 3. 自动替换（高置信度的）
        auto_replaced = []
        manual_review = []
        
        for result in replacement_results:
            if result.found and result.confidence_score >= 0.8:
                success = await self.auto_replace_document(result)
                if success:
                    auto_replaced.append(result)
                else:
                    manual_review.append(result)
            else:
                manual_review.append(result)
        
        # 4. 生成处理报告
        report = {
            'total_documents': len(document_list),
            'obsolete_found': len(obsolete_docs),
            'replacements_found': len([r for r in replacement_results if r.found]),
            'auto_replaced': len(auto_replaced),
            'manual_review_needed': len(manual_review),
            'missing_documents': len([r for r in replacement_results if not r.found]),
            'auto_replaced_list': [{
                'old_name': r.old_document.name,
                'new_name': r.new_document.name,
                'source': r.source,
                'confidence': r.confidence_score
            } for r in auto_replaced],
            'manual_review_list': [{
                'old_name': r.old_document.name,
                'new_name': r.new_document.name if r.new_document else None,
                'reason': r.reason,
                'confidence': r.confidence_score
            } for r in manual_review],
            'processing_stats': self.get_processing_stats()
        }
        
        logger.info(f"批量处理完成: 自动替换 {len(auto_replaced)} 个，需人工审核 {len(manual_review)} 个")
        
        return report
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return self.stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_checked': 0,
            'obsolete_found': 0,
            'replacements_found': 0,
            'auto_replaced': 0,
            'missing_documents': 0,
            'errors': 0
        }

# 使用示例
async def main():
    """使用示例"""
    # 配置
    config = {
        'cloud_api_enabled': True,
        'cloud_api_url': 'https://api.example.com',
        'cloud_api_key': 'your_api_key',
        'local_docs_enabled': True,
        'local_docs_path': './documents',
        'backup_enabled': True,
        'backup_path': './backup',
        'time_threshold_days': 30,
        'size_change_threshold': 0.1,
        'hash_comparison_enabled': True
    }
    
    # 示例文档列表
    sample_docs = [
        DocumentInfo(
            id="doc_001",
            name="门禁控制器_v1.0.pdf",
            path="./documents/access_controller_v1.0.pdf",
            product_id="AC-2000",
            document_type="installation_guide",
            version="1.0",
            modified_time=datetime.now() - timedelta(days=45),
            file_size=1024000,
            hash_value="abc123",
            status="active",
            source="local_docs"
        )
    ]
    
    # 创建管理器
    manager = DocumentReplacementManager(config)
    
    # 批量处理
    report = await manager.batch_process_replacements(sample_docs)
    
    # 显示报告
    print("\n=== 文档替换处理报告 ===")
    print(f"总文档数: {report['total_documents']}")
    print(f"发现作废文档: {report['obsolete_found']}")
    print(f"找到替代文档: {report['replacements_found']}")
    print(f"自动替换: {report['auto_replaced']}")
    print(f"需人工审核: {report['manual_review_needed']}")
    print(f"缺失文档: {report['missing_documents']}")
    
    if report['auto_replaced_list']:
        print("\n自动替换列表:")
        for item in report['auto_replaced_list']:
            print(f"  {item['old_name']} -> {item['new_name']} (置信度: {item['confidence']:.2f})")
    
    if report['manual_review_list']:
        print("\n需人工审核列表:")
        for item in report['manual_review_list']:
            print(f"  {item['old_name']}: {item['reason']}")

if __name__ == "__main__":
    asyncio.run(main())