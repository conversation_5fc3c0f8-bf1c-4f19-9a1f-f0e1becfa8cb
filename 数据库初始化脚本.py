#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品知识库数据库初始化脚本
基于MySQL数据库的产品知识库系统初始化

数据库配置:
- 服务器: ***********
- 用户名: username
- 密码: 123456
- 数据库: product
"""

import psycopg2
import psycopg2.extras
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_init.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseInitializer:
    """
    数据库初始化器
    负责创建表结构、插入初始数据、建立索引等操作
    """
    
    def __init__(self, host: str, username: str, password: str, database: str, port: int = 5432):
        """
        初始化数据库连接配置
        
        Args:
            host (str): 数据库服务器地址
            username (str): 数据库用户名
            password (str): 数据库密码
            database (str): 数据库名称
            port (int): 数据库端口，默认5432
        """
        self.config = {
            'host': host,
            'user': username,
            'password': password,
            'dbname': database,
            'port': port
        }
        self.connection = None
        
    def connect(self) -> bool:
        """
        建立数据库连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.connection = psycopg2.connect(**self.config)
            self.connection.autocommit = True
            logger.info(f"成功连接到数据库 {self.config['host']}:{self.config['port']}/{self.config['dbname']}")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """
        关闭数据库连接
        """
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def execute_sql(self, sql: str, params: Optional[Tuple] = None) -> bool:
        """
        执行SQL语句
        
        Args:
            sql (str): SQL语句
            params (Optional[Tuple]): SQL参数
            
        Returns:
            bool: 执行是否成功
        """
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, params)
                return True
        except Exception as e:
            logger.error(f"SQL执行失败: {sql[:100]}... 错误: {e}")
            return False
    
    def execute_sql_file(self, file_path: str) -> bool:
        """
        执行SQL文件
        
        Args:
            file_path (str): SQL文件路径
            
        Returns:
            bool: 执行是否成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句（以分号为分隔符）
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            success_count = 0
            for sql in sql_statements:
                if self.execute_sql(sql):
                    success_count += 1
                else:
                    logger.warning(f"SQL语句执行失败: {sql[:50]}...")
            
            logger.info(f"SQL文件执行完成: {success_count}/{len(sql_statements)} 条语句成功")
            return success_count == len(sql_statements)
            
        except Exception as e:
            logger.error(f"SQL文件执行失败: {e}")
            return False
    
    def create_tables(self) -> bool:
        """
        创建数据库表结构
        
        Returns:
            bool: 创建是否成功
        """
        logger.info("开始创建数据库表结构...")
        
        # 产品分类表
        product_category_sql = """
        CREATE TABLE IF NOT EXISTS ProductCategory (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            parent_id INTEGER REFERENCES ProductCategory(id) ON DELETE SET NULL,
            level INTEGER NOT NULL DEFAULT 1,
            category_type VARCHAR(50),
            series_name VARCHAR(100),
            collection_status VARCHAR(20) DEFAULT 'pending',
            sort_order INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_productcategory_parent_id ON ProductCategory(parent_id);
        CREATE INDEX IF NOT EXISTS idx_productcategory_level ON ProductCategory(level);
        CREATE INDEX IF NOT EXISTS idx_productcategory_type ON ProductCategory(category_type);
        CREATE INDEX IF NOT EXISTS idx_productcategory_status ON ProductCategory(collection_status);
        
        -- 创建更新时间触发器
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
        
        DROP TRIGGER IF EXISTS update_productcategory_updated_at ON ProductCategory;
        CREATE TRIGGER update_productcategory_updated_at
            BEFORE UPDATE ON ProductCategory
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        """
        
        # 产品表
        product_sql = """
        CREATE TABLE IF NOT EXISTS Product (
            id SERIAL PRIMARY KEY,
            fastgpt_id VARCHAR(50),
            model VARCHAR(100) NOT NULL UNIQUE,
            name VARCHAR(200),
            category_id INTEGER REFERENCES ProductCategory(id) ON DELETE SET NULL,
            series VARCHAR(100),
            business_scope VARCHAR(100),
            features TEXT,
            specifications TEXT,
            software_support TEXT,
            collection_status VARCHAR(20) DEFAULT 'pending',
            collection_priority INTEGER DEFAULT 0,
            tags JSONB,
            metadata JSONB,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_product_fastgpt_id ON Product(fastgpt_id);
        CREATE INDEX IF NOT EXISTS idx_product_category_id ON Product(category_id);
        CREATE INDEX IF NOT EXISTS idx_product_series ON Product(series);
        CREATE INDEX IF NOT EXISTS idx_product_business_scope ON Product(business_scope);
        CREATE INDEX IF NOT EXISTS idx_product_collection_status ON Product(collection_status);
        CREATE INDEX IF NOT EXISTS idx_product_tags ON Product USING GIN(tags);
        CREATE INDEX IF NOT EXISTS idx_product_metadata ON Product USING GIN(metadata);
        
        -- 创建更新时间触发器
        DROP TRIGGER IF EXISTS update_product_updated_at ON Product;
        CREATE TRIGGER update_product_updated_at
            BEFORE UPDATE ON Product
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        """
        
        # 文档类型表
        document_type_sql = """
        CREATE TABLE IF NOT EXISTS DocumentType (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            file_extensions VARCHAR(200),
            processing_priority INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 创建更新时间触发器
        DROP TRIGGER IF EXISTS update_documenttype_updated_at ON DocumentType;
        CREATE TRIGGER update_documenttype_updated_at
            BEFORE UPDATE ON DocumentType
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        """
        
        # 产品文档表
        product_document_sql = """
        CREATE TABLE IF NOT EXISTS ProductDocument (
            id SERIAL PRIMARY KEY,
            product_id INTEGER NOT NULL REFERENCES Product(id) ON DELETE CASCADE,
            document_type_id INTEGER NOT NULL REFERENCES DocumentType(id) ON DELETE RESTRICT,
            title VARCHAR(200) NOT NULL,
            file_name VARCHAR(255),
            file_path VARCHAR(500),
            file_size BIGINT,
            file_hash VARCHAR(64),
            content TEXT,
            markdown_content TEXT,
            extracted_text TEXT,
            tags JSONB,
            metadata JSONB,
            processing_status VARCHAR(20) DEFAULT 'pending',
            vector_status VARCHAR(20) DEFAULT 'pending',
            quality_score DECIMAL(3,2),
            language VARCHAR(10) DEFAULT 'zh',
            version VARCHAR(20) DEFAULT '1.0',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_productdocument_product_id ON ProductDocument(product_id);
        CREATE INDEX IF NOT EXISTS idx_productdocument_document_type_id ON ProductDocument(document_type_id);
        CREATE INDEX IF NOT EXISTS idx_productdocument_file_hash ON ProductDocument(file_hash);
        CREATE INDEX IF NOT EXISTS idx_productdocument_processing_status ON ProductDocument(processing_status);
        CREATE INDEX IF NOT EXISTS idx_productdocument_vector_status ON ProductDocument(vector_status);
        CREATE INDEX IF NOT EXISTS idx_productdocument_quality_score ON ProductDocument(quality_score);
        CREATE INDEX IF NOT EXISTS idx_productdocument_tags ON ProductDocument USING GIN(tags);
        CREATE INDEX IF NOT EXISTS idx_productdocument_metadata ON ProductDocument USING GIN(metadata);
        
        -- 创建更新时间触发器
        DROP TRIGGER IF EXISTS update_productdocument_updated_at ON ProductDocument;
        CREATE TRIGGER update_productdocument_updated_at
            BEFORE UPDATE ON ProductDocument
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        """
        
        # 知识库配置表
        knowledge_config_sql = """
        CREATE TABLE IF NOT EXISTS KnowledgeConfig (
            id SERIAL PRIMARY KEY,
            config_key VARCHAR(100) NOT NULL UNIQUE,
            config_value TEXT,
            config_type VARCHAR(50) DEFAULT 'string',
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 创建更新时间触发器
        DROP TRIGGER IF EXISTS update_knowledgeconfig_updated_at ON KnowledgeConfig;
        CREATE TRIGGER update_knowledgeconfig_updated_at
            BEFORE UPDATE ON KnowledgeConfig
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        """
        
        # 执行建表语句
        tables = [
            ('ProductCategory', product_category_sql),
            ('Product', product_sql),
            ('DocumentType', document_type_sql),
            ('ProductDocument', product_document_sql),
            ('KnowledgeConfig', knowledge_config_sql)
        ]
        
        success_count = 0
        for table_name, sql in tables:
            if self.execute_sql(sql):
                logger.info(f"表 {table_name} 创建成功")
                success_count += 1
            else:
                logger.error(f"表 {table_name} 创建失败")
        
        logger.info(f"表结构创建完成: {success_count}/{len(tables)} 个表成功")
        return success_count == len(tables)
    
    def insert_initial_data(self) -> bool:
        """
        插入初始数据
        
        Returns:
            bool: 插入是否成功
        """
        logger.info("开始插入初始数据...")
        
        # 插入文档类型数据
        document_types = [
            ('用户手册', '产品使用说明和操作指南', '.pdf,.doc,.docx', 1),
            ('技术规格书', '产品技术参数和规格说明', '.pdf,.doc,.docx', 2),
            ('安装指南', '产品安装和配置说明', '.pdf,.doc,.docx', 3),
            ('故障排除', '常见问题和解决方案', '.pdf,.doc,.docx', 4),
            ('软件文档', '配套软件使用说明', '.pdf,.doc,.docx', 5),
            ('API文档', '接口和开发文档', '.pdf,.doc,.docx,.md', 6),
            ('视频教程', '操作演示和培训视频', '.mp4,.avi,.mov', 7),
            ('图片资料', '产品图片和示意图', '.jpg,.png,.gif,.bmp', 8)
        ]
        
        document_type_sql = """
        INSERT INTO DocumentType (name, description, file_extensions, processing_priority)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (name) DO NOTHING
        """
        
        success_count = 0
        for doc_type in document_types:
            if self.execute_sql(document_type_sql, doc_type):
                success_count += 1
        
        logger.info(f"文档类型数据插入完成: {success_count}/{len(document_types)} 条记录")
        
        # 插入知识库配置数据
        configs = [
            ('dify_api_url', 'https://api.dify.ai', 'string', 'Dify API地址'),
            ('fastgpt_api_url', 'https://api.fastgpt.in', 'string', 'FastGPT API地址'),
            ('chunk_size', '1000', 'integer', '文档分块大小'),
            ('chunk_overlap', '200', 'integer', '分块重叠大小'),
            ('vector_dimension', '1536', 'integer', '向量维度'),
            ('similarity_threshold', '0.7', 'float', '相似度阈值'),
            ('max_tokens', '4000', 'integer', '最大token数'),
            ('temperature', '0.7', 'float', '生成温度'),
            ('auto_tag_enabled', 'true', 'boolean', '是否启用自动标签'),
            ('quality_check_enabled', 'true', 'boolean', '是否启用质量检查')
        ]
        
        config_sql = """
        INSERT INTO KnowledgeConfig (config_key, config_value, config_type, description)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (config_key) DO NOTHING
        """
        
        config_success = 0
        for config in configs:
            if self.execute_sql(config_sql, config):
                config_success += 1
        
        logger.info(f"配置数据插入完成: {config_success}/{len(configs)} 条记录")
        
        return success_count == len(document_types) and config_success == len(configs)
    
    def load_migration_data(self) -> bool:
        """
        加载迁移数据（从现有的SQL文件和JSON文件）
        
        Returns:
            bool: 加载是否成功
        """
        logger.info("开始加载迁移数据...")
        
        # 执行数据迁移脚本
        migration_file = 'd:\\sync-db-fastgpt\\数据迁移脚本.sql'
        try:
            if self.execute_sql_file(migration_file):
                logger.info("数据迁移脚本执行成功")
            else:
                logger.warning("数据迁移脚本执行部分失败")
        except Exception as e:
            logger.error(f"数据迁移脚本执行失败: {e}")
        
        # 加载产品数据
        try:
            with open('d:\\sync-db-fastgpt\\allcollections.json', 'r', encoding='utf-8') as f:
                products_data = json.load(f)
            
            product_sql = """
            INSERT INTO Product (fastgpt_id, model, name, series, business_scope, 
                               collection_status, tags, metadata)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (model) DO NOTHING
            """
            
            success_count = 0
            for product in products_data:
                tags = json.dumps({
                    'model': product.get('产品型号', ''),
                    'series': product.get('系列', ''),
                    'business_scope': product.get('业务范围', '')
                }, ensure_ascii=False)
                
                metadata = json.dumps({
                    'source': 'allcollections',
                    'import_time': datetime.now().isoformat(),
                    'original_data': product
                }, ensure_ascii=False)
                
                params = (
                    product.get('id-fastgpt'),
                    product.get('产品型号'),
                    product.get('产品型号'),  # 使用型号作为名称
                    product.get('系列'),
                    product.get('业务范围'),
                    'imported',
                    tags,
                    metadata
                )
                
                if self.execute_sql(product_sql, params):
                    success_count += 1
            
            logger.info(f"产品数据导入完成: {success_count}/{len(products_data)} 条记录")
            
        except Exception as e:
            logger.error(f"产品数据加载失败: {e}")
        
        return True
    
    def create_indexes(self) -> bool:
        """
        创建额外的索引以优化查询性能
        
        Returns:
            bool: 创建是否成功
        """
        logger.info("开始创建优化索引...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_product_tags_model ON Product USING GIN((tags->'model'))",
            "CREATE INDEX IF NOT EXISTS idx_product_metadata_source ON Product USING GIN((metadata->'source'))",
            "CREATE INDEX IF NOT EXISTS idx_document_tags_type ON ProductDocument USING GIN((tags->'type'))",
            "CREATE INDEX IF NOT EXISTS idx_document_language_status ON ProductDocument (language, processing_status)",
            "CREATE INDEX IF NOT EXISTS idx_category_hierarchy ON ProductCategory (parent_id, level, sort_order)",
            "CREATE INDEX IF NOT EXISTS idx_document_quality_time ON ProductDocument (quality_score DESC, created_at DESC)"
        ]
        
        success_count = 0
        for index_sql in indexes:
            try:
                if self.execute_sql(index_sql):
                    success_count += 1
                    logger.info(f"索引创建成功: {index_sql.split()[2]}")
            except Exception as e:
                logger.warning(f"索引创建跳过（可能已存在）: {e}")
        
        logger.info(f"索引创建完成: {success_count}/{len(indexes)} 个索引")
        return True
    
    def initialize_database(self) -> bool:
        """
        完整的数据库初始化流程
        
        Returns:
            bool: 初始化是否成功
        """
        logger.info("开始数据库初始化流程...")
        
        if not self.connect():
            return False
        
        try:
            # 1. 创建表结构
            if not self.create_tables():
                logger.error("表结构创建失败")
                return False
            
            # 2. 插入初始数据
            if not self.insert_initial_data():
                logger.error("初始数据插入失败")
                return False
            
            # 3. 加载迁移数据
            if not self.load_migration_data():
                logger.error("迁移数据加载失败")
                return False
            
            # 4. 创建优化索引
            if not self.create_indexes():
                logger.error("索引创建失败")
                return False
            
            logger.info("数据库初始化完成！")
            return True
            
        except Exception as e:
            logger.error(f"数据库初始化过程中发生错误: {e}")
            return False
        finally:
            self.disconnect()

def main():
    """
    主函数：执行数据库初始化
    """
    # 数据库配置
    db_config = {
        'host': '***********',
        'username': 'username',
        'password': '123456',
        'database': 'product',
        'port': 5432
    }
    
    # 创建初始化器实例
    initializer = DatabaseInitializer(**db_config)
    
    # 执行初始化
    if initializer.initialize_database():
        print("\n✅ 数据库初始化成功！")
        print("\n📊 初始化内容包括:")
        print("   - 创建了5个核心数据表")
        print("   - 插入了基础配置数据")
        print("   - 导入了产品分类和产品数据")
        print("   - 创建了性能优化索引")
        print("\n🚀 现在可以开始使用产品知识库系统了！")
    else:
        print("\n❌ 数据库初始化失败，请检查日志文件 database_init.log")

if __name__ == '__main__':
    main()