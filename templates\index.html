<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FastGPT知识库同步管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-badge {
            font-size: 0.8em;
        }
        .content-preview {
            max-height: 100px;
            overflow-y: auto;
            font-size: 0.9em;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
        }
        .stats-card {
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .loading {
            display: none;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-database"></i> FastGPT知识库同步管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/batch_review">
                    <i class="bi bi-check2-square"></i> 批量审核
                </a>
                <a class="nav-link" href="/sync_logs">
                    <i class="bi bi-journal-text"></i> 同步日志
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 操作按钮区域 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex gap-2">
                    <button class="btn btn-primary" onclick="detectChanges()">
                        <i class="bi bi-search"></i> 检测变更
                        <span class="loading spinner-border spinner-border-sm ms-2" role="status"></span>
                    </button>
                    <button class="btn btn-success" onclick="syncApproved()">
                        <i class="bi bi-cloud-upload"></i> 同步已批准变更
                        <span class="loading spinner-border spinner-border-sm ms-2" role="status"></span>
                    </button>
                    <button class="btn btn-info" onclick="refreshStats()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新统计
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计信息卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card bg-warning text-dark">
                    <div class="card-body text-center">
                        <i class="bi bi-clock-history fs-1"></i>
                        <h4 class="mt-2">{{ stats.pending_approval_count or 0 }}</h4>
                        <p class="mb-0">待审核</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-check-circle fs-1"></i>
                        <h4 class="mt-2" id="synced-count">0</h4>
                        <p class="mb-0">已同步</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-check2-all fs-1"></i>
                        <h4 class="mt-2" id="approved-count">0</h4>
                        <p class="mb-0">已批准</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card bg-danger text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-exclamation-triangle fs-1"></i>
                        <h4 class="mt-2" id="failed-count">0</h4>
                        <p class="mb-0">失败</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近同步时间 -->
        {% if stats.last_sync_time %}
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i>
            最近同步时间: {{ stats.last_sync_time | datetime_format }}
        </div>
        {% endif %}

        <!-- 待审核变更列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-list-check"></i> 待审核变更
                </h5>
                <span class="badge bg-warning">{{ pending_changes|length }} 项</span>
            </div>
            <div class="card-body">
                {% if pending_changes %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%">
                                    <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                                </th>
                                <th width="15%">数据源</th>
                                <th width="25%">标题</th>
                                <th width="10%">变更类型</th>
                                <th width="20%">内容预览</th>
                                <th width="15%">创建时间</th>
                                <th width="10%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for change in pending_changes %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="change-checkbox" value="{{ change.id }}">
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ change.source_type }}</span>
                                </td>
                                <td>
                                    <a href="/change/{{ change.id }}" class="text-decoration-none">
                                        {{ change.title[:50] }}{% if change.title|length > 50 %}...{% endif %}
                                    </a>
                                </td>
                                <td>
                                    {% if change.change_type == 'create' %}
                                        <span class="badge bg-success">新增</span>
                                    {% elif change.change_type == 'update' %}
                                        <span class="badge bg-warning">更新</span>
                                    {% elif change.change_type == 'delete' %}
                                        <span class="badge bg-danger">删除</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="content-preview">
                                        {{ change.content[:100] }}{% if change.content|length > 100 %}...{% endif %}
                                    </div>
                                </td>
                                <td>
                                    <small>{{ change.created_at | datetime_format }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-success" onclick="approveChange({{ change.id }})" title="批准">
                                            <i class="bi bi-check"></i>
                                        </button>
                                        <a href="/change/{{ change.id }}" class="btn btn-outline-info" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 批量操作按钮 -->
                <div class="mt-3">
                    <button class="btn btn-success" onclick="approveSelected()" id="approve-selected-btn" disabled>
                        <i class="bi bi-check-all"></i> 批准选中项
                    </button>
                    <span class="ms-2 text-muted" id="selected-count">已选择 0 项</span>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-check-circle text-success" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">暂无待审核变更</h5>
                    <p class="text-muted">所有变更都已处理完成</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notification-toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="bi bi-info-circle text-primary me-2"></i>
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toast-message">
                <!-- 消息内容 -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示通知
        function showNotification(message, type = 'info') {
            const toast = document.getElementById('notification-toast');
            const toastMessage = document.getElementById('toast-message');
            const toastHeader = toast.querySelector('.toast-header i');
            
            toastMessage.textContent = message;
            
            // 设置图标和颜色
            toastHeader.className = `bi me-2`;
            if (type === 'success') {
                toastHeader.classList.add('bi-check-circle', 'text-success');
            } else if (type === 'error') {
                toastHeader.classList.add('bi-exclamation-triangle', 'text-danger');
            } else {
                toastHeader.classList.add('bi-info-circle', 'text-primary');
            }
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        // 显示/隐藏加载状态
        function setLoading(button, loading) {
            const spinner = button.querySelector('.loading');
            if (loading) {
                button.disabled = true;
                spinner.style.display = 'inline-block';
            } else {
                button.disabled = false;
                spinner.style.display = 'none';
            }
        }

        // 检测变更
        async function detectChanges() {
            const button = event.target.closest('button');
            setLoading(button, true);
            
            try {
                const response = await fetch('/api/detect_changes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification(`检测完成: 新增${result.data.new_items}项, 更新${result.data.updated_items}项, 删除${result.data.deleted_items}项`, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('检测失败: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('检测失败: ' + error.message, 'error');
            } finally {
                setLoading(button, false);
            }
        }

        // 同步已批准变更
        async function syncApproved() {
            const button = event.target.closest('button');
            setLoading(button, true);
            
            try {
                const response = await fetch('/api/sync_approved', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification(`同步完成: 成功${result.data.synced}项, 失败${result.data.failed}项`, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('同步失败: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('同步失败: ' + error.message, 'error');
            } finally {
                setLoading(button, false);
            }
        }

        // 批准单个变更
        async function approveChange(changeId) {
            try {
                const response = await fetch('/api/approve_changes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        change_ids: [changeId],
                        approved_by: 'admin'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('批准成功', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('批准失败: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('批准失败: ' + error.message, 'error');
            }
        }

        // 批准选中项
        async function approveSelected() {
            const checkboxes = document.querySelectorAll('.change-checkbox:checked');
            const changeIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
            
            if (changeIds.length === 0) {
                showNotification('请选择要批准的变更', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/approve_changes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        change_ids: changeIds,
                        approved_by: 'admin'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification(`批准完成: 成功${result.data.approved}项, 失败${result.data.failed}项`, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('批准失败: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('批准失败: ' + error.message, 'error');
            }
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.change-checkbox');
            
            checkboxes.forEach(cb => {
                cb.checked = selectAll.checked;
            });
            
            updateSelectedCount();
        }

        // 更新选中数量
        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('.change-checkbox:checked');
            const count = checkboxes.length;
            
            document.getElementById('selected-count').textContent = `已选择 ${count} 项`;
            document.getElementById('approve-selected-btn').disabled = count === 0;
        }

        // 刷新统计信息
        async function refreshStats() {
            try {
                const response = await fetch('/api/statistics');
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    
                    // 更新统计数字
                    let syncedCount = 0, approvedCount = 0, failedCount = 0;
                    
                    if (stats.status_breakdown) {
                        stats.status_breakdown.forEach(item => {
                            if (item.sync_status === 'synced') {
                                syncedCount += item.count;
                            } else if (item.sync_status === 'approved') {
                                approvedCount += item.count;
                            } else if (item.sync_status === 'failed') {
                                failedCount += item.count;
                            }
                        });
                    }
                    
                    document.getElementById('synced-count').textContent = syncedCount;
                    document.getElementById('approved-count').textContent = approvedCount;
                    document.getElementById('failed-count').textContent = failedCount;
                    
                    showNotification('统计信息已刷新', 'success');
                } else {
                    showNotification('刷新失败: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('刷新失败: ' + error.message, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 为复选框添加事件监听
            document.querySelectorAll('.change-checkbox').forEach(cb => {
                cb.addEventListener('change', updateSelectedCount);
            });
            
            // 初始化统计信息
            refreshStats();
        });
    </script>
</body>
</html>