# -*- coding: utf-8 -*-
"""
产品数据处理器
功能：
1. 初始化数据库
2. 导入allcollections.json数据并标记为作废附件
3. 从云商API获取产品数据并下载附件
4. 处理各种格式的附件文件（Word、Excel、PDF、PPT）
"""

import psycopg2
import psycopg2.extras
import requests
import json
import os
import logging
import hashlib
import mimetypes
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import urlparse, urljoin
from pathlib import Path
import time
from config import DATABASE_CONFIG, API_CONFIG, FILE_PATHS, LOG_CONFIG, PROCESS_CONFIG

# 配置日志
def setup_logging():
    """设置日志配置"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, 'product_processor.log')
    
    # 配置日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    
    # 配置根日志器
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logging.getLogger(__name__)

# 初始化日志
logger = setup_logging()

class ProductDataProcessor:
    """
    产品数据处理器主类
    负责数据库初始化、数据导入和API数据获取
    """
    
    def __init__(self, db_config: Dict[str, Any], api_config: Dict[str, str], local_data_config: Dict[str, str] = None):
        """
        初始化产品数据处理器
        
        Args:
            db_config (Dict): 数据库配置
            api_config (Dict): API配置
            local_data_config (Dict): 本地数据配置
        """
        self.db_config = db_config
        self.api_config = api_config
        self.connection = None
        self.session = requests.Session()
        self.token = None
        
        # 创建下载目录
        download_dir = api_config.get('download_dir', './downloads')
        if not os.path.exists(download_dir):
            os.makedirs(download_dir)
            logger.info(f"创建下载目录: {download_dir}")
        
        self.download_dir = download_dir
        
    def connect_database(self) -> bool:
        """
        连接数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.connection.autocommit = True
            logger.info(f"成功连接到数据库 {self.db_config['host']}")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def disconnect_database(self):
        """
        断开数据库连接
        """
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def execute_sql(self, sql: str, params: Optional[Tuple] = None) -> bool:
        """
        执行SQL语句
        
        Args:
            sql (str): SQL语句
            params (Optional[Tuple]): SQL参数
            
        Returns:
            bool: 执行是否成功
        """
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, params)
                return True
        except Exception as e:
            logger.error(f"SQL执行失败: {sql[:100]}... 错误: {e}")
            return False
    
    def fetch_data(self, sql: str, params: Optional[Tuple] = None) -> List[Dict]:
        """
        查询数据
        
        Args:
            sql (str): SQL语句
            params (Optional[Tuple]): SQL参数
            
        Returns:
            List[Dict]: 查询结果
        """
        try:
            with self.connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"数据查询失败: {e}")
            return []
    
    def initialize_database(self) -> bool:
        """
        初始化数据库表结构
        
        Returns:
            bool: 初始化是否成功
        """
        logger.info("开始初始化数据库表结构")
        
        # 创建产品分类表
        create_category_sql = """
        CREATE TABLE IF NOT EXISTS ProductCategory (
            category_id SERIAL PRIMARY KEY,
            category_name VARCHAR(100) NOT NULL,
            parent_id INTEGER REFERENCES ProductCategory(category_id),
            category_level INTEGER DEFAULT 1,
            sort_order INTEGER DEFAULT 0,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        # 创建产品主表
        create_product_sql = """
        CREATE TABLE IF NOT EXISTS Product (
            product_id VARCHAR(50) PRIMARY KEY,
            product_category VARCHAR(50) NOT NULL,
            product_type VARCHAR(50) NOT NULL,
            product_series VARCHAR(50),
            product_model VARCHAR(100) NOT NULL,
            product_name VARCHAR(200),
            description TEXT,
            introduction TEXT,
            small_img VARCHAR(500),
            banner VARCHAR(500),
            attribute VARCHAR(100),
            label VARCHAR(100),
            show_for TEXT,
            use_to TEXT,
            price DECIMAL(10,2) DEFAULT 0.00,
            like_count INTEGER DEFAULT 0,
            favorite_count INTEGER DEFAULT 0,
            view_count INTEGER DEFAULT 0,
            is_suggest BOOLEAN DEFAULT FALSE,
            guide TEXT,
            details TEXT,
            other_attachments TEXT,
            site_id INTEGER,
            fastgpt_id VARCHAR(100),
            document_name VARCHAR(500),
            material_usage VARCHAR(50),
            business_scope VARCHAR(255),
            launch_time TIMESTAMP,
            supported_software TEXT,
            collection_status VARCHAR(50) DEFAULT '未开始',
            external_id INTEGER,
            sync_status VARCHAR(20) DEFAULT 'pending',
            last_sync_time TIMESTAMP,
            data_source VARCHAR(20) DEFAULT 'manual',
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        # 创建资料类型表
        create_document_type_sql = """
        CREATE TABLE IF NOT EXISTS DocumentType (
            type_id SERIAL PRIMARY KEY,
            type_name VARCHAR(100) NOT NULL,
            type_code VARCHAR(50) UNIQUE NOT NULL,
            description TEXT,
            is_required BOOLEAN DEFAULT FALSE,
            sort_order INTEGER DEFAULT 0,
            document_category VARCHAR(50),
            priority INTEGER DEFAULT 0,
            template_path VARCHAR(500),
            file_extensions VARCHAR(255),
            max_file_size BIGINT,
            quality_requirements JSONB,
            review_required BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        # 创建产品资料表
        create_product_document_sql = """
        CREATE TABLE IF NOT EXISTS ProductDocument (
            document_id VARCHAR(50) PRIMARY KEY,
            product_id VARCHAR(50) NOT NULL,
            type_id INTEGER,
            document_name VARCHAR(500) NOT NULL,
            file_path VARCHAR(1000),
            file_size BIGINT,
            file_format VARCHAR(20),
            file_extension VARCHAR(10),
            file_type VARCHAR(50),
            mime_type VARCHAR(100),
            file_hash VARCHAR(64),
            version VARCHAR(50),
            language VARCHAR(10) DEFAULT 'zh-CN',
            business_unit VARCHAR(100),
            consultation_type VARCHAR(50),
            upload_date TIMESTAMP,
            fastgpt_id VARCHAR(100),
            original_filename VARCHAR(500),
            material_usage VARCHAR(50),
            business_scope VARCHAR(255),
            collection_status VARCHAR(50) DEFAULT '未收集',
            collector VARCHAR(100),
            collection_date TIMESTAMP,
            review_status VARCHAR(50),
            reviewer VARCHAR(100),
            review_date TIMESTAMP,
            review_comments TEXT,
            data_source VARCHAR(20) DEFAULT 'manual',
            is_deprecated BOOLEAN DEFAULT FALSE,
            local_file_path VARCHAR(1000),
            relative_path VARCHAR(500),
            metadata JSONB,
            status VARCHAR(20) DEFAULT 'draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES Product(product_id),
            FOREIGN KEY (type_id) REFERENCES DocumentType(type_id)
        );
        """
        
        # 创建软件关联表
        create_product_software_sql = """
        CREATE TABLE IF NOT EXISTS ProductSoftware (
            id SERIAL PRIMARY KEY,
            product_id VARCHAR(50) NOT NULL,
            software_name VARCHAR(100) NOT NULL,
            software_version VARCHAR(50),
            compatibility_level VARCHAR(20) DEFAULT 'full',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES Product(product_id)
        );
        """
        
        # 执行建表语句
        tables = [
            ("ProductCategory", create_category_sql),
            ("Product", create_product_sql),
            ("DocumentType", create_document_type_sql),
            ("ProductDocument", create_product_document_sql),
            ("ProductSoftware", create_product_software_sql)
        ]
        
        for table_name, sql in tables:
            if self.execute_sql(sql):
                logger.info(f"表 {table_name} 创建成功")
            else:
                logger.error(f"表 {table_name} 创建失败")
                return False
        
        # 创建索引
        self._create_indexes()
        
        # 插入初始数据
        self._insert_initial_data()
        
        logger.info("数据库初始化完成")
        return True
    
    def _create_indexes(self):
        """
        创建数据库索引
        """
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_product_category ON Product(product_category);",
            "CREATE INDEX IF NOT EXISTS idx_product_model ON Product(product_model);",
            "CREATE INDEX IF NOT EXISTS idx_product_fastgpt_id ON Product(fastgpt_id);",
            "CREATE INDEX IF NOT EXISTS idx_product_data_source ON Product(data_source);",
            "CREATE INDEX IF NOT EXISTS idx_document_product_id ON ProductDocument(product_id);",
            "CREATE INDEX IF NOT EXISTS idx_document_fastgpt_id ON ProductDocument(fastgpt_id);",
            "CREATE INDEX IF NOT EXISTS idx_document_data_source ON ProductDocument(data_source);",
            "CREATE INDEX IF NOT EXISTS idx_document_metadata ON ProductDocument USING GIN(metadata);"
        ]
        
        for index_sql in indexes:
            self.execute_sql(index_sql)
    
    def _insert_initial_data(self):
        """
        插入初始数据
        """
        # 插入文档类型
        document_types = [
            ('brochure', '宣传彩页', '产品宣传资料', True, '彩页'),
            ('quick_guide', '入门指南', '产品快速入门指南', True, '入门指南'),
            ('user_manual', '用户手册', '产品详细使用手册', True, '用户手册'),
            ('tech_spec', '技术规格书', '产品技术规格说明', True, '技术文档'),
            ('install_guide', '安装指南', '产品安装说明', False, '技术文档'),
            ('troubleshooting', '故障排除指南', '常见问题解决方案', False, '技术文档')
        ]
        
        for type_code, type_name, description, is_required, category in document_types:
            sql = """
            INSERT INTO DocumentType (type_code, type_name, description, is_required, document_category)
            VALUES (%s, %s, %s, %s, %s)
            ON CONFLICT (type_code) DO NOTHING;
            """
            self.execute_sql(sql, (type_code, type_name, description, is_required, category))
    
    def import_allcollections_data(self, json_file_path: str) -> bool:
        """
        导入allcollections.json数据并标记为作废附件
        
        Args:
            json_file_path (str): JSON文件路径
            
        Returns:
            bool: 导入是否成功
        """
        logger.info(f"开始导入 {json_file_path} 数据")
        
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            imported_count = 0
            for item in data:
                if self._import_single_collection_item(item):
                    imported_count += 1
            
            logger.info(f"成功导入 {imported_count} 条allcollections数据")
            return True
            
        except Exception as e:
            logger.error(f"导入allcollections数据失败: {e}")
            return False
    
    def _import_single_collection_item(self, item: Dict) -> bool:
        """
        导入单个collection数据项
        
        Args:
            item (Dict): 数据项
            
        Returns:
            bool: 导入是否成功
        """
        try:
            # 生成产品ID
            product_id = f"legacy_{item.get('产品型号', 'unknown')}_{item.get('id-fastgpt', '')}"
            
            # 插入或更新产品信息
            product_sql = """
            INSERT INTO Product (
                product_id, product_model, product_name, business_scope,
                launch_time, supported_software, fastgpt_id, document_name,
                material_usage, data_source, status, created_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (product_id) DO UPDATE SET
                product_name = EXCLUDED.product_name,
                business_scope = EXCLUDED.business_scope,
                supported_software = EXCLUDED.supported_software,
                updated_at = CURRENT_TIMESTAMP;
            """
            
            launch_time = None
            if item.get('产品上市时间'):
                try:
                    launch_time = datetime.strptime(item['产品上市时间'], '%Y-%m-%d %H:%M:%S')
                except:
                    pass
            
            self.execute_sql(product_sql, (
                product_id,
                item.get('产品型号', ''),
                item.get('产品名称', ''),
                item.get('产品业务范畴', ''),
                launch_time,
                item.get('产品所支持的软件', ''),
                item.get('id-fastgpt', ''),
                item.get('name', ''),
                item.get('资料用途', ''),
                'legacy',  # 标记为历史数据
                'deprecated',  # 标记为作废
                datetime.now()
            ))
            
            # 插入文档记录（标记为作废附件）
            document_id = f"doc_{item.get('id-fastgpt', '')}"
            document_sql = """
            INSERT INTO ProductDocument (
                document_id, product_id, document_name, fastgpt_id,
                material_usage, business_scope, data_source,
                is_deprecated, status, created_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (document_id) DO UPDATE SET
                document_name = EXCLUDED.document_name,
                is_deprecated = TRUE,
                updated_at = CURRENT_TIMESTAMP;
            """
            
            self.execute_sql(document_sql, (
                document_id,
                product_id,
                item.get('name', ''),
                item.get('id-fastgpt', ''),
                item.get('资料用途', ''),
                item.get('产品业务范畴', ''),
                'legacy',
                True,  # 标记为作废附件
                'deprecated',
                datetime.now()
            ))
            
            return True
            
        except Exception as e:
            logger.error(f"导入单个collection项失败: {e}")
            return False
    
    def login_yunshang_api(self) -> bool:
        """
        登录云商API获取token
        
        Returns:
            bool: 登录是否成功
        """
        login_url = f"{self.api_config['base_url']}/api/loginPlatform"
        login_data = {
            "username": self.api_config['username'],
            "password": self.api_config['password']
        }
        
        try:
            response = self.session.post(login_url, json=login_data)
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 200:
                self.token = result.get('token')
                self.session.headers.update({'Authorization': self.token})
                logger.info("云商API登录成功")
                return True
            else:
                logger.error(f"云商API登录失败: {result.get('msg')}")
                return False
                
        except Exception as e:
            logger.error(f"云商API登录异常: {e}")
            return False
    
    def fetch_yunshang_products(self) -> List[Dict]:
        """
        获取云商产品列表
        
        Returns:
            List[Dict]: 产品列表
        """
        if not self.token:
            logger.error("未登录云商API")
            return []
        
        products_url = f"{self.api_config['base_url']}/api/business/product/list"
        all_products = []
        current_page = 1
        page_size = 50
        
        while True:
            params = {
                'current': str(current_page),
                'pageSize': str(page_size)
            }
            
            try:
                response = self.session.get(products_url, params=params)
                response.raise_for_status()
                
                result = response.json()
                if result.get('code') == 200:
                    data = result.get('data', {})
                    rows = data.get('rows', [])
                    
                    if not rows:
                        break
                    
                    all_products.extend(rows)
                    logger.info(f"获取第 {current_page} 页产品数据，共 {len(rows)} 条")
                    
                    # 检查是否还有更多数据
                    total = data.get('total', 0)
                    if len(all_products) >= total:
                        break
                    
                    current_page += 1
                    time.sleep(0.5)  # 避免请求过快
                else:
                    logger.error(f"获取产品列表失败: {result.get('msg')}")
                    break
                    
            except Exception as e:
                logger.error(f"获取产品列表异常: {e}")
                break
        
        logger.info(f"总共获取 {len(all_products)} 个产品")
        return all_products
    
    def download_attachment(self, url: str, product_id: str, filename: str) -> Optional[str]:
        """
        下载附件文件
        
        Args:
            url (str): 附件URL
            product_id (str): 产品ID
            filename (str): 文件名
            
        Returns:
            Optional[str]: 本地文件路径
        """
        if not url:
            return None
        
        try:
            # 创建产品专用目录
            product_dir = Path(self.download_dir) / product_id
            product_dir.mkdir(exist_ok=True)
            
            # 下载文件
            response = self.session.get(url, stream=True)
            response.raise_for_status()
            
            # 确定文件扩展名
            content_type = response.headers.get('content-type', '')
            if not filename or '.' not in filename:
                ext = mimetypes.guess_extension(content_type) or '.bin'
                filename = f"attachment_{int(time.time())}{ext}"
            
            file_path = product_dir / filename
            
            # 保存文件
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"附件下载成功: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"下载附件失败 {url}: {e}")
            return None
    
    def extract_file_metadata(self, file_path: str) -> Dict:
        """
        提取文件元数据
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            Dict: 文件元数据
        """
        metadata = {
            'file_size': 0,
            'file_hash': '',
            'mime_type': '',
            'created_time': None,
            'modified_time': None
        }
        
        try:
            file_path_obj = Path(file_path)
            if file_path_obj.exists():
                stat = file_path_obj.stat()
                metadata['file_size'] = stat.st_size
                metadata['created_time'] = datetime.fromtimestamp(stat.st_ctime)
                metadata['modified_time'] = datetime.fromtimestamp(stat.st_mtime)
                
                # 计算文件哈希
                with open(file_path, 'rb') as f:
                    file_hash = hashlib.md5()
                    for chunk in iter(lambda: f.read(4096), b""):
                        file_hash.update(chunk)
                    metadata['file_hash'] = file_hash.hexdigest()
                
                # 获取MIME类型
                metadata['mime_type'] = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
                
        except Exception as e:
            logger.error(f"提取文件元数据失败 {file_path}: {e}")
        
        return metadata
    
    def import_yunshang_product(self, product_data: Dict) -> bool:
        """
        导入单个云商产品数据
        
        Args:
            product_data (Dict): 产品数据
            
        Returns:
            bool: 导入是否成功
        """
        try:
            product_id = f"yunshang_{product_data.get('id')}"
            
            # 插入产品信息
            product_sql = """
            INSERT INTO Product (
                product_id, product_name, product_model, description,
                introduction, small_img, banner, attribute, label,
                show_for, use_to, price, like_count, favorite_count,
                view_count, is_suggest, guide, details, other_attachments,
                site_id, external_id, data_source, status, created_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (product_id) DO UPDATE SET
                product_name = EXCLUDED.product_name,
                description = EXCLUDED.description,
                introduction = EXCLUDED.introduction,
                small_img = EXCLUDED.small_img,
                banner = EXCLUDED.banner,
                price = EXCLUDED.price,
                like_count = EXCLUDED.like_count,
                favorite_count = EXCLUDED.favorite_count,
                view_count = EXCLUDED.view_count,
                updated_at = CURRENT_TIMESTAMP;
            """
            
            self.execute_sql(product_sql, (
                product_id,
                product_data.get('name', ''),
                product_data.get('spec', ''),
                product_data.get('description', ''),
                product_data.get('introduction', ''),
                product_data.get('smallImg', ''),
                product_data.get('banner', ''),
                product_data.get('attribute', ''),
                product_data.get('label', ''),
                product_data.get('showFor', ''),
                product_data.get('useTo', ''),
                float(product_data.get('price', 0)),
                int(product_data.get('likeCount', 0)),
                int(product_data.get('favoriteCount', 0)),
                int(product_data.get('count', 0)),
                product_data.get('isSuggest') == '0',
                product_data.get('guide', ''),
                product_data.get('details', ''),
                product_data.get('other', ''),
                int(product_data.get('siteId', 0)),
                int(product_data.get('id')),
                'yunshang',
                'active',
                datetime.now()
            ))
            
            # 处理附件
            other_attachments = product_data.get('other', '')
            if other_attachments:
                self._process_product_attachments(product_id, other_attachments)
            
            return True
            
        except Exception as e:
            logger.error(f"导入云商产品失败: {e}")
            return False
    
    def _process_product_attachments(self, product_id: str, attachments_str: str):
        """
        处理产品附件
        
        Args:
            product_id (str): 产品ID
            attachments_str (str): 附件字符串
        """
        if not attachments_str:
            return
        
        # 解析附件URL（可能是逗号分隔的多个URL）
        attachment_urls = [url.strip() for url in attachments_str.split(',') if url.strip()]
        
        for i, url in enumerate(attachment_urls):
            try:
                # 生成文件名
                parsed_url = urlparse(url)
                filename = os.path.basename(parsed_url.path) or f"attachment_{i+1}"
                
                # 下载附件
                local_path = self.download_attachment(url, product_id, filename)
                if local_path:
                    # 提取文件元数据
                    metadata = self.extract_file_metadata(local_path)
                    file_type_info = self.get_file_type_info(local_path)
                    
                    # 插入文档记录
                    document_id = f"{product_id}_attachment_{i+1}"
                    document_sql = """
                    INSERT INTO ProductDocument (
                        document_id, product_id, document_name, file_path,
                        file_size, file_format, file_extension, file_type, mime_type,
                        file_hash, original_filename, data_source, metadata, status, created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (document_id) DO UPDATE SET
                        file_path = EXCLUDED.file_path,
                        file_size = EXCLUDED.file_size,
                        file_hash = EXCLUDED.file_hash,
                        metadata = EXCLUDED.metadata,
                        updated_at = CURRENT_TIMESTAMP;
                    """
                    
                    self.execute_sql(document_sql, (
                        document_id,
                        product_id,
                        filename,
                        local_path,
                        metadata['file_size'],
                        file_type_info['format'],
                        file_type_info['extension'],
                        file_type_info['type'],
                        file_type_info['mime_type'],
                        metadata['file_hash'],
                        filename,
                        'yunshang',
                        json.dumps(metadata, default=str),
                        'published',
                        datetime.now()
                    ))
                    
            except Exception as e:
                logger.error(f"处理附件失败 {url}: {e}")
    
    def get_file_type_info(self, file_path: str) -> Dict[str, str]:
        """
        获取文件类型信息
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            Dict[str, str]: 文件类型信息
        """
        file_path_obj = Path(file_path)
        extension = file_path_obj.suffix.lower()
        mime_type = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
        
        # 文件类型映射
        type_mapping = {
            '.pdf': 'PDF文档',
            '.doc': 'Word文档',
            '.docx': 'Word文档',
            '.xls': 'Excel表格',
            '.xlsx': 'Excel表格',
            '.ppt': 'PowerPoint演示',
            '.pptx': 'PowerPoint演示',
            '.txt': '文本文档',
            '.jpg': '图片文件',
            '.jpeg': '图片文件',
            '.png': '图片文件',
            '.gif': '图片文件',
            '.bmp': '图片文件',
            '.zip': '压缩文件',
            '.rar': '压缩文件',
            '.7z': '压缩文件',
            '.mp4': '视频文件',
            '.avi': '视频文件',
            '.mov': '视频文件',
            '.mp3': '音频文件',
            '.wav': '音频文件'
        }
        
        file_type = type_mapping.get(extension, '其他文件')
        
        return {
            'extension': extension,
            'format': extension.replace('.', '').upper() if extension else 'UNKNOWN',
            'type': file_type,
            'mime_type': mime_type
        }
    
    def load_product_structure_data(self, json_file_path: str) -> List[Dict]:
        """
        加载产品结构数据
        
        Args:
            json_file_path (str): JSON文件路径
            
        Returns:
            List[Dict]: 产品结构数据
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载产品结构数据，共 {len(data)} 条记录")
            return data
        except Exception as e:
            logger.error(f"加载产品结构数据失败: {e}")
            return []
    
    def scan_local_product_files(self, base_path: str, product_structure: List[Dict]) -> bool:
        """
        扫描本地产品文件
        
        Args:
            base_path (str): 基础路径
            product_structure (List[Dict]): 产品结构数据
            
        Returns:
            bool: 扫描是否成功
        """
        logger.info(f"开始扫描本地产品文件，基础路径: {base_path}")
        
        base_path_obj = Path(base_path)
        if not base_path_obj.exists():
            logger.error(f"基础路径不存在: {base_path}")
            return False
        
        processed_count = 0
        for product_info in product_structure:
            try:
                # 构建产品路径：基础路径/产品分类/系列/产品型号
                product_category = product_info.get('产品分类', '')
                product_series = product_info.get('系列', '')
                product_model = product_info.get('产品型号', '')
                
                if not all([product_category, product_series, product_model]):
                    logger.warning(f"产品信息不完整，跳过: {product_info}")
                    continue
                
                # 构建产品文件夹路径
                product_path = base_path_obj / product_category / product_series / product_model
                
                if product_path.exists() and product_path.is_dir():
                    # 扫描该产品目录下的所有文件
                    files_processed = self._scan_product_directory(product_path, product_info)
                    if files_processed > 0:
                        processed_count += 1
                        logger.info(f"处理产品 {product_model}，找到 {files_processed} 个文件")
                else:
                    logger.debug(f"产品路径不存在: {product_path}")
                    
            except Exception as e:
                logger.error(f"处理产品失败 {product_info.get('产品型号', 'unknown')}: {e}")
        
        logger.info(f"本地文件扫描完成，共处理 {processed_count} 个产品")
        return True
    
    def _scan_product_directory(self, product_path: Path, product_info: Dict) -> int:
        """
        扫描单个产品目录
        
        Args:
            product_path (Path): 产品路径
            product_info (Dict): 产品信息
            
        Returns:
            int: 处理的文件数量
        """
        files_processed = 0
        product_model = product_info.get('产品型号', '')
        product_id = f"local_{product_model}"
        
        # 确保产品记录存在
        self._ensure_product_exists(product_id, product_info)
        
        # 递归扫描所有文件
        for file_path in product_path.rglob('*'):
            if file_path.is_file():
                try:
                    # 处理单个文件
                    if self._process_local_file(file_path, product_id, product_path, product_info):
                        files_processed += 1
                except Exception as e:
                    logger.error(f"处理文件失败 {file_path}: {e}")
        
        return files_processed
    
    def _ensure_product_exists(self, product_id: str, product_info: Dict):
        """
        确保产品记录存在
        
        Args:
            product_id (str): 产品ID
            product_info (Dict): 产品信息
        """
        product_sql = """
        INSERT INTO Product (
            product_id, product_category, product_type, product_series,
            product_model, product_name, collection_status, data_source,
            status, created_at
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (product_id) DO UPDATE SET
            collection_status = EXCLUDED.collection_status,
            updated_at = CURRENT_TIMESTAMP;
        """
        
        self.execute_sql(product_sql, (
            product_id,
            product_info.get('产品分类', ''),
            product_info.get('类别', ''),
            product_info.get('系列', ''),
            product_info.get('产品型号', ''),
            product_info.get('产品型号', ''),  # 使用型号作为名称
            product_info.get('收集情况', '未开始'),
            'local',
            'active',
            datetime.now()
        ))
    
    def _process_local_file(self, file_path: Path, product_id: str, product_base_path: Path, product_info: Dict) -> bool:
        """
        处理单个本地文件
        
        Args:
            file_path (Path): 文件路径
            product_id (str): 产品ID
            product_base_path (Path): 产品基础路径
            product_info (Dict): 产品信息
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 计算相对路径
            relative_path = file_path.relative_to(product_base_path)
            
            # 提取文件元数据
            metadata = self.extract_file_metadata(str(file_path))
            file_type_info = self.get_file_type_info(str(file_path))
            
            # 根据文件名和路径推断文档类型
            document_type_id = self._infer_document_type(file_path.name, str(relative_path))
            
            # 生成文档ID
            file_hash_short = metadata['file_hash'][:8] if metadata['file_hash'] else 'unknown'
            document_id = f"{product_id}_{file_hash_short}"
            
            # 插入文档记录
            document_sql = """
            INSERT INTO ProductDocument (
                document_id, product_id, type_id, document_name, local_file_path,
                relative_path, file_size, file_format, file_extension, file_type,
                mime_type, file_hash, original_filename, data_source, metadata,
                status, created_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (document_id) DO UPDATE SET
                local_file_path = EXCLUDED.local_file_path,
                file_size = EXCLUDED.file_size,
                file_hash = EXCLUDED.file_hash,
                metadata = EXCLUDED.metadata,
                updated_at = CURRENT_TIMESTAMP;
            """
            
            self.execute_sql(document_sql, (
                document_id,
                product_id,
                document_type_id,
                file_path.name,
                str(file_path),
                str(relative_path),
                metadata['file_size'],
                file_type_info['format'],
                file_type_info['extension'],
                file_type_info['type'],
                file_type_info['mime_type'],
                metadata['file_hash'],
                file_path.name,
                'local',
                json.dumps({
                    **metadata,
                    'relative_path': str(relative_path),
                    'product_info': product_info
                }, default=str),
                'published',
                datetime.now()
            ))
            
            return True
            
        except Exception as e:
            logger.error(f"处理本地文件失败 {file_path}: {e}")
            return False
    
    def _infer_document_type(self, filename: str, relative_path: str) -> Optional[int]:
        """
        根据文件名和路径推断文档类型
        
        Args:
            filename (str): 文件名
            relative_path (str): 相对路径
            
        Returns:
            Optional[int]: 文档类型ID
        """
        filename_lower = filename.lower()
        path_lower = relative_path.lower()
        
        # 根据文件名关键词推断类型
        if any(keyword in filename_lower for keyword in ['彩页', 'brochure', '宣传']):
            return self._get_document_type_id('brochure')
        elif any(keyword in filename_lower for keyword in ['入门', 'quick', 'start', '快速']):
            return self._get_document_type_id('quick_guide')
        elif any(keyword in filename_lower for keyword in ['用户手册', 'user', 'manual', '使用说明']):
            return self._get_document_type_id('user_manual')
        elif any(keyword in filename_lower for keyword in ['技术', 'tech', 'spec', '规格']):
            return self._get_document_type_id('tech_spec')
        elif any(keyword in filename_lower for keyword in ['安装', 'install', 'setup']):
            return self._get_document_type_id('install_guide')
        elif any(keyword in filename_lower for keyword in ['故障', 'trouble', 'problem', '问题']):
            return self._get_document_type_id('troubleshooting')
        
        return None
    
    def _get_document_type_id(self, type_code: str) -> Optional[int]:
        """
        根据类型代码获取文档类型ID
        
        Args:
            type_code (str): 类型代码
            
        Returns:
            Optional[int]: 文档类型ID
        """
        sql = "SELECT type_id FROM DocumentType WHERE type_code = %s"
        result = self.fetch_data(sql, (type_code,))
        return result[0]['type_id'] if result else None
    
    def run_full_process(self, allcollections_path: str, product_structure_path: str = None, local_files_base_path: str = None) -> bool:
        """
        运行完整的数据处理流程
        
        Args:
            allcollections_path (str): allcollections.json文件路径
            product_structure_path (str): 产品结构数据文件路径
            local_files_base_path (str): 本地文件基础路径
            
        Returns:
            bool: 处理是否成功
        """
        logger.info("开始执行完整数据处理流程")
        
        # 1. 连接数据库
        if not self.connect_database():
            return False
        
        try:
            # 2. 初始化数据库
            if not self.initialize_database():
                logger.error("数据库初始化失败")
                return False
            
            # 3. 导入allcollections数据（标记为作废）
            if not self.import_allcollections_data(allcollections_path):
                logger.error("导入allcollections数据失败")
                return False
            
            # 4. 登录云商API
            if not self.login_yunshang_api():
                logger.error("云商API登录失败")
                return False
            
            # 5. 获取并导入云商产品数据
            products = self.fetch_yunshang_products()
            if not products:
                logger.warning("未获取到云商产品数据")
                return True
            
            success_count = 0
            for product in products:
                if self.import_yunshang_product(product):
                    success_count += 1
                time.sleep(0.1)  # 避免处理过快
            
            logger.info(f"成功导入 {success_count}/{len(products)} 个云商产品")
            
            # 6. 处理本地产品文件（如果提供了相关参数）
            if product_structure_path and local_files_base_path:
                logger.info("开始处理本地产品文件")
                product_structure = self.load_product_structure_data(product_structure_path)
                if product_structure:
                    if not self.scan_local_product_files(local_files_base_path, product_structure):
                        logger.warning("本地文件扫描失败，但继续执行")
                else:
                    logger.warning("无法加载产品结构数据，跳过本地文件处理")
            
            logger.info("完整数据处理流程执行完成")
            return True
            
        finally:
            self.disconnect_database()

def main():
    """
    主函数
    """
    # 从配置文件加载配置
    db_config = DATABASE_CONFIG
    api_config = API_CONFIG
    
    # 文件路径配置
    allcollections_path = FILE_PATHS['allcollections']
    product_structure_path = FILE_PATHS['product_structure']
    local_files_base_path = FILE_PATHS['local_files_base']
    
    # 检查必要文件是否存在
    if not os.path.exists(allcollections_path):
        logger.error(f"未找到文件: {allcollections_path}")
        return False
        
    if not os.path.exists(product_structure_path):
        logger.error(f"未找到文件: {product_structure_path}")
        return False
    
    # 创建处理器并运行
    processor = ProductDataProcessor(db_config, api_config)
    
    try:
        success = processor.run_full_process(
            allcollections_path=allcollections_path,
            product_structure_path=product_structure_path,
            local_files_base_path=local_files_base_path
        )
        
        if success:
            logger.info("数据处理完成")
        else:
            logger.error("数据处理失败")
            
    finally:
        processor.disconnect_database()

if __name__ == '__main__':
    main()