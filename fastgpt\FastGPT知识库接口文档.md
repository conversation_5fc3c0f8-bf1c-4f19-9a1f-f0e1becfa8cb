# FastGPT 知识库接口文档

本文档包含 FastGPT OpenAPI 知识库相关的所有接口信息。

## 目录

- [创建训练订单](#创建训练订单)
- [知识库管理](#知识库管理)
  - [创建知识库](#创建知识库)
  - [获取知识库列表](#获取知识库列表)
  - [获取知识库详情](#获取知识库详情)
  - [删除知识库](#删除知识库)
- [集合管理](#集合管理)
  - [通用创建参数说明](#通用创建参数说明)
  - [创建空集合](#创建空集合)
  - [创建纯文本集合](#创建纯文本集合)
  - [创建链接集合](#创建链接集合)
  - [创建文件集合](#创建文件集合)
  - [创建API集合](#创建api集合)
  - [获取集合列表](#获取集合列表)
  - [获取集合详情](#获取集合详情)
  - [修改集合信息](#修改集合信息)
  - [删除集合](#删除集合)
- [数据管理](#数据管理)
  - [数据结构说明](#数据结构说明)
  - [批量添加数据](#批量添加数据)
  - [获取数据列表](#获取数据列表)
  - [获取数据详情](#获取数据详情)
  - [修改数据](#修改数据)
  - [删除数据](#删除数据)

## 创建训练订单

创建训练订单用于添加知识库数据时进行账单聚合。

**接口地址：** `POST /api/support/wallet/usage/createTrainingUsage`

**请求示例：**
```bash
curl --location --request POST 'http://localhost:3000/api/support/wallet/usage/createTrainingUsage' \
--header 'Authorization: Bearer {{apikey}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "datasetId": "知识库 ID",
    "name": "可选，自定义订单名称，例如：文档训练-fastgpt.docx"
}'
```

**请求参数：**
- `datasetId` (string, 必填): 知识库ID
- `name` (string, 可选): 自定义订单名称

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "message": "",
  "data": "65112ab717c32018f4156361"
}
```

**说明：** data 为 billId，可用于添加知识库数据时进行账单聚合。

## 知识库管理

### 创建知识库

创建一个新的知识库。

**接口地址：** `POST /api/core/dataset/create`

**请求示例：**
```bash
curl --location --request POST 'http://localhost:3000/api/core/dataset/create' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "parentId": null,
    "type": "dataset",
    "name":"测试",
    "intro":"介绍",
    "avatar": "",
    "vectorModel": "text-embedding-ada-002",
    "agentModel": "gpt-3.5-turbo-16k"
}'
```

**请求参数：**
- `parentId` (string, 可选): 父级ID，用于构建目录结构。通常可以为 null 或者直接不传
- `type` (string, 可选): dataset或者folder，代表普通知识库和文件夹。不传则代表创建普通知识库
- `name` (string, 必填): 知识库名
- `intro` (string, 可选): 介绍
- `avatar` (string, 可选): 头像地址
- `vectorModel` (string, 可选): 向量模型(建议传空，用系统默认的)
- `agentModel` (string, 可选): 文本处理模型(建议传空，用系统默认的)

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "message": "",
  "data": "65abc9bd9d1448617cba5e6c"
}
```

### 获取知识库列表

获取指定父级目录下的知识库列表。

**接口地址：** `POST /api/core/dataset/list`

**请求示例：**
```bash
curl --location --request POST 'http://localhost:3000/api/core/dataset/list?parentId=' \
--header 'Authorization: Bearer xxxx' \
--header 'Content-Type: application/json' \
--data-raw '{
    "parentId":""
}'
```

**请求参数：**
- `parentId` (string, 可选): 父级ID，传空字符串或者null，代表获取根目录下的知识库

**响应示例：**
```json
{
    "code": 200,
    "statusText": "",
    "message": "",
    "data": [
        {
            "_id": "65abc9bd9d1448617cba5e6c",
            "parentId": null,
            "avatar": "",
            "name": "测试",
            "intro": "",
            "type": "dataset",
            "permission": "private",
            "canWrite": true,
            "isOwner": true,
            "vectorModel": {
                "model": "text-embedding-ada-002",
                "name": "Embedding-2",
                "charsPointsPrice": 0,
                "defaultToken": 512,
                "maxToken": 8000,
                "weight": 100
            }
        }
    ]
}
```

### 获取知识库详情

根据知识库ID获取详细信息。

**接口地址：** `GET /api/core/dataset/detail`

**请求示例：**
```bash
curl --location --request GET 'http://localhost:3000/api/core/dataset/detail?id=6593e137231a2be9c5603ba7' \
--header 'Authorization: Bearer {{authorization}}'
```

**请求参数：**
- `id` (string, 必填): 知识库的ID

**响应示例：**
```json
{
    "code": 200,
    "statusText": "",
    "message": "",
    "data": {
        "_id": "6593e137231a2be9c5603ba7",
        "parentId": null,
        "teamId": "65422be6aa44b7da77729ec8",
        "tmbId": "65422be6aa44b7da77729ec9",
        "type": "dataset",
        "status": "active",
        "avatar": "/icon/logo.svg",
        "name": "FastGPT test",
        "vectorModel": {
            "model": "text-embedding-ada-002",
            "name": "Embedding-2",
            "charsPointsPrice": 0,
            "defaultToken": 512,
            "maxToken": 8000,
            "weight": 100
        },
        "agentModel": {
            "model": "gpt-3.5-turbo-16k",
            "name": "FastAI-16k",
            "maxContext": 16000,
            "maxResponse": 16000,
            "charsPointsPrice": 0
        },
        "intro": "",
        "permission": "private",
        "updateTime": "2024-01-02T10:11:03.084Z",
        "canWrite": true,
        "isOwner": true
    }
}
```

### 删除知识库

根据知识库ID删除知识库。

**接口地址：** `DELETE /api/core/dataset/delete`

**请求示例：**
```bash
curl --location --request DELETE 'http://localhost:3000/api/core/dataset/delete?id=65abc8729d1448617cba5df6' \
--header 'Authorization: Bearer {{authorization}}'
```

**请求参数：**
- `id` (string, 必填): 知识库的ID

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "message": "",
  "data": null
}
```

## 集合管理

### 通用创建参数说明

以下参数适用于所有集合创建接口：

**入参：**

| 参数 | 说明 | 必填 |
| --- | --- | --- |
| datasetId | 知识库ID | ✅ |
| parentId | 父级ID，不填则默认为根目录 | |
| customPdfParse | PDF增强解析。true: 开启PDF增强解析;不填则默认为false | |
| trainingType | 数据处理方式。chunk: 按文本长度进行分割;qa: 问答对提取 | ✅ |
| chunkTriggerType | 分块条件逻辑。minSize(默认): 大于 n 时分块;maxSize: 小于文件处理模型最大上下文时分块;forceChunk: 强制分块 | |
| chunkTriggerMinSize | chunkTriggerType=minSize 时候填写，原文长度大于该值时候分块(默认 1000) | |
| autoIndexes | 是否自动生成索引(仅商业版支持) | |
| imageIndex | 是否自动生成图片索引(仅商业版支持) | |
| chunkSettingMode | 分块参数模式。auto: 系统默认参数; custom: 手动指定参数 | |
| chunkSplitMode | 分块拆分模式。paragraph:段落优先，再按长度分;size: 按长度拆分; char: 按字符拆分。chunkSettingMode=auto时不生效。 | |
| paragraphChunkDeep | 最大段落深度(默认 5) | |
| chunkSize | 分块大小，默认 1500。chunkSettingMode=auto时不生效。 | |
| indexSize | 索引大小，默认 512，必须小于索引模型最大token。chunkSettingMode=auto时不生效。 | |
| chunkSplitter | 自定义最高优先分割符号，除非超出文件处理最大上下文，否则不会进行进一步拆分。chunkSettingMode=auto时不生效。 | |
| qaPrompt | qa拆分提示词 | |
| tags | 集合标签(字符串数组) | |
| createTime | 文件创建时间(Date / String) | |

**出参：**
- `collectionId` - 新建的集合ID
- `insertLen` - 插入的块数量

### 创建空集合

创建一个空的虚拟集合。

**接口地址：** `POST /api/core/dataset/collection/create`

**请求示例：**
```bash
curl --location --request POST 'http://localhost:3000/api/core/dataset/collection/create' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "datasetId":"6593e137231a2be9c5603ba7",
    "parentId": null,
    "name":"测试",
    "type":"virtual",
    "metadata":{
        "test":111
    }
}'
```

**请求参数：**
- `datasetId` (string, 必填): 知识库的ID
- `parentId` (string, 可选): 父级ID，不填则默认为根目录
- `name` (string, 必填): 集合名称
- `type` (string, 必填): folder:文件夹; virtual:虚拟集合(手动集合)
- `metadata` (object, 可选): 元数据(暂时没啥用)

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "message": "",
  "data": "65abcd009d1448617cba5ee1"
}
```

### 创建纯文本集合

传入一段文字，创建一个集合，会根据传入的文字进行分割。

**接口地址：** `POST /api/core/dataset/collection/create/text`

**请求示例：**
```bash
curl --location --request POST 'http://localhost:3000/api/core/dataset/collection/create/text' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "text":"xxxxxxxx",
    "datasetId":"6593e137231a2be9c5603ba7",
    "parentId": null,
    "name":"测试训练",
    "trainingType": "qa",
    "chunkSettingMode": "auto",
    "qaPrompt":"",
    "metadata":{}
}'
```

**请求参数：**
- `text` (string, 必填): 原文本
- `datasetId` (string, 必填): 知识库的ID
- `parentId` (string, 可选): 父级ID，不填则默认为根目录
- `name` (string, 必填): 集合名称
- `metadata` (object, 可选): 元数据(暂时没啥用)
- 其他参数参考通用创建参数说明

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "message": "",
  "data": {
    "collectionId": "65abcfab9d1448617cba5f0d",
    "results": {
      "insertLen": 5
    }
  }
}
```

### 创建链接集合

传入一个网络链接，创建一个集合，会先去对应网页抓取内容，再对抓取的文字进行分割。

**接口地址：** `POST /api/core/dataset/collection/create/link`

**请求示例：**
```bash
curl --location --request POST 'http://localhost:3000/api/core/dataset/collection/create/link' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "link":"https://doc.tryfastgpt.ai/docs/course/quick-start/",
    "datasetId":"6593e137231a2be9c5603ba7",
    "parentId": null,
    "trainingType": "chunk",
    "chunkSettingMode": "auto",
    "qaPrompt":"",
    "metadata":{
        "webPageSelector":".docs-content"
    }
}'
```

**请求参数：**
- `link` (string, 必填): 网络链接
- `datasetId` (string, 必填): 知识库的ID
- `parentId` (string, 可选): 父级ID，不填则默认为根目录
- `metadata.webPageSelector` (string, 可选): 网页选择器，用于指定网页中的哪个元素作为文本
- 其他参数参考通用创建参数说明

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "message": "",
  "data": {
    "collectionId": "65abd0ad9d1448617cba6031",
    "results": {
      "insertLen": 1
    }
  }
}
```

### 创建文件集合

传入一个文件，创建一个集合，会读取文件内容进行分割。目前支持：pdf, docx, md, txt, html, csv。

**接口地址：** `POST /api/core/dataset/collection/create/localFile`

**请求示例：**
```bash
curl --location --request POST 'http://localhost:3000/api/core/dataset/collection/create/localFile' \
--header 'Authorization: Bearer {{authorization}}' \
--form 'file=@"C:\\Users\\<USER>\\Desktop\\fastgpt测试文件\\index.html"' \
--form 'data="{\"datasetId\":\"6593e137231a2be9c5603ba7\",\"parentId\":null,\"trainingType\":\"chunk\",\"chunkSize\":512,\"chunkSplitter\":\"\",\"qaPrompt\":\"\",\"metadata\":{}}"'
```

**请求格式：** 使用 POST form-data 格式上传，包含 file 和 data 两个字段。

**请求参数：**
- `file` (file, 必填): 文件
- `data` (string, 必填): 知识库相关信息(json序列化后传入)，参数说明见通用创建参数说明

**注意：** 使用代码上传时，请注意中文 filename 需要进行 encode 处理，否则容易乱码。由于解析文档是异步操作，此处不会返回插入的数量。

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "message": "",
  "data": {
    "collectionId": "65abc044e4704bac793fbd81",
    "results": {
      "insertLen": 0
    }
  }
}
```

### 创建API集合

传入一个文件的 id，创建一个集合，会读取文件内容进行分割。目前支持：pdf, docx, md, txt, html, csv。

**接口地址：** `POST /api/core/dataset/collection/create/apiCollection`

**请求示例：**
```bash
curl --location --request POST 'http://localhost:3000/api/core/dataset/collection/create/apiCollection' \
--header 'Authorization: Bearer fastgpt-xxx' \
--header 'Content-Type: application/json' \
--data-raw '{
    "name": "A Quick Guide to Building a Discord Bot.pdf",
    "apiFileId":"A Quick Guide to Building a Discord Bot.pdf",
    "datasetId": "674e9e479c3503c385495027",
    "parentId": null,
    "trainingType": "chunk",
    "chunkSize":512,
    "chunkSplitter":"",
    "qaPrompt":""
}'
```

**请求参数：**
- `name` (string, 必填): 集合名，建议就用文件名
- `apiFileId` (string, 必填): 文件的ID
- `datasetId` (string, 必填): 知识库的ID
- `parentId` (string, 可选): 父级ID，不填则默认为根目录
- `trainingType` (string, 必填): 训练模式
- `chunkSize` (number, 可选): 每个 chunk 的长度。chunk模式:100~3000; qa模式: 4000~模型最大token(16k模型通常建议不超过10000)
- `chunkSplitter` (string, 可选): 自定义最高优先分割符号
- `qaPrompt` (string, 可选): qa拆分自定义提示词

**注意：** 使用代码上传时，请注意中文 filename 需要进行 encode 处理，否则容易乱码。

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "message": "",
  "data": {
    "collectionId": "65abc044e4704bac793fbd81",
    "results": {
      "insertLen": 1
    }
  }
}
```

### 获取集合列表

获取指定知识库下的集合列表。

**接口地址（4.8.19+）：** `POST /api/core/dataset/collection/listV2`

**请求示例：**
```bash
curl --location --request POST 'http://localhost:3000/api/core/dataset/collection/listV2' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "offset":0,
    "pageSize": 10,
    "datasetId":"6593e137231a2be9c5603ba7",
    "parentId": null,
    "searchText":""
}'
```

**接口地址（4.8.19-，不再维护）：** `POST /api/core/dataset/collection/list`

**请求参数：**
- `offset` (number, 必填): 偏移量
- `pageSize` (number, 可选): 每页数量，最大30
- `datasetId` (string, 必填): 知识库的ID
- `parentId` (string, 可选): 父级Id
- `searchText` (string, 可选): 模糊搜索文本

**响应示例：**
```json
{
    "code": 200,
    "statusText": "",
    "message": "",
    "data": {
        "list": [
            {
                "_id": "6593e137231a2be9c5603ba9",
                "parentId": null,
                "tmbId": "65422be6aa44b7da77729ec9",
                "type": "virtual",
                "name": "手动录入",
                "updateTime": "2099-01-01T00:00:00.000Z",
                "dataAmount": 3,
                "trainingAmount": 0,
                "externalFileId": "1111",
                "tags": ["11", "测试的"],
                "forbid": false,
                "trainingType": "chunk",
                "permission": {
                    "value": 4294967295,
                    "isOwner": true,
                    "hasManagePer": true,
                    "hasWritePer": true,
                    "hasReadPer": true
                }
            }
        ],
        "total": 93
    }
}
```

### 获取集合详情

根据集合ID获取详细信息。

**接口地址：** `GET /api/core/dataset/collection/detail`

**请求示例：**
```bash
curl --location --request GET 'http://localhost:3000/api/core/dataset/collection/detail?id=65abcfab9d1448617cba5f0d' \
--header 'Authorization: Bearer {{authorization}}'
```

**请求参数：**
- `id` (string, 必填): 集合的ID

**响应示例：**
```json
{
    "code": 200,
    "statusText": "",
    "message": "",
    "data": {
        "_id": "65abcfab9d1448617cba5f0d",
        "parentId": null,
        "teamId": "65422be6aa44b7da77729ec8",
        "tmbId": "65422be6aa44b7da77729ec9",
        "datasetId": {
            "_id": "6593e137231a2be9c5603ba7",
            "parentId": null,
            "teamId": "65422be6aa44b7da77729ec8",
            "tmbId": "65422be6aa44b7da77729ec9",
            "type": "dataset",
            "status": "active",
            "avatar": "/icon/logo.svg",
            "name": "FastGPT test",
            "vectorModel": "text-embedding-ada-002",
            "agentModel": "gpt-3.5-turbo-16k",
            "intro": "",
            "permission": "private",
            "updateTime": "2024-01-02T10:11:03.084Z"
        },
        "type": "virtual",
        "name": "测试训练",
        "trainingType": "qa",
        "chunkSize": 8000,
        "chunkSplitter": "",
        "qaPrompt": "11",
        "rawTextLength": 40466,
        "hashRawText": "47270840614c0cc122b29daaddc09c2a48f0ec6e77093611ab12b69cba7fee12",
        "createTime": "2024-01-20T13:50:35.838Z",
        "updateTime": "2024-01-20T13:50:35.838Z",
        "canWrite": true,
        "sourceName": "测试训练"
    }
}
```

### 修改集合信息

通过集合ID或外部文件ID修改集合信息。

**接口地址：** `PUT /api/core/dataset/collection/update`

**通过集合ID修改：**
```bash
curl --location --request PUT 'http://localhost:3000/api/core/dataset/collection/update' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "id":"65abcfab9d1448617cba5f0d",
    "parentId": null,
    "name": "测2222试",
    "tags": ["tag1", "tag2"],
    "forbid": false,
    "createTime": "2024-01-01T00:00:00.000Z"
}'
```

**通过外部文件ID修改：**
```bash
curl --location --request PUT 'http://localhost:3000/api/core/dataset/collection/update' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "datasetId":"6593e137231a2be9c5603ba7",
    "externalFileId":"1111",
    "parentId": null,
    "name": "测2222试",
    "tags": ["tag1", "tag2"],
    "forbid": false,
    "createTime": "2024-01-01T00:00:00.000Z"
}'
```

**请求参数：**
- `id` (string, 可选): 集合的ID（与datasetId+externalFileId二选一）
- `datasetId` (string, 可选): 知识库ID（与externalFileId配合使用）
- `externalFileId` (string, 可选): 外部文件ID（与datasetId配合使用）
- `parentId` (string, 可选): 修改父级ID
- `name` (string, 可选): 修改集合名称
- `tags` (array, 可选): 修改集合标签
- `forbid` (boolean, 可选): 修改集合禁用状态
- `createTime` (string, 可选): 修改集合创建时间

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "message": "",
  "data": null
}
```

### 删除集合

根据集合ID删除集合。

**接口地址：** `DELETE /api/core/dataset/collection/delete`

**请求示例：**
```bash
curl --location --request DELETE 'http://localhost:3000/api/core/dataset/collection/delete?id=65aa2a64e6cb9b8ccdc00de8' \
--header 'Authorization: Bearer {{authorization}}'
```

**请求参数：**
- `id` (string, 必填): 集合的ID

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "message": "",
  "data": null
}
```

## 数据管理

### 数据结构说明

**Data结构：**

| 字段 | 类型 | 说明 | 必填 |
| --- | --- | --- | --- |
| teamId | String | 团队ID | ✅ |
| tmbId | String | 成员ID | ✅ |
| datasetId | String | 知识库ID | ✅ |
| collectionId | String | 集合ID | ✅ |
| q | String | 主要数据 | ✅ |
| a | String | 辅助数据 | ✖ |
| fullTextToken | String | 分词 | ✖ |
| indexes | Index[] | 向量索引 | ✅ |
| updateTime | Date | 更新时间 | ✅ |
| chunkIndex | Number | 分块下标 | ✖ |

**Index结构：**

每组数据的自定义索引最多5个。

| 字段 | 类型 | 说明 | 必填 |
| --- | --- | --- | --- |
| type | String | 可选索引类型:default-默认索引; custom-自定义索引; summary-总结索引; question-问题索引; image-图片索引 | |
| dataId | String | 关联的向量ID，变更数据时候传入该 ID，会进行差量更新，而不是全量更新 | |
| text | String | 文本内容 | ✅ |

**说明：** type 不填则默认为 custom 索引，还会基于 q/a 组成一个默认索引。如果传入了默认索引，则不会额外创建。

### 批量添加数据

为集合批量添加数据，每次最多推送 200 组数据。

**接口地址：** `POST /api/core/dataset/data/pushData`

**请求示例：**
```bash
curl --location --request POST 'https://api.fastgpt.in/api/core/dataset/data/pushData' \
--header 'Authorization: Bearer apikey' \
--header 'Content-Type: application/json' \
--data-raw '{
    "collectionId": "64663f451ba1676dbdef0499",
    "trainingType": "chunk",
    "prompt": "可选。qa 拆分引导词，chunk 模式下忽略",
    "billId": "可选。如果有这个值，本次的数据会被聚合到一个订单中，这个值可以重复使用。可以参考 [创建训练订单] 获取该值。",
    "data": [
        {
            "q": "你是谁?",
            "a": "我是FastGPT助手"
        },
        {
            "q": "你会什么?",
            "a": "我什么都会",
            "indexes": [
                {
                    "text":"自定义索引1"
                },
                {
                    "text":"自定义索引2"
                }
            ]
        }
    ]
}'
```

**请求参数：**
- `collectionId` (string, 必填): 集合ID
- `trainingType` (string, 必填): 训练模式
- `prompt` (string, 可选): 自定义 QA 拆分提示词，需严格按照模板，建议不要传入
- `billId` (string, 可选): 订单ID，用于账单聚合
- `data` (array, 必填): 具体数据
  - `q` (string, 必填): 主要数据
  - `a` (string, 可选): 辅助数据
  - `indexes` (array, 可选): 自定义索引。可以不传或者传空数组，默认都会使用q和a组成一个索引

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "data": {
    "insertLen": 2,
    "overTokens": [],
    "repeatIndexes": []
  }
}
```

### 获取数据列表

获取指定集合下的数据列表。

**接口地址：** `POST /api/core/dataset/data/list`

**请求示例：**
```bash
curl --location --request POST 'http://localhost:3000/api/core/dataset/data/list' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "pageNum": 1,
    "pageSize": 10,
    "collectionId": "65abcfab9d1448617cba5f0d",
    "searchText": ""
}'
```

**请求参数：**
- `pageNum` (number, 必填): 页码
- `pageSize` (number, 必填): 每页数量，最大30
- `collectionId` (string, 必填): 集合ID
- `searchText` (string, 可选): 模糊搜索文本

**响应示例：**
```json
{
    "code": 200,
    "statusText": "",
    "message": "",
    "data": {
        "pageNum": 1,
        "pageSize": 10,
        "data": [
            {
                "_id": "65abcfab9d1448617cba5f0e",
                "q": "你是谁?",
                "a": "我是FastGPT助手",
                "chunkIndex": 0,
                "updateTime": "2024-01-20T13:50:35.838Z",
                "datasetId": "6593e137231a2be9c5603ba7",
                "collectionId": "65abcfab9d1448617cba5f0d",
                "indexes": [
                    {
                        "dataId": "65abcfab9d1448617cba5f0f",
                        "type": "default",
                        "text": "你是谁?\n我是FastGPT助手"
                    }
                ]
            }
        ],
        "total": 5
    }
}
```

### 获取数据详情

根据数据ID获取详细信息。

**接口地址：** `GET /api/core/dataset/data/detail`

**请求示例：**
```bash
curl --location --request GET 'http://localhost:3000/api/core/dataset/data/detail?id=65abcfab9d1448617cba5f0e' \
--header 'Authorization: Bearer {{authorization}}'
```

**请求参数：**
- `id` (string, 必填): 数据的ID

**响应示例：**
```json
{
    "code": 200,
    "statusText": "",
    "message": "",
    "data": {
        "_id": "65abcfab9d1448617cba5f0e",
        "q": "你是谁?",
        "a": "我是FastGPT助手",
        "chunkIndex": 0,
        "updateTime": "2024-01-20T13:50:35.838Z",
        "datasetId": "6593e137231a2be9c5603ba7",
        "collectionId": "65abcfab9d1448617cba5f0d",
        "indexes": [
            {
                "dataId": "65abcfab9d1448617cba5f0f",
                "type": "default",
                "text": "你是谁?\n我是FastGPT助手"
            }
        ]
    }
}
```

### 修改数据

根据数据ID修改数据内容。

**接口地址：** `PUT /api/core/dataset/data/update`

**请求示例：**
```bash
curl --location --request PUT 'http://localhost:3000/api/core/dataset/data/update' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "id": "65abcfab9d1448617cba5f0e",
    "q": "你是谁?",
    "a": "我是FastGPT智能助手",
    "indexes": [
        {
            "dataId": "65abcfab9d1448617cba5f0f",
            "type": "default",
            "text": "你是谁?\n我是FastGPT智能助手"
        }
    ]
}'
```

**请求参数：**
- `id` (string, 必填): 数据的ID
- `q` (string, 可选): 主要数据
- `a` (string, 可选): 辅助数据
- `indexes` (array, 可选): 向量索引

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "message": "",
  "data": null
}
```

### 删除数据

根据数据ID删除数据。

**接口地址：** `DELETE /api/core/dataset/data/delete`

**请求示例：**
```bash
curl --location --request DELETE 'http://localhost:3000/api/core/dataset/data/delete' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "id": "65abcfab9d1448617cba5f0e"
}'
```

**请求参数：**
- `id` (string, 必填): 数据的ID

**响应示例：**
```json
{
  "code": 200,
  "statusText": "",
  "message": "",
  "data": null
}
```

## 常见问题

### 如何获取知识库ID（datasetId）

可以通过以下方式获取知识库ID：
1. 调用「获取知识库列表」接口，从返回结果中获取 `_id` 字段
2. 在 FastGPT 管理界面中查看知识库详情页面的URL参数

### 如何获取文件集合ID（collection_id）

可以通过以下方式获取集合ID：
1. 调用「获取集合列表」接口，从返回结果中获取 `_id` 字段
2. 创建集合时，接口会返回新创建的集合ID
3. 在 FastGPT 管理界面中查看集合详情页面的URL参数

### 支持的文件格式

目前支持的文件格式包括：
- PDF (.pdf)
- Word文档 (.docx)
- Markdown (.md)
- 纯文本 (.txt)
- HTML (.html)
- CSV (.csv)

### 分块模式说明

- **chunk模式**：按文本长度进行分割，适合长文档的处理
- **qa模式**：问答对提取，适合FAQ类型的内容

### 索引类型说明

- **default**：默认索引，基于q/a组成
- **custom**：自定义索引
- **summary**：总结索引
- **question**：问题索引
- **image**：图片索引

---

**注意事项：**
1. 所有接口都需要在请求头中携带有效的 Authorization Bearer Token
2. 批量操作时注意数据量限制，避免超出接口限制
3. 文件上传时注意中文文件名的编码处理
4. 异步操作（如文件解析）可能需要轮询状态接口获取最终结果
5. 请根据实际部署环境修改接口地址中的域名和端口