# FastGPT知识库数据同步系统 - 环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# =============================================================================
# 数据库配置
# =============================================================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=sync_fastgpt
DB_USER=postgres
DB_PASSWORD=your_database_password

# =============================================================================
# FastGPT API配置
# =============================================================================
# FastGPT API基础URL
FASTGPT_API_URL=https://api.fastgpt.in/api

# FastGPT API密钥
FASTGPT_API_KEY=your_fastgpt_api_key

# FastGPT知识库ID
FASTGPT_KB_ID=your_knowledge_base_id

# =============================================================================
# TextIn API配置 (PDF解析)
# =============================================================================
# TextIn应用ID
TEXTIN_APP_ID=your_textin_app_id

# TextIn密钥
TEXTIN_SECRET_CODE=your_textin_secret_code

# =============================================================================
# 多模态AI配置
# =============================================================================
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4-vision-preview

# Azure OpenAI配置（可选）
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
AZURE_OPENAI_DEPLOYMENT=your_deployment_name

# Claude配置（可选）
CLAUDE_API_KEY=your_claude_api_key

# =============================================================================
# 云商API配置（如果使用）
# =============================================================================
# 阿里云配置
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret
ALIYUN_REGION=cn-hangzhou

# 腾讯云配置
TENCENT_SECRET_ID=your_tencent_secret_id
TENCENT_SECRET_KEY=your_tencent_secret_key
TENCENT_REGION=ap-beijing

# 华为云配置
HUAWEI_ACCESS_KEY=your_huawei_access_key
HUAWEI_SECRET_KEY=your_huawei_secret_key
HUAWEI_REGION=cn-north-4

# =============================================================================
# 应用配置
# =============================================================================
# Flask应用密钥
FLASK_SECRET_KEY=your_flask_secret_key_here

# 应用环境 (development/production)
FLASK_ENV=development

# 调试模式
FLASK_DEBUG=true

# 应用端口
FLASK_PORT=5000

# =============================================================================
# 日志配置
# =============================================================================
# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE_PATH=logs/app.log

# =============================================================================
# 缓存配置
# =============================================================================
# Redis配置（如果使用）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# =============================================================================
# 安全配置
# =============================================================================
# 加密密钥
ENCRYPTION_KEY=your_encryption_key_here

# JWT密钥
JWT_SECRET_KEY=your_jwt_secret_key_here

# =============================================================================
# 性能配置
# =============================================================================
# 最大并发处理数
MAX_WORKERS=4

# 批处理大小
BATCH_SIZE=10

# 请求超时时间（秒）
REQUEST_TIMEOUT=60

# 最大重试次数
MAX_RETRIES=3

# =============================================================================
# 文件处理配置
# =============================================================================
# 最大文件大小（字节）
MAX_FILE_SIZE=52428800  # 50MB

# 支持的文件扩展名（逗号分隔）
ALLOWED_EXTENSIONS=.md,.pdf,.docx,.doc,.txt

# 文件存储路径
FILE_STORAGE_PATH=./storage

# 临时文件路径
TEMP_FILE_PATH=./temp

# =============================================================================
# 监控配置
# =============================================================================
# 启用性能监控
ENABLE_MONITORING=true

# 监控数据保留天数
MONITORING_RETENTION_DAYS=30

# 告警邮箱（如果配置邮件告警）
ALERT_EMAIL=<EMAIL>

# =============================================================================
# 邮件配置（可选）
# =============================================================================
# SMTP服务器
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_USE_TLS=true

# =============================================================================
# 备份配置
# =============================================================================
# 自动备份间隔（小时）
BACKUP_INTERVAL=24

# 备份保留天数
BACKUP_RETENTION_DAYS=7

# 备份存储路径
BACKUP_PATH=./backups

# =============================================================================
# 开发配置
# =============================================================================
# 启用开发模式特性
DEVELOPMENT_MODE=true

# 启用详细日志
VERBOSE_LOGGING=true

# 启用API文档
ENABLE_API_DOCS=true

# =============================================================================
# 注意事项
# =============================================================================
# 1. 请确保所有密钥和敏感信息的安全性
# 2. 不要将包含真实密钥的.env文件提交到版本控制系统
# 3. 定期更换API密钥以确保安全
# 4. 根据实际需求调整性能和资源配置
# 5. 在生产环境中禁用调试模式和开发特性