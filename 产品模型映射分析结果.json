{"field_mappings": {"allcollections": {"id-fastgpt": "fastgpt_id", "name": "document_name", "资料用途": "material_usage", "产品业务范畴": "business_scope", "产品上市时间": "launch_time", "产品型号": "model", "产品名称": "name", "产品所支持的软件": "supported_software"}, "product_structure": {"产品分类": "category_name", "类别": "category_type", "系列": "series_name", "产品型号": "model", "备注（定义对应型号所包含的内容）": "collection_requirements", "收集情况": "collection_status"}}, "category_hierarchy": {"考勤产品": {"level": 1, "type": "产品分类", "children": {"考勤机": {"level": 2, "type": "类别", "parent": "考勤产品", "children": {"X系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "X10", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "X20", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "K28", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "XU100", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "XU200", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "XU300", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "XU500", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "ZK系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "ZK3960/ZK3960X", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "S系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "DS310", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "S60", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "S60PLUS", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "ST200", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "ST300", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "S30", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "云智PT600", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "钉钉系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "DW6", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "DF100", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "TX系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "TX628", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "TX638", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "TX628-P", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "U系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "U100", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "U160", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "U260", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "U560", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "新U8", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "JT500", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "iClock系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "iClock300", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iClock360", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "WX系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "ZK-T1", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "ZK-F3", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "WX108", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "WX3960", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "企业微信云考勤", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "ZQ系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "ZQ1", "collection_requirements": "彩页、入门指南", "collection_status": "已完成"}, {"model": "ZQ2", "collection_requirements": "彩页、入门指南", "collection_status": "已完成"}, {"model": "ZQ3/ZQ3Pro", "collection_requirements": "彩页、入门指南", "collection_status": "已完成"}, {"model": "ZQ102", "collection_requirements": "彩页、入门指南", "collection_status": "已完成"}]}, "FS系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "fs1", "collection_requirements": "彩页、入门指南", "collection_status": "已完成"}, {"model": "fs2/fs2pro", "collection_requirements": "彩页、入门指南", "collection_status": "已完成"}, {"model": "FS3", "collection_requirements": "彩页、入门指南", "collection_status": "已完成"}]}, "nFace系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "nFace101-S", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "UF系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "EF200", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "UF100Plus-S", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "iFace系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "iFace101", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "xFace系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "xFace50", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "HORUS系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "HORUS101", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "BK系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "BK100/BK100 Pro", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "BK200", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "ZKTeco+系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "iQ1", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "M系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "MX200", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "M200 PLUS", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "M300 PLUS", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "JM300", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "FA系列": {"level": 3, "type": "系列", "parent": "考勤机", "models": [{"model": "FA1000", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}}}}}, "门禁产品": {"level": 1, "type": "产品分类", "children": {"门禁一体机": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"SC系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "SC601B", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "SC601W", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "SCR100", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "SC700", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "SC701", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "SC102", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "SC103", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "SC202", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "SC203", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "MCR系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "MCR100", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "MCR103", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "MCR200", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "MCR203", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "MCR300", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "MCR303", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "M系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "M880", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "M980", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "X系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "X1", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "X6", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "Smart系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "Smart 3F", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "Smart 5F", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "JF108", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "OF系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "OF260", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "OF360", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "OF109", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "OF107", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "F系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "F25", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "F28", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "F7PLUS", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "F2", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "F2S", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "F2S[GM]", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "F8", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "MA300", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "新F18", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "F20", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "F30", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "ZKTeco+系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "F7+", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "F18+", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "nFace102 Pro+/nFace102-S+", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iX25", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iX360", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iX100", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iX601", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iX602", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "WX系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "WX7 plus", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "WX702", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "ZK-F6", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "iClock系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "iClock506", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iClock660", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iClock880", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iClock980", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iClock1000", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "新iClock660", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "iFace系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "iFace301", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iFace302-S", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iFace303", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iFace501", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iFace502", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iFace503", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iFace701", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iFace702-S", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iFace703", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "iFace3", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "inSun100", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "ZF系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "ZF800/ZF800C", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "ZK系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "ZK3969", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "UF系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "UF200-S", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "PF1000/PF1000[P2P]", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "UF560", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "UF600", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "Xpalm系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "Xpalm602", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "FJ系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "FJ200", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "BioFace系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "BioFace500/BioFace600", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "ZK-S1007", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "ZK-Z1008", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "nFace系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "nFace102-S/nFace103", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "nFace102 Pro", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "nFace260", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "nFace260-HL", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "nFace280", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "nFace128", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "nFace138", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "zFace系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "zFace1100/zFace1200/zFace1300/zFace1701/zFace1702", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "xFace系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "xFace701/xFace702", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace55", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace360", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace360-HL", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace100", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace60[全网通4G]/xFace600[全网通4G]", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace320/xFace420", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace320-HL/xFace420-HL", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace500/xFace500[GM]/xFace600/xFace60/xFace60[GM]", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace610/xFace620", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace630", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace600-HL", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace640/xFace640-3D", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace600-BM", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace60-Plus/xFace600-Plus", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace60-TI/xFace600-TI/xFace600[TI][BM]", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace700-TI", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "xFace700/xFace700C", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "FS系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "FS100", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "FS600", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "AI系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "AI302", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "AI702", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "MateFace系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "MateFace30", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "MateFace20", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "MateFace20 Pro", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "无线门禁系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "ISMC100套包/ISMC100门禁机", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "ISMC200套包/ISMC200门禁机", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "T系列非可见光": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "TC2000", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "TF2000", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "TF3000", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "TA1200", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}, "TDB系列": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "TDB06", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "TDB06[F]", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "TDB06[QR]", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "TDB07", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "TDB08", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "TDB08-C", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "TDB08-3D", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "TDB08-C-4G", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "TDB09", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "TDB09-TI", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}, "T系列可见光": {"level": 3, "type": "系列", "parent": "门禁一体机", "models": [{"model": "T02", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "T08-C", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}}}, "机械锁": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"UX系列": {"level": 3, "type": "系列", "parent": "机械锁", "models": [{"model": "UX30", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}}}, "采集器": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"ZK系列": {"level": 3, "type": "系列", "parent": "采集器", "models": [{"model": "ZK8500R[ID]/ZK8500R[MF]", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "CR系列": {"level": 3, "type": "系列", "parent": "采集器", "models": [{"model": "CR70、CR70RW", "collection_requirements": "内容类别:彩页、用户手册、产品图片", "collection_status": NaN}, {"model": "CR70RP", "collection_requirements": "内容类别:彩页、产品图片", "collection_status": NaN}]}, "MC系列": {"level": 3, "type": "系列", "parent": "采集器", "models": [{"model": "MC5000", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}]}, "GM系列": {"level": 3, "type": "系列", "parent": "采集器", "models": [{"model": "GM900D", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}}}, "发卡器": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"CR系列": {"level": 3, "type": "系列", "parent": "发卡器", "models": [{"model": "CR10E", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "CR10M", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "CR10MW", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "CR20E", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "CR20M", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "CR20MW", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "CR30MW", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}}}, "读卡器": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"KR系列": {"level": 3, "type": "系列", "parent": "读卡器", "models": [{"model": "KR100E/KR100M", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "KR101E/KR101M", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "KR102E/KR102M", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "KR300", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "KR600E/KR600M", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "KR601E/KR601M", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "KR602E/KR602M", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "KR702E/KR702M", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "KR801系列", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}, {"model": "KR802系列", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}, {"model": "KR803系列", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}]}, "FR系列": {"level": 3, "type": "系列", "parent": "读卡器", "models": [{"model": "新FR1200", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "FR2200", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}, {"model": "FR4200", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}, {"model": "FR4300", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}]}, "KD系列": {"level": 3, "type": "系列", "parent": "读卡器", "models": [{"model": "KD100E/KD100M/KD103E/KD103M", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}, {"model": "KD400EM/KD401EM", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "QR系列": {"level": 3, "type": "系列", "parent": "读卡器", "models": [{"model": "QR301W-F、QR401W-F、QR401B-F、QR401W-I", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}, "KQR系列": {"level": 3, "type": "系列", "parent": "读卡器", "models": [{"model": "KQR101-C、KQR102-C、KQR102E、KQR201-C、KQR202-C、KQR202E", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}, "ZR系列": {"level": 3, "type": "系列", "parent": "读卡器", "models": [{"model": "ZR601", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}, {"model": "ZR602", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}, {"model": "ZR604", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}, {"model": "ZR604-WR", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}]}, "KM系列": {"level": 3, "type": "系列", "parent": "读卡器", "models": [{"model": "KM101系列", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}, {"model": "KM102系列", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}, "FI系列": {"level": 3, "type": "系列", "parent": "读卡器", "models": [{"model": "FI5000系列", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "FI1100、FI1200", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}, "防爆读头系列": {"level": 3, "type": "系列", "parent": "读卡器", "models": [{"model": "KR601B-RS-EX", "collection_requirements": "内容类别:彩页、产品图片", "collection_status": NaN}, {"model": "TM1206-EX", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}, "GR系列": {"level": 3, "type": "系列", "parent": "读卡器", "models": [{"model": "GR902", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}}}, "一维扫描枪": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"ZK系列": {"level": 3, "type": "系列", "parent": "一维扫描枪", "models": [{"model": "ZKS10/ZKS10W/ZKS10B/ZKS10C/ZKS10CW/ZKS10CB", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}}}, "二维扫描枪": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"ZK系列": {"level": 3, "type": "系列", "parent": "二维扫描枪", "models": [{"model": "ZKS20/ZKS20W/ZKS20B", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}}}, "二维扫描平台": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"ZK系列": {"level": 3, "type": "系列", "parent": "二维扫描平台", "models": [{"model": "ZKX1/ZKX2/ZKX3", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}}}, "条码扫描器": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"ZK系列": {"level": 3, "type": "系列", "parent": "条码扫描器", "models": [{"model": "ZKQR42", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}}}, "门禁控制器": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"C3系列": {"level": 3, "type": "系列", "parent": "门禁控制器", "models": [{"model": "C3-100/C3-200/C3-400", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "C4系列": {"level": 3, "type": "系列", "parent": "门禁控制器", "models": [{"model": "C4-100/C4-200/C4-400", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "InBio系列": {"level": 3, "type": "系列", "parent": "门禁控制器", "models": [{"model": "InBio160/InBio260/InBio460", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "K2系列": {"level": 3, "type": "系列", "parent": "门禁控制器", "models": [{"model": "K2-100、K2-200、K2-400", "collection_requirements": "内容类别:上市指南、彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}, "ZTHCAM系列": {"level": 3, "type": "系列", "parent": "门禁控制器", "models": [{"model": "ZTHCAM160、ZTHCAM260、ZTHCAM460", "collection_requirements": "内容类别:上市指南、彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}, "ZTHCAMPRO系列": {"level": 3, "type": "系列", "parent": "门禁控制器", "models": [{"model": "ZTHCAM160PRO、ZTHCAM260PRO、ZTHCAM460PRO", "collection_requirements": "内容类别:上市指南、彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}, "C5系列": {"level": 3, "type": "系列", "parent": "门禁控制器", "models": [{"model": "C5-100、C5-200、C5-400", "collection_requirements": "内容类别:上市指南、彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}, "InBioP系列": {"level": 3, "type": "系列", "parent": "门禁控制器", "models": [{"model": "InBioP3040、InBioP3080、DM20", "collection_requirements": "内容类别:彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}}}, "控制器配件": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"ZKPSM系列": {"level": 3, "type": "系列", "parent": "控制器配件", "models": [{"model": "ZKPSM030", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZKPSM030B", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZKPSM050", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "TPM系列": {"level": 3, "type": "系列", "parent": "控制器配件", "models": [{"model": "TPM003", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "TPM005/TPM005B", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "CASE系列铁箱": {"level": 3, "type": "系列", "parent": "控制器配件", "models": [{"model": "CASE01", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "Case04", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CASE06", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CASE07、CASE08", "collection_requirements": "内容类别:上市指南、彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}}}, "门禁线性电源": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"AP系列": {"level": 3, "type": "系列", "parent": "门禁线性电源", "models": [{"model": "AP103", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AP303", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AP105", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AP305", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AP108", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AP208", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "50W门禁线性电源", "collection_requirements": "彩页、", "collection_status": "已完成"}]}}}, "电插锁": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"AL系列": {"level": 3, "type": "系列", "parent": "电插锁", "models": [{"model": "AL-100S", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-300S", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-400L/AL-400R", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-180M", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "CL系列": {"level": 3, "type": "系列", "parent": "电插锁", "models": [{"model": "CL-100", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-200", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-300", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-105S", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-500", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-800", "collection_requirements": "彩页、", "collection_status": "已完成"}]}}}, "电磁锁": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"AL系列": {"level": 3, "type": "系列", "parent": "电磁锁", "models": [{"model": "AL-180", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-180D", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-280（LED)", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-280D（LED)", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-350（LED)", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-350D（LED)", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-500S", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-500DS", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "ZL系列": {"level": 3, "type": "系列", "parent": "电磁锁", "models": [{"model": "ZL-100S", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-100", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-105S", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-300", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-280S", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-280DS", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-280", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-280D", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-280T", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-280DT", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-X280S", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-X280DS", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "CL系列": {"level": 3, "type": "系列", "parent": "电磁锁", "models": [{"model": "CL-280S", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-280DS", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-280", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-280D", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-280T", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-280DT", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-280AS", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "防爆电锁": {"level": 3, "type": "系列", "parent": "电磁锁", "models": [{"model": "MY-EX8610", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}]}}}, "电锁配件": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"AL系列配件": {"level": 3, "type": "系列", "parent": "电锁配件", "models": [{"model": "AL-500S L支架/AL-500S Z支架", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-180PL/AL-180DPL/AL-180PZ/AL-180PU", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-280PL/AL-280DPL/AL-280PZ/AL-280PU", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "AL-350PL/AL-350DPL/AL-350PZ/AL-350PU", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "ZL系列配件": {"level": 3, "type": "系列", "parent": "电锁配件", "models": [{"model": "ZL-280PL/ZL-280PU/ZL-280PZL/ZL-280PZ", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-280DPL", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-280PLC", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "ZL-100X/ZL-100PF/ZL-100PZ/ZL-100PZF", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "CL系列配件": {"level": 3, "type": "系列", "parent": "电锁配件", "models": [{"model": "CL-280PL/CL-280PZ/CL-280PZL", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-280DPL", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-280PU", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-280LC", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-280LCD", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "CL-280DZ", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "70小铝夹/90小钢夹/180大钢夹/180大钢夹上夹", "collection_requirements": "彩页、", "collection_status": "已完成"}]}}}, "电控锁": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"EL系列": {"level": 3, "type": "系列", "parent": "电控锁", "models": [{"model": "EL02", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "EL05", "collection_requirements": "彩页、", "collection_status": "已完成"}]}}}, "出门开关": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"EX-802": {"level": 3, "type": "系列", "parent": "出门开关", "models": [{"model": "EX-802", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "K1系列": {"level": 3, "type": "系列", "parent": "出门开关", "models": [{"model": "K1-1D", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "K1-1DR", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "K2系列": {"level": 3, "type": "系列", "parent": "出门开关", "models": [{"model": "K2-5D/K2-5DA", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "K2-2D/K2-2DA", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "K5系列": {"level": 3, "type": "系列", "parent": "出门开关", "models": [{"model": "K5-5G/K5-5R", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "iB1": {"level": 3, "type": "系列", "parent": "出门开关", "models": [{"model": "iB1", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "Fex119系列": {"level": 3, "type": "系列", "parent": "出门开关", "models": [{"model": "Fex119", "collection_requirements": "彩页、", "collection_status": "已完成"}, {"model": "Fex119-G", "collection_requirements": "彩页、", "collection_status": "已完成"}]}, "BUT系列": {"level": 3, "type": "系列", "parent": "出门开关", "models": [{"model": "BUT100", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}, {"model": "BUT200", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}]}, "防爆出门开关": {"level": 3, "type": "系列", "parent": "出门开关", "models": [{"model": "JT-EX-0225", "collection_requirements": "内容类别:彩页、快速入门指南、产品图片", "collection_status": NaN}]}}}, "配件": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"TDM95系列": {"level": 3, "type": "系列", "parent": "配件", "models": [{"model": "TDM95", "collection_requirements": "彩页、入门指南", "collection_status": "已完成"}, {"model": "TDM95E", "collection_requirements": "彩页、入门指南", "collection_status": "已完成"}]}}}, "立柱支架": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"MateFace20-L": {"level": 3, "type": "系列", "parent": "立柱支架", "models": [{"model": "MateFace20-L", "collection_requirements": "彩页、入门指南", "collection_status": "已完成"}]}, "PZ-11": {"level": 3, "type": "系列", "parent": "立柱支架", "models": [{"model": "PZ-11", "collection_requirements": "彩页、入门指南", "collection_status": "已完成"}]}}}, "门禁梯控": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"EC系列": {"level": 3, "type": "系列", "parent": "门禁梯控", "models": [{"model": "EC-100、EX-16", "collection_requirements": "内容类别:上市指南、彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}, {"model": "EC-300、EX-316", "collection_requirements": "内容类别:上市指南、彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}}}, "门禁控制器铁箱": {"level": 2, "type": "类别", "parent": "门禁产品", "children": {"CASE系列铁箱": {"level": 3, "type": "系列", "parent": "门禁控制器铁箱", "models": [{"model": "CASE05", "collection_requirements": "内容类别:上市指南、彩页、快速入门指南、用户手册、产品图片", "collection_status": NaN}]}}}}}, "软件": {"level": 1, "type": "产品分类", "children": {"软件": {"level": 2, "type": "类别", "parent": "软件", "children": {"ZKTime微服务器": {"level": 3, "type": "系列", "parent": "软件", "models": [{"model": "ZKTime微服务器", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "E-ZKEco Pro微服务器": {"level": 3, "type": "系列", "parent": "软件", "models": [{"model": "E-ZKEco Pro微服务器", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已完成"}]}, "熵基互联": {"level": 3, "type": "系列", "parent": "软件", "models": [{"model": "熵基互联APP", "collection_requirements": "上市指南、用户手册", "collection_status": "已完成"}, {"model": "熵基互联小程序", "collection_requirements": "上市指南、用户手册", "collection_status": "已完成"}]}, "熵基云商": {"level": 3, "type": "系列", "parent": "软件", "models": [{"model": "熵基云商小程序", "collection_requirements": "用户手册", "collection_status": NaN}]}, "万傲瑞达": {"level": 3, "type": "系列", "parent": "软件", "models": [{"model": "万傲瑞达V6600", "collection_requirements": "用户手册", "collection_status": "已完成"}, {"model": "万傲瑞达APP", "collection_requirements": "用户手册", "collection_status": "已完成"}, {"model": "万傲瑞达小程序", "collection_requirements": "用户手册", "collection_status": "已完成"}]}}}}}, "车行产品": {"level": 1, "type": "产品分类", "children": {"自动道闸": {"level": 2, "type": "类别", "parent": "车行产品", "children": {"PBL系列": {"level": 3, "type": "系列", "parent": "自动道闸", "models": [{"model": "PBL800L(45)/PBL800R(45)\nPBL800L(60)/PBL800R(60)", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "PBL900L(45)/PBL900R(45)\nPBL900L(60)/PBL900R(60)\nPBL920L(45)/PBL920R(45)", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "PBL1030L/PBL1030R\nPBL1060L/PBL1060R\nPBL1245L/PBL1245R", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "PBL1030L-DC/PBL1030R-DC\nPBL1060L-DC/PBL1060R-DC\nPBL1245L-DC/PBL1245R-DC", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "PBL2030L/PBL2030R\nPBL2060L/PBL2060R\nPBL2260L/PBL2260R\nPBL2090-50L/PBL2090-50R", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "PBL2045L-DC/PBL2045R-DC\nPBL2060L-DC/PBL2060R-DC\nPBL2230L-DC/PBL2230R-DC\nPBL2245L-DC/PBL2245R-DC", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}, "PBH系列": {"level": 3, "type": "系列", "parent": "自动道闸", "models": [{"model": "PBH6009L/PBH6009R\nPBH6009L(YG)/PBH6009R(YG)\nPBH6009L-90/PBH6009R-90\nPBH6009L(DT)/PBH6009R(DT)\nPBH6015L/PBH6015R\nPBH6015L(YG)/PBH6015R(YG)\nPBH6015L-90/PBH6015R-90\nPBH6015L(DT)/PBH6015R(DT)\nPBH6230L/PBH6230R\nPBH6245-90L/PBH6245-90R", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}, "ZK-BAR系列": {"level": 3, "type": "系列", "parent": "自动道闸", "models": [{"model": "ZK-BAR-DC-2L(50)/ZK-BAR-DC-2R(50)\nZK-BAR-DC-L(45)/ZK-BAR-DC-R(45)\nZK-BAR-DC-L(60)/ZK-BAR-DC-R(60)\nZK-BAR-DC-90L(45)/ZK-BAR-DC-90R(45)\nZK-BAR-DC-90L(60)/ZK-BAR-DC-90R(60)", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}, "Z-C系列": {"level": 3, "type": "系列", "parent": "自动道闸", "models": [{"model": "Z-CDZP603L/Z-CDZP603R\nZ-CDZP606L/Z-CDZP606R\nZ-CDZP620L/Z-CDZP620R", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "Z-CYZ300L(45)/Z-CYZ300R(45)\nZ-CYZ300L(60)/Z-CYZ300R(60)\nZ-CYZ320L(45)/Z-CYZ320R(45)", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}}}, "广告道闸": {"level": 2, "type": "类别", "parent": "车行产品", "children": {"PBG系列": {"level": 3, "type": "系列", "parent": "广告道闸", "models": [{"model": "PBG400L/PBG400R\nPBG405L/PBG405R", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "PBG400L-DC/PBG400R-DC", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "PBG504L/PBG504R\nPBG508L/PBG508R", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}}}, "空降闸": {"level": 2, "type": "类别", "parent": "车行产品", "children": {"PBK系列": {"level": 3, "type": "系列", "parent": "空降闸", "models": [{"model": "PBK2000", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}}}, "车牌识别\n道闸一体机": {"level": 2, "type": "类别", "parent": "车行产品", "children": {"LPR-PB系列": {"level": 3, "type": "系列", "parent": "车牌识别\n道闸一体机", "models": [{"model": "LPR-PB5020L-M5V/LPR-PB5020R-M5V\nLPR-PB5090L-M5V/LPR-PB5090R-M5V\nLPR-PB5010L-M5V/LPR-PB5010R-M5V", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}}}, "车牌识别一体机": {"level": 2, "type": "类别", "parent": "车行产品", "children": {"LPR系列": {"level": 3, "type": "系列", "parent": "车牌识别一体机", "models": [{"model": "LPR100-Y-LCD", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "LPR800", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "LPR900-LV5/LPR900G-LV5", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "LPR1000-LV5", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "LPR6500M-LV5/LPR6500MG-LV5", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "LPR6500MG-M5V(BJ)", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "LPR6600-M5V/LPR6600G-M5V", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "LPR7500-M5V", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "LPR8500-M5V", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "LPR8800", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}, "DPR系列": {"level": 3, "type": "系列", "parent": "车牌识别一体机", "models": [{"model": "DPR1000-LV5", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "DPR2000-LV5", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}, "Z-C系列": {"level": 3, "type": "系列", "parent": "车牌识别一体机", "models": [{"model": "Z-CY300/Z-CY300G", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "Z-CY800", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "Z-CYP600", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}}}, "无人值守自助终端": {"level": 2, "type": "类别", "parent": "车行产品", "children": {"SH系列": {"level": 3, "type": "系列", "parent": "无人值守自助终端", "models": [{"model": "SH1000-A/SH1000-B", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "SH2000-A/SH2000-B", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}}}, "充电桩": {"level": 2, "type": "类别", "parent": "车行产品", "children": {"CP7系列": {"level": 3, "type": "系列", "parent": "充电桩", "models": [{"model": "CP7-100/CP7-100-4G/CP7-100（BG)/CP7-100-4G(BG)", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "CP7-200-HN(BG)/CP7-200BY(BG)/CP7-200-BY-4G(BG)CP7-200-HN/CP7-200BY/CP7-200-BY-4G", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}, "CP8系列": {"level": 3, "type": "系列", "parent": "充电桩", "models": [{"model": "CP8-DC030-1/CP8-DC030-1(BG)", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "CP8-DC060-1/CP8-DC060-2/CP8-DC120-2/CP8-DC160-2", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}, {"model": "CP8-DC240-2", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}}}, "车位管理相机": {"level": 2, "type": "类别", "parent": "车行产品", "children": {"CP-IPC系列": {"level": 3, "type": "系列", "parent": "车位管理相机", "models": [{"model": "CP-IPC300/CP-IPC300(4G)", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}}}, "车位锁 ": {"level": 2, "type": "类别", "parent": "车行产品", "children": {"CWS-RS系列": {"level": 3, "type": "系列", "parent": "车位锁 ", "models": [{"model": "CWS-RS-01/CWS-RS-02", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}}}, "车位引导": {"level": 2, "type": "类别", "parent": "车行产品", "children": {"PG-VD系列": {"level": 3, "type": "系列", "parent": "车位引导", "models": [{"model": "PG-VD04", "collection_requirements": "彩页", "collection_status": "已完成"}, {"model": "PG-VD03-3D/PG-VD03W-3D/PG-VD03-6D", "collection_requirements": "彩页", "collection_status": "已完成"}]}, "UD系列": {"level": 3, "type": "系列", "parent": "车位引导", "models": [{"model": "UD02-LM01/UD03-LM01", "collection_requirements": "彩页", "collection_status": "已完成"}]}, "PG-UD系列": {"level": 3, "type": "系列", "parent": "车位引导", "models": [{"model": "PG-UD01/PG-UD02/PG-UD03", "collection_requirements": "彩页", "collection_status": "已完成"}]}, "PG-LED系列": {"level": 3, "type": "系列", "parent": "车位引导", "models": [{"model": "PG-LED01/PG-LED02/PG-LED03/PK11-G1-LED04", "collection_requirements": "彩页", "collection_status": "已完成"}]}, "PG-MC系列": {"level": 3, "type": "系列", "parent": "车位引导", "models": [{"model": "PG-MC01/PG-MC02", "collection_requirements": "彩页", "collection_status": "已完成"}]}}}, "周边配件": {"level": 2, "type": "类别", "parent": "车行产品", "children": {"PT系列": {"level": 3, "type": "系列", "parent": "周边配件", "models": [{"model": "PT-i3/PT-i3S/PT-i5/PT-i5S", "collection_requirements": "彩页", "collection_status": "已完成"}]}, "ZK-RD系列": {"level": 3, "type": "系列", "parent": "周边配件", "models": [{"model": "ZK-RD01-79(A)", "collection_requirements": "彩页", "collection_status": "已完成"}, {"model": "ZK-RDCF-79", "collection_requirements": "彩页", "collection_status": "已完成"}]}, "ETC系列": {"level": 3, "type": "系列", "parent": "周边配件", "models": [{"model": "ETC100", "collection_requirements": "彩页、用户手册", "collection_status": "已完成"}]}, "剩余车位显示屏系列": {"level": 3, "type": "系列", "parent": "周边配件", "models": [{"model": "ZK-LED-002", "collection_requirements": "彩页", "collection_status": "已完成"}, {"model": "ZK-LED-004", "collection_requirements": "彩页", "collection_status": "已完成"}, {"model": "RSP-LED-1 ", "collection_requirements": "彩页", "collection_status": "已完成"}]}, "车辆检测器系列": {"level": 3, "type": "系列", "parent": "周边配件", "models": [{"model": "PSA02", "collection_requirements": "彩页", "collection_status": "已完成"}, {"model": "ZK-VDT-M-001", "collection_requirements": "彩页", "collection_status": "已完成"}, {"model": "ZK-VDT-S-002", "collection_requirements": "彩页", "collection_status": "已完成"}]}, "地感线圈系列": {"level": 3, "type": "系列", "parent": "周边配件", "models": [{"model": "PSA03", "collection_requirements": "彩页", "collection_status": "已完成"}]}}}}}, "智能锁产品": {"level": 1, "type": "产品分类", "children": {"智能家用办公锁": {"level": 2, "type": "类别", "parent": "智能锁产品", "children": {"ZM系列": {"level": 3, "type": "系列", "parent": "智能家用办公锁", "models": [{"model": "ZM400", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "ZM300", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "ZM301", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "ZM302", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}]}, "P2系列": {"level": 3, "type": "系列", "parent": "智能家用办公锁", "models": [{"model": "P2-L", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "P2-W", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "P2-Z", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "P2-Pro", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}]}, "K200系列": {"level": 3, "type": "系列", "parent": "智能家用办公锁", "models": [{"model": "K200", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}]}}}, "配件": {"level": 2, "type": "类别", "parent": "智能锁产品", "children": {"配件": {"level": 3, "type": "系列", "parent": "配件", "models": [{"model": "遥控器", "collection_requirements": "彩页", "collection_status": "已收集"}]}}}, "集中管理锁": {"level": 2, "type": "类别", "parent": "智能锁产品", "children": {"P3系列": {"level": 3, "type": "系列", "parent": "集中管理锁", "models": [{"model": "P3-BH13", "collection_requirements": "彩页、快速使用指南", "collection_status": "已收集"}, {"model": "P3-T01", "collection_requirements": "彩页、快速使用指南", "collection_status": "已收集"}, {"model": "P3-CAT-600H", "collection_requirements": "彩页、快速使用指南", "collection_status": "已收集"}, {"model": "P3-W-600H", "collection_requirements": "彩页、快速使用指南", "collection_status": "已收集"}, {"model": "P3-CAT-700H", "collection_requirements": "彩页、快速使用指南", "collection_status": "已收集"}, {"model": "P3-W-700H", "collection_requirements": "彩页、快速使用指南", "collection_status": "已收集"}]}}}, "单机版酒店锁": {"level": 2, "type": "类别", "parent": "智能锁产品", "children": {"LH系列": {"level": 3, "type": "系列", "parent": "单机版酒店锁", "models": [{"model": "LH1000", "collection_requirements": "彩页、用户手册（单机版软件）", "collection_status": "已收集"}, {"model": "LH3000", "collection_requirements": "彩页、用户手册（单机版软件）", "collection_status": "已收集"}, {"model": "LH4000", "collection_requirements": "彩页、用户手册（单机版软件）", "collection_status": "已收集"}, {"model": "LH5000", "collection_requirements": "彩页、用户手册（单机版软件）", "collection_status": "已收集"}, {"model": "LH6000", "collection_requirements": "彩页、用户手册（单机版软件）", "collection_status": "已收集"}, {"model": "LH6500", "collection_requirements": "彩页、用户手册（单机版软件）", "collection_status": "已收集"}, {"model": "LH6800", "collection_requirements": "彩页、用户手册（单机版软件）", "collection_status": "已收集"}]}}}, "发卡器": {"level": 2, "type": "类别", "parent": "智能锁产品", "children": {"CR系列": {"level": 3, "type": "系列", "parent": "发卡器", "models": [{"model": "CR60W", "collection_requirements": "彩页、高清图片", "collection_status": "已收集"}]}}}, "玻璃门锁": {"level": 2, "type": "类别", "parent": "智能锁产品", "children": {"GDL系列": {"level": 3, "type": "系列", "parent": "玻璃门锁", "models": [{"model": "GDL-PFC60", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "GDL-PFC60W", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "GDL-PFC80", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "GDL-PFC80L", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "GDL-PFC90", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "GDL-PFC90L", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "GDL-PFC90-F", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "GDL-F618", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}, {"model": "GDL-F618-F", "collection_requirements": "彩页、用户手册", "collection_status": "已收集"}]}}}}}, "人证产品": {"level": 1, "type": "产品分类", "children": {"生物识别采集器": {"level": 2, "type": "类别", "parent": "人证产品", "children": {"指纹系列": {"level": 3, "type": "系列", "parent": "生物识别采集器", "models": [{"model": "Live10R", "collection_requirements": "彩页、高清图片", "collection_status": NaN}, {"model": "Live20R", "collection_requirements": "彩页、高清图片", "collection_status": NaN}, {"model": "FS200", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "FS300", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ZK6000A", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ZK7000A", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ZK4500", "collection_requirements": "彩页、高清图片", "collection_status": NaN}]}, "手掌系列": {"level": 3, "type": "系列", "parent": "生物识别采集器", "models": [{"model": "PAR200", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "PAR300", "collection_requirements": "彩页、高清图片", "collection_status": NaN}]}, "指静脉系列": {"level": 3, "type": "系列", "parent": "生物识别采集器", "models": [{"model": "FV1000", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}]}}}, "生物识别模组": {"level": 2, "type": "类别", "parent": "人证产品", "children": {"指纹系列": {"level": 3, "type": "系列", "parent": "生物识别模组", "models": [{"model": "live20M", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "live30M", "collection_requirements": "彩页、高清图片", "collection_status": NaN}, {"model": "Live31M", "collection_requirements": "彩页、高清图片", "collection_status": NaN}, {"model": "ZK6000A-R", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ZK7000A-R", "collection_requirements": "高清图片", "collection_status": NaN}, {"model": "FS200P-R", "collection_requirements": "彩页、高清图片", "collection_status": NaN}]}, "手掌系列": {"level": 3, "type": "系列", "parent": "生物识别模组", "models": [{"model": "PAM220", "collection_requirements": "彩页、高清图片", "collection_status": NaN}, {"model": "PAM300", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}]}, "面部系列": {"level": 3, "type": "系列", "parent": "生物识别模组", "models": [{"model": "FAM520", "collection_requirements": "彩页、高清图片", "collection_status": NaN}]}, "指静脉系列": {"level": 3, "type": "系列", "parent": "生物识别模组", "models": [{"model": "FVM100", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}]}}}, "人证核验产品": {"level": 2, "type": "类别", "parent": "人证产品", "children": {"桌面式": {"level": 3, "type": "系列", "parent": "人证核验产品", "models": [{"model": "ID2000", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID5004", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID5004-D", "collection_requirements": "彩页、高清图片", "collection_status": NaN}, {"model": "ID5005", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID5006", "collection_requirements": "彩页、高清图片", "collection_status": NaN}, {"model": "ID860-S", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID860-D", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}]}, "访客系列": {"level": 3, "type": "系列", "parent": "人证核验产品", "models": [{"model": "ZKVD200Pro", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID860-D-V01", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID5008", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ZKVD200Pro", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID860-D-V01", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID5008", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}]}, "立式": {"level": 3, "type": "系列", "parent": "人证核验产品", "models": [{"model": "ID830-V", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID830-H", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID5007", "collection_requirements": "彩页、高清图片", "collection_status": NaN}, {"model": "ID5008", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}]}, "壁挂式": {"level": 3, "type": "系列", "parent": "人证核验产品", "models": [{"model": "ID700-A/B/C", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID900-A/B/C", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID880", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}]}, "手持系列": {"level": 3, "type": "系列", "parent": "人证核验产品", "models": [{"model": "ID500", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID510", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID520", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}]}}}, "身份证阅读机具": {"level": 2, "type": "类别", "parent": "人证产品", "children": {"台式ID系列": {"level": 3, "type": "系列", "parent": "身份证阅读机具", "models": [{"model": "ID100", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID110", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID180", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID200", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID300", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID310", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}]}, "内置式IDM系列": {"level": 3, "type": "系列", "parent": "身份证阅读机具", "models": [{"model": "IDM20", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "IDM40", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "IDM50", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}]}, "多功能系列": {"level": 3, "type": "系列", "parent": "身份证阅读机具", "models": [{"model": "ID320-A/B/C", "collection_requirements": "彩页、用户手册、高清图片", "collection_status": NaN}, {"model": "ID350", "collection_requirements": "彩页、高清图片", "collection_status": NaN}]}}}}}, "NaN": {"level": 1, "type": "产品分类", "children": {"人证核验产品": {"level": 2, "type": "类别", "parent": NaN, "children": {"桌面式": {"level": 3, "type": "系列", "parent": "人证核验产品", "models": [{"model": "TID100", "collection_requirements": "彩页、高清图片", "collection_status": NaN}, {"model": "TID200", "collection_requirements": "彩页、高清图片", "collection_status": NaN}]}}}}}, "安检产品": {"level": 1, "type": "产品分类", "children": {"安检机": {"level": 2, "type": "类别", "parent": "安检产品", "children": {"5030系列": {"level": 3, "type": "系列", "parent": "安检机", "models": [{"model": "LD5030", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "LD5030C", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "ZKX-5030", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "ZKX5030", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}, "6550系列": {"level": 3, "type": "系列", "parent": "安检机", "models": [{"model": "ZKX-6550", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "ZKX6550", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "ZKX6550D", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}, "10080系列": {"level": 3, "type": "系列", "parent": "安检机", "models": [{"model": "ZKX10080", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}}}, "安检门": {"level": 2, "type": "类别", "parent": "安检产品", "children": {"金属安检门系列": {"level": 3, "type": "系列", "parent": "安检门", "models": [{"model": "ZK-D1065", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "ZK-D2180", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "ZK-D3180", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "ZK-D4330", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "ZK-D2180P", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "ZK-D2180 pro", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}, "分类安检门系列": {"level": 3, "type": "系列", "parent": "安检门", "models": [{"model": "ZK-D6180", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "ZK-D6180 pro", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "ZK-D7180 ", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}, "通道安检门系列": {"level": 3, "type": "系列", "parent": "安检门", "models": [{"model": "ZK-D6180 Plus", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "ZK-D7180 plus", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}}}, "安检棒": {"level": 2, "type": "类别", "parent": "安检产品", "children": {"手持金属检测系列": {"level": 3, "type": "系列", "parent": "安检棒", "models": [{"model": "ZK-D100S", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}}}, "升降柱": {"level": 2, "type": "类别", "parent": "安检产品", "children": {"液压升降柱系列": {"level": 3, "type": "系列", "parent": "升降柱", "models": [{"model": "LZYZ-B2-ZK1220", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}, {"model": "LZYZ-B2-ZK1220（浅埋款）", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}, "固定柱系列": {"level": 3, "type": "系列", "parent": "升降柱", "models": [{"model": "ZK-BOL1219-F", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}}}, "路障机": {"level": 2, "type": "类别", "parent": "安检产品", "children": {"液压路障机系列": {"level": 3, "type": "系列", "parent": "路障机", "models": [{"model": "ZK-RB1000", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}}}, "车底检查系统": {"level": 2, "type": "类别", "parent": "安检产品", "children": {"固定车底系列": {"level": 3, "type": "系列", "parent": "车底检查系统", "models": [{"model": "ZK-VSCN100", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}, "移动车底系列": {"level": 3, "type": "系列", "parent": "车底检查系统", "models": [{"model": "ZK-VSCN200", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}}}, "防爆产品": {"level": 2, "type": "类别", "parent": "安检产品", "children": {"防爆毯系列": {"level": 3, "type": "系列", "parent": "防爆产品", "models": [{"model": "FBT-ZK-E1", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}, "防爆罐系列": {"level": 3, "type": "系列", "parent": "防爆产品", "models": [{"model": "FBG-G1-ZK-E2", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}}}, "危险液体检查仪": {"level": 2, "type": "类别", "parent": "安检产品", "children": {"手持系列": {"level": 3, "type": "系列", "parent": "危险液体检查仪", "models": [{"model": "ZK-LD800", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}, "台式系列": {"level": 3, "type": "系列", "parent": "危险液体检查仪", "models": [{"model": "ZK-LD4000", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}}}, "磁性探测立柱": {"level": 2, "type": "类别", "parent": "安检产品", "children": {"铁磁探测系列": {"level": 3, "type": "系列", "parent": "磁性探测立柱", "models": [{"model": "ZK-D7000", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}}}, "鞋底金属探测器": {"level": 2, "type": "类别", "parent": "安检产品", "children": {"鞋底探测系列": {"level": 3, "type": "系列", "parent": "鞋底金属探测器", "models": [{"model": "ZK-D600", "collection_requirements": "彩页、用户手册、高清图", "collection_status": NaN}]}}}}}, "视频产品": {"level": 1, "type": "产品分类", "children": {"网络摄像机": {"level": 2, "type": "类别", "parent": "视频产品", "children": {"BL系列": {"level": 3, "type": "系列", "parent": "网络摄像机", "models": [{"model": "BL-954F48A-S3-S-HL", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}]}, "BS系列": {"level": 3, "type": "系列", "parent": "网络摄像机", "models": [{"model": "BS-954F32C-S3-MI-S-HL", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}, {"model": "BS-952O22C-S5-MI", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}, {"model": "BS-954N22C-S7-MI", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}]}, "ES系列": {"level": 3, "type": "系列", "parent": "网络摄像机", "models": [{"model": "ES-954F31C-S3-MI-S-HL", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}, {"model": "ES-952O21C-S5-MI", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}, {"model": "ES-954N21C-S7-MI", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}]}}}, "网络硬盘录像机": {"level": 2, "type": "类别", "parent": "视频产品", "children": {"网络硬盘录像机": {"level": 3, "type": "系列", "parent": "网络硬盘录像机", "models": [{"model": "Z9504NEQ/Z9508NEQ", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}, {"model": "Z9516NFR-S", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}, {"model": "Z9536NHR", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}, {"model": "Z9564NHR", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}, {"model": "Z95128NTR", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}]}}}, "智能视频盒": {"level": 2, "type": "类别", "parent": "视频产品", "children": {"IVS系列": {"level": 3, "type": "系列", "parent": "智能视频盒", "models": [{"model": "XC-IVS8008-01/XC-IVS8008-02", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}, {"model": "XC-IVS8008-1U", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}, {"model": "IVS-X1-08/IVS-X1-16", "collection_requirements": "彩页、用户手册、上市指南", "collection_status": "提交"}]}}}}}, "消费产品": {"level": 1, "type": "产品分类", "children": {"消费机": {"level": 2, "type": "类别", "parent": "消费产品", "children": {"CM离线消费系列": {"level": 3, "type": "系列", "parent": "消费机", "models": [{"model": "CM20", "collection_requirements": "彩页、用户手册", "collection_status": "已提交"}, {"model": "CM102", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}, {"model": "CM50", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}, {"model": "CM60", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}]}, "ZTHP离线消费系列": {"level": 3, "type": "系列", "parent": "消费机", "models": [{"model": "ZTHP50", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}, {"model": "ZTHP60", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}]}, "CM在线消费系列": {"level": 3, "type": "系列", "parent": "消费机", "models": [{"model": "CM10", "collection_requirements": "彩页、用户手册", "collection_status": "已提交"}, {"model": "CM101", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}, {"model": "CM105", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}, {"model": "CM40", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}, {"model": "CM70", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}, {"model": "CM300", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}, {"model": "CM500", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}]}, "ZTHP在线消费系列": {"level": 3, "type": "系列", "parent": "消费机", "models": [{"model": "ZTHP40", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}, {"model": "ZTHP70", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}, {"model": "ZTHP300", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}, {"model": "ZTHP510", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}, {"model": "ZTHP560", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}]}}}, "消费产品辅件": {"level": 2, "type": "类别", "parent": "消费产品", "children": {"充值机": {"level": 3, "type": "系列", "parent": "消费产品辅件", "models": [{"model": "ZTHP800", "collection_requirements": "彩页、入门指南、用户手册", "collection_status": "已提交"}]}, "消费机支架": {"level": 3, "type": "系列", "parent": "消费产品辅件", "models": [{"model": "CM300-HZJ", "collection_requirements": "快速安装指南", "collection_status": "已提交"}, {"model": "ZK300S", "collection_requirements": "快速安装指南", "collection_status": "已提交"}]}}}}}, "通道产品": {"level": 1, "type": "产品分类", "children": {"三辊闸": {"level": 2, "type": "类别", "parent": "通道产品", "children": {"TS系列": {"level": 3, "type": "系列", "parent": "三辊闸", "models": [{"model": "TS2000/TS2000Pro", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "TS1000Pro", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "TS100", "collection_requirements": "彩页、用户手册", "collection_status": NaN}]}, "TSM系列": {"level": 3, "type": "系列", "parent": "三辊闸", "models": [{"model": "TSM3000", "collection_requirements": "彩页、用户手册", "collection_status": NaN}]}}}, "翼闸": {"level": 2, "type": "类别", "parent": "通道产品", "children": {"FBL系列": {"level": 3, "type": "系列", "parent": "翼闸", "models": [{"model": "FBL100-主A/FBL100-从A/FBL100-从B/FBL120-AA/FBL120-BB/FBL120-AB", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "FBL2000-主A/FBL2000-从A/FBL2000-从B/FBL2200-AA/FBL2200-BB/FBL2200-AB", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "FBL2000PLUS-主A/FBLL2000PLUS-从A/FBLL2000PLUS-从B/FBLL2000PLUS-AA/FBLL2000PLUS-BB/FBLL2000PLUS-AB", "collection_requirements": "彩页、用户手册", "collection_status": NaN}]}, "FBM系列": {"level": 3, "type": "系列", "parent": "翼闸", "models": [{"model": "FBM1000-FS-主A/FBM1000-FS-从A/FBM1000-FS-从B/FBM1200-FS-AA/FBM1200-FS-BB/FBM1200-FS-AB", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "FBM3000-主A/FBM3000-从A/FBM3000-从B/FBM3200-AA/FBM3200-BB/FBM3200-AB", "collection_requirements": "彩页、用户手册", "collection_status": NaN}]}}}, "摆闸": {"level": 2, "type": "类别", "parent": "通道产品", "children": {"SBTL系列": {"level": 3, "type": "系列", "parent": "摆闸", "models": [{"model": "SBT1000S", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "SBTL100-副机/SBTL100-主机/SBTL120", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "SBTL310-L/SBTL310-R/SBTL310-M", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "SBTL310PLUS-L/SBTL310PLUS-R/SBTL310PLUS-M", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "SBTL360-L/SBTL360-R/SBTL360-M", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "SBTL2000 PLUS-主机/SBTL2000 PLUS-副机/SBTL2200 PLUS-双机芯", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "SBTL500 PLUS-主机/SBTL500 PLUS-副机/SBTL520 PLUS-双机芯", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "SBTL2000-主机/SBTL2000-副机/SBTL2200", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "SBTL2000-YSK-L(副机)/SBTL2000-YSK-L(主机)/SBTL2200-YSK(双机芯)", "collection_requirements": "彩页、用户手册", "collection_status": NaN}]}, "SBTM系列": {"level": 3, "type": "系列", "parent": "摆闸", "models": [{"model": "SBTM1000主机SBTM1000副机SBTM1200-双机芯", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "SBTM5000-FS-主机SBTM5000-FS-副机SBTM5200-FS-双机芯", "collection_requirements": "彩页、用户手册", "collection_status": NaN}]}, "Mars系列": {"level": 3, "type": "系列", "parent": "摆闸", "models": [{"model": "Mars-B10-主机/Mars-B10-副机/Mars-B12-双机芯", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "Mars-S10-主机/Mars-S10-副机/Mars -S12双机芯", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "Mars-S20-主机/Mars-S20-副机/Mars -S22", "collection_requirements": "彩页、用户手册", "collection_status": NaN}]}, "SBTH系列": {"level": 3, "type": "系列", "parent": "摆闸", "models": [{"model": "SBTH1000-FS-主机/SBTH1000-FS-副机/SBTH1200-FS", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "SBTH2000/SBTH2000S", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "SBTH4000-主机/SBTH4000-副机/SBTH4200-双机芯", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "SBTH9000主机/SBTH9000从机/SBTH9200", "collection_requirements": "彩页、用户手册", "collection_status": NaN}]}}}, "转闸": {"level": 2, "type": "类别", "parent": "通道产品", "children": {"FHT系列": {"level": 3, "type": "系列", "parent": "转闸", "models": [{"model": "FHT2300DL/FHT2400DL", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "FHT3300L/FHT3400L", "collection_requirements": "彩页、用户手册", "collection_status": NaN}]}}}, "广告门": {"level": 2, "type": "类别", "parent": "通道产品", "children": {"ADR系列": {"level": 3, "type": "系列", "parent": "广告门", "models": [{"model": "ADR2000/ADR2000PRO", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "ADR5000/ADR5000PRO", "collection_requirements": "彩页、用户手册", "collection_status": NaN}, {"model": "ADR3000", "collection_requirements": "彩页、用户手册", "collection_status": NaN}]}}}, "开门机": {"level": 2, "type": "类别", "parent": "通道产品", "children": {"ASD100系列": {"level": 3, "type": "系列", "parent": "开门机", "models": [{"model": "ASD100-Z1", "collection_requirements": "彩页、用户手册", "collection_status": NaN}]}}}, "自动伸缩门": {"level": 2, "type": "类别", "parent": "通道产品", "children": {"自动伸缩门系列": {"level": 3, "type": "系列", "parent": "自动伸缩门", "models": [{"model": "TS-FG-10AL/TS-FG-10AR/TS-FG-10BL/TS-FG-10BR/TS-10A/TS-10B", "collection_requirements": "彩页、用户手册", "collection_status": NaN}]}}}}}}, "category_insert_sql": ["\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (1, '考勤产品', NULL, 1, '产品分类', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (2, '考勤机', 1, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (3, 'X系列', 2, 3, '系列', 'X系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (4, 'ZK系列', 2, 3, '系列', 'ZK系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (5, 'S系列', 2, 3, '系列', 'S系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (6, '钉钉系列', 2, 3, '系列', '钉钉系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (7, 'TX系列', 2, 3, '系列', 'TX系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (8, 'U系列', 2, 3, '系列', 'U系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (9, 'iClock系列', 2, 3, '系列', 'iClock系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (10, 'WX系列', 2, 3, '系列', 'WX系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (11, 'ZQ系列', 2, 3, '系列', 'ZQ系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (12, 'FS系列', 2, 3, '系列', 'FS系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (13, 'nFace系列', 2, 3, '系列', 'nFace系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (14, 'UF系列', 2, 3, '系列', 'UF系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (15, 'iFace系列', 2, 3, '系列', 'iFace系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (16, 'xFace系列', 2, 3, '系列', 'xFace系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (17, 'HORUS系列', 2, 3, '系列', 'HORUS系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (18, 'BK系列', 2, 3, '系列', 'BK系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (19, 'ZKTeco+系列', 2, 3, '系列', 'ZKTeco+系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (20, 'M系列', 2, 3, '系列', 'M系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (21, 'FA系列', 2, 3, '系列', 'FA系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (22, '门禁产品', NULL, 1, '产品分类', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (23, '门禁一体机', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (24, 'SC系列', 23, 3, '系列', 'SC系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (25, 'MCR系列', 23, 3, '系列', 'MCR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (26, 'M系列', 23, 3, '系列', 'M系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (27, 'X系列', 23, 3, '系列', 'X系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (28, 'Smart系列', 23, 3, '系列', 'Smart系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (29, 'OF系列', 23, 3, '系列', 'OF系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (30, 'F系列', 23, 3, '系列', 'F系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (31, 'ZKTeco+系列', 23, 3, '系列', 'ZKTeco+系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (32, 'WX系列', 23, 3, '系列', 'WX系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (33, 'iClock系列', 23, 3, '系列', 'iClock系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (34, 'iFace系列', 23, 3, '系列', 'iFace系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (35, 'ZF系列', 23, 3, '系列', 'ZF系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (36, 'ZK系列', 23, 3, '系列', 'ZK系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (37, 'UF系列', 23, 3, '系列', 'UF系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (38, 'Xpalm系列', 23, 3, '系列', 'Xpalm系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (39, 'FJ系列', 23, 3, '系列', 'FJ系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (40, 'BioFace系列', 23, 3, '系列', 'BioFace系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (41, 'nFace系列', 23, 3, '系列', 'nFace系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (42, 'zFace系列', 23, 3, '系列', 'zFace系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (43, 'xFace系列', 23, 3, '系列', 'xFace系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (44, 'FS系列', 23, 3, '系列', 'FS系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (45, 'AI系列', 23, 3, '系列', 'AI系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (46, 'MateFace系列', 23, 3, '系列', 'MateFace系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (47, '无线门禁系列', 23, 3, '系列', '无线门禁系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (48, 'T系列非可见光', 23, 3, '系列', 'T系列非可见光', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (49, 'TDB系列', 23, 3, '系列', 'TDB系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (50, 'T系列可见光', 23, 3, '系列', 'T系列可见光', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (51, '机械锁', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (52, 'UX系列', 51, 3, '系列', 'UX系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (53, '采集器', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (54, 'ZK系列', 53, 3, '系列', 'ZK系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (55, 'CR系列', 53, 3, '系列', 'CR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (56, 'MC系列', 53, 3, '系列', 'MC系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (57, 'GM系列', 53, 3, '系列', 'GM系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (58, '发卡器', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (59, 'CR系列', 58, 3, '系列', 'CR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (60, '读卡器', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (61, 'KR系列', 60, 3, '系列', 'KR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (62, 'FR系列', 60, 3, '系列', 'FR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (63, 'KD系列', 60, 3, '系列', 'KD系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (64, 'QR系列', 60, 3, '系列', 'QR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (65, 'KQR系列', 60, 3, '系列', 'KQR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (66, 'ZR系列', 60, 3, '系列', 'ZR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (67, 'KM系列', 60, 3, '系列', 'KM系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (68, 'FI系列', 60, 3, '系列', 'FI系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (69, '防爆读头系列', 60, 3, '系列', '防爆读头系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (70, 'GR系列', 60, 3, '系列', 'GR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (71, '一维扫描枪', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (72, 'ZK系列', 71, 3, '系列', 'ZK系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (73, '二维扫描枪', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (74, 'ZK系列', 73, 3, '系列', 'ZK系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (75, '二维扫描平台', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (76, 'ZK系列', 75, 3, '系列', 'ZK系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (77, '条码扫描器', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (78, 'ZK系列', 77, 3, '系列', 'ZK系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (79, '门禁控制器', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (80, 'C3系列', 79, 3, '系列', 'C3系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (81, 'C4系列', 79, 3, '系列', 'C4系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (82, 'InBio系列', 79, 3, '系列', 'InBio系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (83, 'K2系列', 79, 3, '系列', 'K2系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (84, 'ZTHCAM系列', 79, 3, '系列', 'ZTHCAM系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (85, 'ZTHCAMPRO系列', 79, 3, '系列', 'ZTHCAMPRO系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (86, 'C5系列', 79, 3, '系列', 'C5系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (87, 'InBioP系列', 79, 3, '系列', 'InBioP系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (88, '控制器配件', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (89, 'ZKPSM系列', 88, 3, '系列', 'ZKPSM系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (90, 'TPM系列', 88, 3, '系列', 'TPM系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (91, 'CASE系列铁箱', 88, 3, '系列', 'CASE系列铁箱', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (92, '门禁线性电源', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (93, 'AP系列', 92, 3, '系列', 'AP系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (94, '电插锁', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (95, 'AL系列', 94, 3, '系列', 'AL系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (96, 'CL系列', 94, 3, '系列', 'CL系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (97, '电磁锁', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (98, 'AL系列', 97, 3, '系列', 'AL系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (99, 'ZL系列', 97, 3, '系列', 'ZL系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (100, 'CL系列', 97, 3, '系列', 'CL系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (101, '防爆电锁', 97, 3, '系列', '防爆电锁', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (102, '电锁配件', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (103, 'AL系列配件', 102, 3, '系列', 'AL系列配件', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (104, 'ZL系列配件', 102, 3, '系列', 'ZL系列配件', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (105, 'CL系列配件', 102, 3, '系列', 'CL系列配件', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (106, '电控锁', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (107, 'EL系列', 106, 3, '系列', 'EL系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (108, '出门开关', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (109, 'EX-802', 108, 3, '系列', 'EX-802', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (110, 'K1系列', 108, 3, '系列', 'K1系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (111, 'K2系列', 108, 3, '系列', 'K2系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (112, 'K5系列', 108, 3, '系列', 'K5系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (113, 'iB1', 108, 3, '系列', 'iB1', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (114, 'Fex119系列', 108, 3, '系列', 'Fex119系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (115, 'BUT系列', 108, 3, '系列', 'BUT系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (116, '防爆出门开关', 108, 3, '系列', '防爆出门开关', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (117, '配件', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (118, 'TDM95系列', 117, 3, '系列', 'TDM95系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (119, '立柱支架', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (120, 'MateFace20-L', 119, 3, '系列', 'MateFace20-L', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (121, 'PZ-11', 119, 3, '系列', 'PZ-11', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (122, '门禁梯控', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (123, 'EC系列', 122, 3, '系列', 'EC系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (124, '门禁控制器铁箱', 22, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (125, 'CASE系列铁箱', 124, 3, '系列', 'CASE系列铁箱', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (126, '软件', NULL, 1, '产品分类', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (127, '软件', 126, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (128, 'ZKTime微服务器', 127, 3, '系列', 'ZKTime微服务器', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (129, 'E-ZKEco Pro微服务器', 127, 3, '系列', 'E-ZKEco Pro微服务器', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (130, '熵基互联', 127, 3, '系列', '熵基互联', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (131, '熵基云商', 127, 3, '系列', '熵基云商', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (132, '万傲瑞达', 127, 3, '系列', '万傲瑞达', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (133, '车行产品', NULL, 1, '产品分类', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (134, '自动道闸', 133, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (135, 'PBL系列', 134, 3, '系列', 'PBL系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (136, 'PBH系列', 134, 3, '系列', 'PBH系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (137, 'ZK-BAR系列', 134, 3, '系列', 'ZK-BAR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (138, 'Z-C系列', 134, 3, '系列', 'Z-C系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (139, '广告道闸', 133, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (140, 'PBG系列', 139, 3, '系列', 'PBG系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (141, '空降闸', 133, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (142, 'PBK系列', 141, 3, '系列', 'PBK系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (143, '车牌识别\n道闸一体机', 133, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (144, 'LPR-PB系列', 143, 3, '系列', 'LPR-PB系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (145, '车牌识别一体机', 133, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (146, 'LPR系列', 145, 3, '系列', 'LPR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (147, 'DPR系列', 145, 3, '系列', 'DPR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (148, 'Z-C系列', 145, 3, '系列', 'Z-C系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (149, '无人值守自助终端', 133, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (150, 'SH系列', 149, 3, '系列', 'SH系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (151, '充电桩', 133, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (152, 'CP7系列', 151, 3, '系列', 'CP7系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (153, 'CP8系列', 151, 3, '系列', 'CP8系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (154, '车位管理相机', 133, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (155, 'CP-IPC系列', 154, 3, '系列', 'CP-IPC系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (156, '车位锁 ', 133, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (157, 'CWS-RS系列', 156, 3, '系列', 'CWS-RS系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (158, '车位引导', 133, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (159, 'PG-VD系列', 158, 3, '系列', 'PG-VD系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (160, 'UD系列', 158, 3, '系列', 'UD系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (161, 'PG-UD系列', 158, 3, '系列', 'PG-UD系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (162, 'PG-LED系列', 158, 3, '系列', 'PG-LED系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (163, 'PG-MC系列', 158, 3, '系列', 'PG-MC系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (164, '周边配件', 133, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (165, 'PT系列', 164, 3, '系列', 'PT系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (166, 'ZK-RD系列', 164, 3, '系列', 'ZK-RD系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (167, 'ETC系列', 164, 3, '系列', 'ETC系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (168, '剩余车位显示屏系列', 164, 3, '系列', '剩余车位显示屏系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (169, '车辆检测器系列', 164, 3, '系列', '车辆检测器系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (170, '地感线圈系列', 164, 3, '系列', '地感线圈系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (171, '智能锁产品', NULL, 1, '产品分类', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (172, '智能家用办公锁', 171, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (173, 'ZM系列', 172, 3, '系列', 'ZM系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (174, 'P2系列', 172, 3, '系列', 'P2系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (175, 'K200系列', 172, 3, '系列', 'K200系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (176, '配件', 171, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (177, '配件', 176, 3, '系列', '配件', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (178, '集中管理锁', 171, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (179, 'P3系列', 178, 3, '系列', 'P3系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (180, '单机版酒店锁', 171, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (181, 'LH系列', 180, 3, '系列', 'LH系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (182, '发卡器', 171, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (183, 'CR系列', 182, 3, '系列', 'CR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (184, '玻璃门锁', 171, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (185, 'GDL系列', 184, 3, '系列', 'GDL系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (186, '人证产品', NULL, 1, '产品分类', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (187, '生物识别采集器', 186, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (188, '指纹系列', 187, 3, '系列', '指纹系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (189, '手掌系列', 187, 3, '系列', '手掌系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (190, '指静脉系列', 187, 3, '系列', '指静脉系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (191, '生物识别模组', 186, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (192, '指纹系列', 191, 3, '系列', '指纹系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (193, '手掌系列', 191, 3, '系列', '手掌系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (194, '面部系列', 191, 3, '系列', '面部系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (195, '指静脉系列', 191, 3, '系列', '指静脉系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (196, '人证核验产品', 186, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (197, '桌面式', 196, 3, '系列', '桌面式', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (198, '访客系列', 196, 3, '系列', '访客系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (199, '立式', 196, 3, '系列', '立式', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (200, '壁挂式', 196, 3, '系列', '壁挂式', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (201, '手持系列', 196, 3, '系列', '手持系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (202, '身份证阅读机具', 186, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (203, '台式ID系列', 202, 3, '系列', '台式ID系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (204, '内置式IDM系列', 202, 3, '系列', '内置式IDM系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (205, '多功能系列', 202, 3, '系列', '多功能系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (206, 'nan', NULL, 1, '产品分类', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (207, '人证核验产品', 206, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (208, '桌面式', 207, 3, '系列', '桌面式', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (209, '安检产品', NULL, 1, '产品分类', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (210, '安检机', 209, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (211, '5030系列', 210, 3, '系列', '5030系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (212, '6550系列', 210, 3, '系列', '6550系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (213, '10080系列', 210, 3, '系列', '10080系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (214, '安检门', 209, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (215, '金属安检门系列', 214, 3, '系列', '金属安检门系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (216, '分类安检门系列', 214, 3, '系列', '分类安检门系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (217, '通道安检门系列', 214, 3, '系列', '通道安检门系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (218, '安检棒', 209, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (219, '手持金属检测系列', 218, 3, '系列', '手持金属检测系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (220, '升降柱', 209, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (221, '液压升降柱系列', 220, 3, '系列', '液压升降柱系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (222, '固定柱系列', 220, 3, '系列', '固定柱系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (223, '路障机', 209, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (224, '液压路障机系列', 223, 3, '系列', '液压路障机系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (225, '车底检查系统', 209, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (226, '固定车底系列', 225, 3, '系列', '固定车底系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (227, '移动车底系列', 225, 3, '系列', '移动车底系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (228, '防爆产品', 209, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (229, '防爆毯系列', 228, 3, '系列', '防爆毯系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (230, '防爆罐系列', 228, 3, '系列', '防爆罐系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (231, '危险液体检查仪', 209, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (232, '手持系列', 231, 3, '系列', '手持系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (233, '台式系列', 231, 3, '系列', '台式系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (234, '磁性探测立柱', 209, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (235, '铁磁探测系列', 234, 3, '系列', '铁磁探测系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (236, '鞋底金属探测器', 209, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (237, '鞋底探测系列', 236, 3, '系列', '鞋底探测系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (238, '视频产品', NULL, 1, '产品分类', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (239, '网络摄像机', 238, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (240, 'BL系列', 239, 3, '系列', 'BL系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (241, 'BS系列', 239, 3, '系列', 'BS系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (242, 'ES系列', 239, 3, '系列', 'ES系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (243, '网络硬盘录像机', 238, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (244, '网络硬盘录像机', 243, 3, '系列', '网络硬盘录像机', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (245, '智能视频盒', 238, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (246, 'IVS系列', 245, 3, '系列', 'IVS系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (247, '消费产品', NULL, 1, '产品分类', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (248, '消费机', 247, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (249, 'CM离线消费系列', 248, 3, '系列', 'CM离线消费系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (250, 'ZTHP离线消费系列', 248, 3, '系列', 'ZTHP离线消费系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (251, 'CM在线消费系列', 248, 3, '系列', 'CM在线消费系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (252, 'ZTHP在线消费系列', 248, 3, '系列', 'ZTHP在线消费系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (253, '消费产品辅件', 247, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (254, '充值机', 253, 3, '系列', '充值机', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (255, '消费机支架', 253, 3, '系列', '消费机支架', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (256, '通道产品', NULL, 1, '产品分类', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (257, '三辊闸', 256, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (258, 'TS系列', 257, 3, '系列', 'TS系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (259, 'TSM系列', 257, 3, '系列', 'TSM系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (260, '翼闸', 256, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (261, 'FBL系列', 260, 3, '系列', 'FBL系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (262, 'FBM系列', 260, 3, '系列', 'FBM系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (263, '摆闸', 256, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (264, 'SBTL系列', 263, 3, '系列', 'SBTL系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (265, 'SBTM系列', 263, 3, '系列', 'SBTM系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (266, 'Mars系列', 263, 3, '系列', 'Mars系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (267, 'SBTH系列', 263, 3, '系列', 'SBTH系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (268, '转闸', 256, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (269, 'FHT系列', 268, 3, '系列', 'FHT系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (270, '广告门', 256, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (271, 'ADR系列', 270, 3, '系列', 'ADR系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (272, '开门机', 256, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (273, 'ASD100系列', 272, 3, '系列', 'ASD100系列', '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (274, '自动伸缩门', 256, 2, '类别', NULL, '进行中');\n", "\nINSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) \nVALUES (275, '自动伸缩门系列', 274, 3, '系列', '自动伸缩门系列', '进行中');\n"], "document_type_insert_sql": ["\nINSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) \nVALUES (1, '彩页', '产品宣传彩页', '彩页', True, 5, '.pdf,.doc,.docx', 10485760, TRUE);\n", "\nINSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) \nVALUES (2, '入门指南', '产品入门指南', '入门指南', True, 4, '.pdf,.doc,.docx', 10485760, TRUE);\n", "\nINSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) \nVALUES (3, '用户手册', '产品用户手册', '用户手册', True, 3, '.pdf,.doc,.docx', 52428800, TRUE);\n", "\nINSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) \nVALUES (4, '技术文档', '技术规格文档', '技术文档', False, 2, '.pdf,.doc,.docx', 52428800, TRUE);\n", "\nINSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) \nVALUES (5, '宣传资料', '产品宣传资料', '宣传资料', False, 1, '.pdf,.doc,.docx,.ppt,.pptx', 52428800, TRUE);\n", "\nINSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) \nVALUES (6, '培训资料', '产品培训资料', '培训资料', False, 1, '.pdf,.doc,.docx,.ppt,.pptx', 104857600, TRUE);\n"], "data_quality_report": {"allcollections": {"total_records": 3140, "field_completeness": {"产品型号": {"total": 3140, "non_empty": 3140, "completeness_rate": 1.0}, "产品名称": {"total": 3140, "non_empty": 3140, "completeness_rate": 1.0}, "资料用途": {"total": 3140, "non_empty": 3140, "completeness_rate": 1.0}}, "model_distribution": {"NaN": 1607, "ZKtime5.0": 25, "xface600": 5, "PB3000": 5, "ZKecopro": 5, "ZKAccess3.5": 5, "ZKEpos": 5, "熵基云联": 5, "F30": 4, "nFace128": 4, "SC102": 4, "iFace3": 4, "xFace700": 4, "ZTHP40": 4, "ZTHP800": 4, "熵基互联": 4, "TSM3000": 4, "SBTM1000": 4, "ID860": 4, "iX25": 3}, "scope_distribution": {"门禁及梯控业务块": 989, "NaN": 207, "消费业务块": 150, "考勤业务块": 434, "车行业务块": 270, "熵基互联业务块": 36, "人行通道业务块": 236, "安检业务块": 96, "人证业务块": 269, "万傲瑞达业务块": 54, "ecopro软件": 21, "智能视频摄像机": 116, "测试": 2, "zkaccess3.5单机版门禁软件": 12, "ZKepos单机版消费软件": 26, "ZKtime5.0单机版考勤软件": 56, "云考勤": 86, "公共信息": 16, "熵基云联": 64}}, "product_structure": {"total_records": 581, "collection_status": {"已完成": 336, "NaN": 176, "已收集": 33, "提交": 15, "已提交": 21}, "category_distribution": {"考勤产品": 54, "门禁产品": 272, "软件": 8, "车行产品": 56, "智能锁产品": 33, "人证产品": 54, "NaN": 2, "安检产品": 32, "视频产品": 15, "消费产品": 21, "通道产品": 34}}}, "migration_plan": {"phase1": {"name": "基础数据迁移", "description": "迁移产品分类、文档类型等基础数据", "tasks": ["创建ProductCategory表数据", "创建DocumentType表数据", "建立分类层次关系"]}, "phase2": {"name": "产品数据迁移", "description": "迁移产品基本信息", "tasks": ["从allcollections.xlsx提取产品信息", "数据清洗和标准化", "创建Product表数据"]}, "phase3": {"name": "文档数据迁移", "description": "迁移产品文档信息", "tasks": ["从allcollections.xlsx提取文档信息", "建立产品与文档关联关系", "创建ProductDocument表数据"]}, "phase4": {"name": "数据验证和优化", "description": "验证数据完整性并优化", "tasks": ["数据完整性检查", "重复数据清理", "索引优化", "数据质量报告生成"]}}, "generated_at": "2025-06-16T21:05:08.341424"}