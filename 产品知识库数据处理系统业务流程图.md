# 产品知识库数据处理系统业务流程图

## 系统概述

本系统是一个自动化的产品知识库数据处理系统，通过批处理脚本实现一键执行，整合多个数据源（历史数据、云商API、本地文件）的产品信息和相关文档，为知识库应用提供完整的数据基础。

## 业务流程图

```mermaid
flowchart TD
    %% 系统初始化阶段
    A[开始执行 run_all.bat] --> B{检查Python环境}
    B -->|成功| C[安装依赖包]
    B -->|失败| B1[输出错误信息并退出]
    C --> D{检查必要文件}
    D -->|文件完整| E{用户确认配置}
    D -->|文件缺失| D1[输出缺失文件列表并退出]
    E -->|确认| F[创建必要目录]
    E -->|取消| E1[用户取消执行]
    F --> G[执行 product_data_processor.py]
    
    %% 数据库初始化阶段
    G --> H[初始化日志系统]
    H --> I{连接PostgreSQL数据库}
    I -->|连接成功| J[初始化数据库表结构]
    I -->|连接失败| I1[记录错误日志并重试]
    I1 --> I2{重试次数<3}
    I2 -->|是| I
    I2 -->|否| I3[数据库连接失败，退出]
    
    J --> K[创建产品分类表]
    J --> L[创建产品主表]
    J --> M[创建资料类型表]
    J --> N[创建产品资料表]
    J --> O[创建软件关联表]
    
    %% 历史数据处理阶段
    K --> P[导入历史数据]
    L --> P
    M --> P
    N --> P
    O --> P
    P --> Q[标记历史数据为作废状态]
    Q --> R[记录历史数据导入日志]
    
    %% 双数据源并行处理阶段
    R --> S[开始双数据源并行处理]
    
    %% 云商数据处理分支
    S --> T1[云商API数据处理]
    T1 --> U1{登录云商API}
    U1 -->|登录成功| V1[分页获取产品列表]
    U1 -->|登录失败| U2[记录登录失败日志]
    U2 --> U3{重试次数<3}
    U3 -->|是| U1
    U3 -->|否| U4[跳过云商数据处理]
    
    V1 --> W1[处理每个云商产品]
    W1 --> X1[解析产品基本信息]
    X1 --> Y1[下载产品附件]
    Y1 --> Z1{附件下载成功}
    Z1 -->|成功| AA1[提取文件元数据]
    Z1 -->|失败| Z2[记录下载失败日志]
    Z2 --> AA1
    AA1 --> BB1[计算文件哈希值]
    BB1 --> CC1[检查文件重复性]
    CC1 --> DD1[插入云商产品数据]
    DD1 --> EE1[标记数据源为yunshang]
    EE1 --> FF1[记录云商数据处理日志]
    
    %% 本地文件处理分支
    S --> T2[本地文件数据处理]
    T2 --> U5{加载产品结构映射}
    U5 -->|加载成功| V2[扫描本地产品目录]
    U5 -->|加载失败| U6[记录映射文件错误]
    U6 --> V3[跳过本地文件处理]
    
    V2 --> W2[递归遍历产品文件]
    W2 --> X2[识别文件类型]
    X2 --> Y2{文件格式支持}
    Y2 -->|支持| Z3[提取文件元数据]
    Y2 -->|不支持| Y3[记录不支持格式日志]
    Y3 --> W2
    Z3 --> AA2[计算文件哈希值]
    AA2 --> BB2[检查文件重复性]
    BB2 --> CC2[推断文档类型]
    CC2 --> DD2[插入本地文件数据]
    DD2 --> EE2[标记数据源为local]
    EE2 --> FF2[记录本地文件处理日志]
    
    %% 数据整合和质量保证阶段
    FF1 --> GG[数据整合和验证]
    FF2 --> GG
    V3 --> GG
    U4 --> GG
    
    GG --> HH[执行数据完整性检查]
    HH --> II{数据验证通过}
    II -->|通过| JJ[更新数据状态]
    II -->|失败| II1[记录验证失败日志]
    II1 --> II2[标记问题数据]
    II2 --> JJ
    
    JJ --> KK[生成处理统计报告]
    KK --> LL[记录性能指标]
    LL --> MM[断开数据库连接]
    MM --> NN[输出处理结果]
    NN --> OO[清理临时文件]
    OO --> PP[结束]
    
    %% 样式定义
    style A fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style S fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style T1 fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    style T2 fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style GG fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    style PP fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    
    %% 错误处理节点样式
    style B1 fill:#ffebee,stroke:#c62828,stroke-width:2px
    style D1 fill:#ffebee,stroke:#c62828,stroke-width:2px
    style I3 fill:#ffebee,stroke:#c62828,stroke-width:2px
    style U4 fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style V3 fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```

## 详细业务逻辑说明

### 1. 系统初始化阶段

#### 1.1 环境检查
- **Python环境验证**：检查Python是否正确安装，失败时输出错误信息并退出
- **依赖包安装**：自动安装requirements.txt中的依赖包
- **必要文件检查**：验证以下关键文件是否存在，缺失时列出详细信息并退出：
  - `allcollections.json`：历史知识库数据
  - `国内产品结构细化表.json`：产品结构映射文件
  - `product_data_processor.py`：主处理脚本
  - `config.py`：配置文件

#### 1.2 配置确认
- **用户交互确认**：要求用户确认已修改配置文件，提供取消执行选项
- **配置项检查**：
  - 数据库连接信息
  - 云商API账户信息
  - 文件路径配置

#### 1.3 目录创建
- **logs目录**：存储系统运行日志
- **downloads目录**：存储从云商API下载的附件

### 2. 数据库初始化阶段

#### 2.1 数据库连接
- 使用PostgreSQL数据库
- 支持连接重试机制（最多3次）
- 连接失败时记录详细错误日志
- 超过重试次数后程序退出

#### 2.2 表结构创建
创建5个核心数据表：

**ProductCategory（产品分类表）**
- 支持层级分类结构
- 包含排序和状态管理

**Product（产品主表）**
- 存储产品基本信息
- 支持多种产品属性
- 包含同步状态跟踪

**DocumentType（资料类型表）**
- 定义文档类型和要求
- 支持质量要求配置
- 包含审核流程设置

**ProductDocument（产品资料表）**
- 存储文档详细信息
- 支持版本管理
- 包含元数据和状态跟踪

**ProductSoftware（软件关联表）**
- 管理产品与软件的兼容性关系

### 3. 历史数据处理阶段

#### 3.1 数据导入
- **读取allcollections.json**：解析历史知识库数据
- **数据清洗**：处理数据格式和缺失值
- **批量插入**：使用事务确保数据一致性

#### 3.2 状态标记
- **作废标记**：将历史数据标记为`deprecated`状态
- **数据源标识**：标记为`legacy`数据源
- **保持可追溯性**：确保历史数据可查询但不影响新数据
- **导入日志记录**：记录历史数据导入的详细过程和结果

### 4. 双数据源并行处理阶段

#### 4.1 云商API数据处理分支

**API认证**
- **登录请求**：使用用户名密码获取访问Token
- **登录失败处理**：记录登录失败日志，支持重试机制（最多3次）
- **跳过机制**：登录失败超过重试次数时跳过云商数据处理

**数据获取**
- **分页查询**：批量获取产品列表（每页50条）
- **产品信息解析**：解析云商返回的产品基本信息
- **增量同步**：支持增量数据更新

**附件处理**
- **文件下载**：自动下载产品相关附件
- **下载失败处理**：记录附件下载失败的详细日志，但不中断处理流程
- **元数据提取**：提取文件大小、哈希值、MIME类型等元数据信息
- **重复性检查**：通过哈希值检查文件重复性
- **目录管理**：按产品ID创建专用目录

**数据入库**
- **数据插入**：将产品信息和文档信息插入到相应的数据库表中
- **数据源标记**：标记数据源为`yunshang`
- **处理日志**：记录云商数据处理的详细日志

#### 4.2 本地文件数据处理分支

**文件扫描**
- **映射文件加载**：加载产品结构映射文件（国内产品结构细化表.json）
- **加载失败处理**：记录映射文件错误，跳过本地文件处理
- **目录遍历**：递归扫描指定目录
- **文件类型识别**：识别Word、Excel、PDF、PPT等格式的文档文件
- **格式支持检查**：检查文件格式是否支持，不支持的格式记录日志但继续处理

**元数据提取**
- **文件哈希**：计算MD5/SHA256哈希值避免重复
- **重复性检查**：通过哈希值检查文件重复性
- **文件信息**：提取文件大小、创建时间等
- **MIME类型**：自动识别文件MIME类型

**关联建立**
- **文档类型推断**：根据文件名和路径推断文档类型（彩页、用户手册、技术规格等）
- **数据入库**：将本地文件信息同步到数据库中
- **数据源标记**：标记数据源为`local`
- **处理日志**：记录本地文件处理的详细日志

### 5. 数据整合和质量保证阶段

#### 5.1 数据汇聚和验证
- **数据汇聚**：汇聚云商和本地两个数据源的处理结果
- **完整性检查**：执行数据完整性和一致性验证
- **验证失败处理**：记录验证失败的详细日志，标记问题数据

#### 5.2 状态管理
- **状态更新**：更新所有数据的处理状态和时间戳
- **处理状态**：跟踪每个文件的处理状态
- **同步状态**：记录数据同步状态
- **错误状态**：记录处理失败的原因

#### 5.3 报告生成
- **统计报告生成**：生成详细的处理统计报告
- **性能指标记录**：记录处理时间、文件数量、成功率等性能指标

### 6. 系统清理和结束阶段

#### 6.1 资源清理
- **数据库断开**：安全断开数据库连接
- **临时文件清理**：清理处理过程中产生的临时文件

#### 6.2 结果输出
- **结果输出**：输出处理结果摘要和统计信息
- **程序结束**：正常结束程序执行

## 核心技术特点

### 1. 健壮的错误处理机制
- **多层次重试**：数据库连接、API登录等关键操作支持重试机制
- **优雅降级**：单个数据源失败时不影响其他数据源处理
- **详细错误日志**：记录错误详情和上下文信息
- **故障隔离**：错误不会导致整个系统崩溃

### 2. 双数据源并行处理
- **云商API集成**：自动获取最新产品数据和附件
- **本地文件扫描**：处理现有文档资源
- **并行执行**：两个数据源可以独立并行处理
- **统一数据模型**：不同数据源使用相同的数据结构

### 3. 智能数据质量保证
- **文件去重**：基于哈希值的智能去重机制
- **格式验证**：支持多种文档格式的验证
- **完整性检查**：确保数据的完整性和一致性
- **数据源标识**：清晰标记数据来源便于追溯

### 4. 增量更新和版本控制
- **智能同步**：只处理新增和变更的数据
- **历史数据保护**：标记历史数据为作废状态而非删除
- **状态跟踪**：跟踪每个文件和产品的处理状态
- **时间戳管理**：记录创建和更新时间

### 5. 全面的监控和日志系统
- **分级日志**：支持不同级别的日志记录
- **操作审计**：记录所有关键操作和决策点
- **性能监控**：跟踪处理时间、成功率等关键指标
- **统计报告**：生成详细的处理统计和性能报告

### 6. 高度可配置的架构
- **模块化设计**：各个处理模块相对独立
- **配置驱动**：通过配置文件控制系统行为
- **功能开关**：可选择性启用不同功能模块
- **环境适配**：支持不同环境的配置切换

### 7. 用户友好的交互体验
- **环境自检**：启动时自动检查运行环境
- **用户确认**：关键操作前提供用户确认机制
- **进度反馈**：提供清晰的处理进度和状态信息
- **结果展示**：输出详细的处理结果和统计信息

## 系统配置说明

### 数据库配置
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'user': 'postgres',
    'password': 'your_password',
    'dbname': 'product_knowledge_db',
    'port': 5432
}
```

### API配置
```python
API_CONFIG = {
    'base_url': 'https://zkmall.zktecoip.com',
    'username': '18929343717',
    'password': 'Zk@123456',
    'download_dir': './downloads'
}
```

### 文件路径配置
```python
FILE_PATHS = {
    'allcollections': './allcollections.json',
    'product_structure': './国内产品结构细化表.json',
    'local_files_base': 'E:/18.AI客服知识库/18.AI客服知识库'
}
```

## 执行结果

系统执行完成后将输出以下结果：
- **数据库表已创建**：完整的表结构和索引
- **历史数据已导入**：标记为作废的历史数据
- **云商数据已获取**：最新的产品和附件数据
- **本地文件已扫描**：本地文档资源整合
- **所有附件已下载**：完整的附件文件库

## 日志和文件位置

- **日志文件**：`./logs/product_processor.log`
- **下载文件**：`./downloads/`
- **配置文件**：`./config.py`
- **数据文件**：`./allcollections.json`、`./国内产品结构细化表.json`

## 总结

该产品知识库数据处理系统实现了从多个数据源到统一知识库的完整数据流转，通过自动化的批处理流程，确保了数据的完整性、一致性和可追溯性，为后续的AI客服知识库应用提供了坚实的数据基础。