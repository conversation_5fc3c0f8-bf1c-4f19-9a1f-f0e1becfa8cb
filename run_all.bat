@echo off
chcp 65001
echo ========================================
echo 产品知识库数据处理系统 - 一键执行脚本
echo ========================================
echo.

REM 设置当前目录为脚本所在目录
cd /d "%~dp0"

echo [1/6] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python
    pause
    exit /b 1
)
echo Python环境检查通过
echo.

echo [2/6] 安装依赖包...
echo 正在安装requirements.txt中的依赖包...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo 错误: 依赖包安装失败
    pause
    exit /b 1
)
echo 依赖包安装完成
echo.

echo [3/6] 检查必要文件...
if not exist "allcollections.json" (
    echo 错误: 未找到allcollections.json文件
    pause
    exit /b 1
)
if not exist "国内产品结构细化表.json" (
    echo 错误: 未找到国内产品结构细化表.json文件
    pause
    exit /b 1
)
if not exist "product_data_processor.py" (
    echo 错误: 未找到product_data_processor.py文件
    pause
    exit /b 1
)
if not exist "config.py" (
    echo 错误: 未找到config.py配置文件
    pause
    exit /b 1
)
echo 必要文件检查通过
echo.

echo [4/6] 检查配置文件...
echo 请确认您已经修改了config.py中的配置信息
echo 特别是数据库连接信息和API账户信息
echo.
echo 是否已修改配置文件? (Y/N)
set /p confirm=
if /i "%confirm%" neq "Y" (
    echo 请先修改config.py配置文件后再运行
    echo 详细说明请参考配置说明.md文件
    pause
    exit /b 1
)
echo 配置确认完成
echo.

echo [5/6] 创建必要目录...
if not exist "logs" mkdir logs
if not exist "downloads" mkdir downloads
echo 目录创建完成
echo.

echo [6/6] 执行数据处理...
echo 开始执行完整的数据处理流程...
echo 这可能需要几分钟时间，请耐心等待...
echo.
python product_data_processor.py
if errorlevel 1 (
    echo.
    echo 错误: 数据处理过程中出现错误
    echo 请检查logs目录下的日志信息并修复问题后重新运行
    pause
    exit /b 1
)

echo.
echo ========================================
echo 数据处理完成！
echo ========================================
echo 处理结果:
echo - 数据库表已创建
echo - 历史数据已导入（标记为作废）
echo - 云商数据已获取并导入
echo - 本地文件已扫描并导入
echo - 所有附件已下载并处理
echo ========================================
echo.
echo 日志文件位置: %~dp0logs\product_processor.log
echo 下载文件位置: %~dp0downloads
echo.
echo 按任意键退出...
pause >nul