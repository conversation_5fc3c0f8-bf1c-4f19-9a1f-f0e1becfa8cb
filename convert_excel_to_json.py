import pandas as pd
import json
import os

def convert_excel_to_json():
    """
    将Excel文件转换为JSON格式
    """
    # 文件路径
    file1 = 'D:/sync-db-fastgpt/allcollections(1).xlsx'
    file2 = 'D:/sync-db-fastgpt/国内产品结构细化表.xlsx'
    
    results = {}
    
    # 处理第一个文件
    if os.path.exists(file1):
        print(f"正在处理文件: {file1}")
        try:
            df1 = pd.read_excel(file1)
            print(f"文件1总行数: {len(df1)}")
            print(f"文件1列名: {list(df1.columns)}")
            
            # 转换为JSON格式，使用第一行作为字段名
            json_data1 = df1.to_dict('records')
            results['allcollections'] = {
                'columns': list(df1.columns),
                'total_rows': len(df1),
                'data': json_data1
            }
            
            # 保存为JSON文件
            with open('D:/sync-db-fastgpt/allcollections.json', 'w', encoding='utf-8') as f:
                json.dump(json_data1, f, ensure_ascii=False, indent=2)
            print("已保存 allcollections.json")
            
        except Exception as e:
            print(f"处理文件1时出错: {e}")
    else:
        print(f"文件不存在: {file1}")
    
    # 处理第二个文件
    if os.path.exists(file2):
        print(f"\n正在处理文件: {file2}")
        try:
            df2 = pd.read_excel(file2)
            print(f"文件2总行数: {len(df2)}")
            print(f"文件2列名: {list(df2.columns)}")
            
            # 转换为JSON格式，使用第一行作为字段名
            json_data2 = df2.to_dict('records')
            results['product_structure'] = {
                'columns': list(df2.columns),
                'total_rows': len(df2),
                'data': json_data2
            }
            
            # 保存为JSON文件
            with open('D:/sync-db-fastgpt/国内产品结构细化表.json', 'w', encoding='utf-8') as f:
                json.dump(json_data2, f, ensure_ascii=False, indent=2)
            print("已保存 国内产品结构细化表.json")
            
        except Exception as e:
            print(f"处理文件2时出错: {e}")
    else:
        print(f"文件不存在: {file2}")
    
    # 分析产品属性
    print("\n=== 产品属性分析 ===")
    
    if 'allcollections' in results:
        print("\nallcollections 文件字段分析:")
        columns = results['allcollections']['columns']
        for col in columns:
            print(f"  - {col}")
        
        # 分析产品相关字段
        product_fields = [col for col in columns if any(keyword in col.lower() for keyword in ['产品', 'product', 'cp', '型号', 'model', '名称', 'name'])]
        if product_fields:
            print(f"\n产品相关字段: {product_fields}")
    
    if 'product_structure' in results:
        print("\n国内产品结构细化表 文件字段分析:")
        columns = results['product_structure']['columns']
        for col in columns:
            print(f"  - {col}")
        
        # 分析产品分类字段
        category_fields = [col for col in columns if any(keyword in col for keyword in ['分类', '类别', '系列', '型号', '名称'])]
        if category_fields:
            print(f"\n产品分类相关字段: {category_fields}")
    
    # 保存完整分析结果
    with open('D:/sync-db-fastgpt/excel_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print("\n已保存完整分析结果到 excel_analysis_results.json")
    
    return results

if __name__ == "__main__":
    convert_excel_to_json()