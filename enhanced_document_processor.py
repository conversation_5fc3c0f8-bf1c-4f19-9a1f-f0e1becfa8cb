
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强文档处理器
实现智能文件格式优先级选择、PDF解析、多模态图片标注和增量内容补充功能
"""

import os
import sys
import json
import hashlib
import requests
import logging
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
from datetime import datetime
import base64
import re
from dataclasses import dataclass

# 导入现有模块
from config import *
from product_data_processor import ProductDataProcessor
from fastgpt_sync_manager import FastGPTSyncManager
from qwen_index_annotator import QwenIndexAnnotator
from document_replacement_manager import DocumentReplacementManager
from siliconflow_index_generator import SiliconFlowIndexGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/enhanced_processor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class DocumentInfo:
    """文档信息数据类"""
    file_path: str
    file_format: str  # markdown, word, pdf
    priority: int     # 优先级，数字越小优先级越高
    file_size: int
    file_hash: str
    last_modified: datetime
    product_id: str
    category: str
    content: Optional[str] = None
    images: Optional[List[Dict]] = None
    removed_images: Optional[List[Dict]] = None  # 被移除的无关图片
    fastgpt_id: Optional[str] = None

class EnhancedDocumentProcessor:
    """增强文档处理器"""
    
    # 文件格式优先级定义（数字越小优先级越高）
    FORMAT_PRIORITY = {
        'markdown': 1,
        'md': 1,
        'word': 2,
        'docx': 2,
        'doc': 2,
        'pdf': 3
    }
    
    # TextIn API配置
    TEXTIN_CONFIG = {
        'base_url': 'https://api.textin.com/ai/service/v1',
        'app_id': 'your_textin_app_id',  # 需要配置实际的TextIn应用ID
        'secret_code': 'your_textin_secret_code'  # 需要配置实际的密钥
    }
    
    # 多模态模型配置（示例使用OpenAI GPT-4V）
    MULTIMODAL_CONFIG = {
        'api_key': 'your_openai_api_key',  # 需要配置实际的API密钥
        'model': 'gpt-4-vision-preview',
        'base_url': 'https://api.openai.com/v1'
    }
    
    def __init__(self, db_config: Dict):
        """
        初始化增强文档处理器
        
        Args:
            db_config (Dict): 数据库配置
        """
        self.db_config = db_config
        self.data_processor = ProductDataProcessor()
        self.sync_manager = FastGPTSyncManager(db_config)
        
        # 新增：千问3索引标注器和文档替换管理器
        self.qwen_annotator = QwenIndexAnnotator()
        self.doc_replacement_manager = DocumentReplacementManager()
        self.siliconflow_generator = SiliconFlowIndexGenerator()
        
        # 处理统计
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'qwen_annotations': 0,
            'replaced_documents': 0,
            'processing_time': 0,
            'total_cost': 0.0
        }
        
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
        
        logger.info("增强文档处理器初始化完成（集成千问3标注和文档替换功能）")
    
    def get_file_hash(self, file_path: str) -> str:
        """
        计算文件哈希值
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: 文件MD5哈希值
        """
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return ""
    
    def get_file_format(self, file_path: str) -> str:
        """
        获取文件格式
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: 文件格式
        """
        ext = Path(file_path).suffix.lower().lstrip('.')
        
        # 格式映射
        format_mapping = {
            'md': 'markdown',
            'markdown': 'markdown',
            'docx': 'word',
            'doc': 'word',
            'pdf': 'pdf'
        }
        
        return format_mapping.get(ext, ext)
    
    def scan_product_documents(self, product_id: str, category: str, base_path: str) -> List[DocumentInfo]:
        """
        扫描产品文档，按格式优先级排序
        
        Args:
            product_id (str): 产品ID
            category (str): 文档类别
            base_path (str): 扫描基础路径
            
        Returns:
            List[DocumentInfo]: 排序后的文档信息列表
        """
        documents = []
        
        try:
            # 扫描指定路径下的文档
            for root, dirs, files in os.walk(base_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_format = self.get_file_format(file_path)
                    
                    # 只处理支持的格式
                    if file_format in self.FORMAT_PRIORITY:
                        file_stat = os.stat(file_path)
                        
                        doc_info = DocumentInfo(
                            file_path=file_path,
                            file_format=file_format,
                            priority=self.FORMAT_PRIORITY[file_format],
                            file_size=file_stat.st_size,
                            file_hash=self.get_file_hash(file_path),
                            last_modified=datetime.fromtimestamp(file_stat.st_mtime),
                            product_id=product_id,
                            category=category
                        )
                        
                        documents.append(doc_info)
            
            # 按优先级排序（优先级数字越小越优先）
            documents.sort(key=lambda x: (x.priority, x.last_modified), reverse=True)
            
            logger.info(f"扫描到 {len(documents)} 个文档，产品ID: {product_id}, 类别: {category}")
            
        except Exception as e:
            logger.error(f"扫描产品文档失败: {e}")
        
        return documents
    
    def select_optimal_document(self, documents: List[DocumentInfo]) -> Optional[DocumentInfo]:
        """
        根据优先级选择最优文档
        
        Args:
            documents (List[DocumentInfo]): 文档列表
            
        Returns:
            Optional[DocumentInfo]: 选中的最优文档
        """
        if not documents:
            return None
        
        # 按同一产品同一类别分组
        grouped_docs = {}
        for doc in documents:
            key = f"{doc.product_id}_{doc.category}"
            if key not in grouped_docs:
                grouped_docs[key] = []
            grouped_docs[key].append(doc)
        
        selected_docs = []
        
        # 为每个分组选择最优文档
        for group_key, group_docs in grouped_docs.items():
            # 按优先级排序
            group_docs.sort(key=lambda x: (x.priority, -x.last_modified.timestamp()))
            selected_doc = group_docs[0]
            
            logger.info(f"选择文档: {selected_doc.file_path} (格式: {selected_doc.file_format}, 优先级: {selected_doc.priority})")
            selected_docs.append(selected_doc)
        
        return selected_docs[0] if selected_docs else None
    
    def parse_pdf_with_textin(self, pdf_path: str) -> Dict[str, Any]:
        """
        使用TextIn API解析PDF文档
        
        Args:
            pdf_path (str): PDF文件路径
            
        Returns:
            Dict[str, Any]: 解析结果，包含markdown内容和图片信息
        """
        try:
            url = f"{self.TEXTIN_CONFIG['base_url']}/pdf_to_markdown"
            
            headers = {
                'x-ti-app-id': self.TEXTIN_CONFIG['app_id'],
                'x-ti-secret-code': self.TEXTIN_CONFIG['secret_code']
            }
            
            params = {
                'page_details': 1,
                'char_details': 1,
                'dpi': 144
            }
            
            with open(pdf_path, 'rb') as f:
                files = {'file': f}
                response = requests.post(url, headers=headers, params=params, files=files, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"PDF解析成功: {pdf_path}")
                return result
            else:
                logger.error(f"PDF解析失败: {response.status_code}, {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"PDF解析异常: {e}")
            return {}
    
    def extract_images_from_markdown(self, markdown_content: str, base_path: str) -> List[Dict[str, str]]:
        """
        从Markdown内容中提取图片信息
        
        Args:
            markdown_content (str): Markdown内容
            base_path (str): 基础路径
            
        Returns:
            List[Dict[str, str]]: 图片信息列表
        """
        images = []
        
        # 匹配Markdown图片语法
        img_pattern = r'!\[([^\]]*)\]\(([^\)]+)\)'
        matches = re.findall(img_pattern, markdown_content)
        
        for alt_text, img_path in matches:
            # 处理相对路径
            if not os.path.isabs(img_path):
                img_path = os.path.join(base_path, img_path)
            
            if os.path.exists(img_path):
                images.append({
                    'alt_text': alt_text,
                    'path': img_path,
                    'description': ''  # 待多模态模型标注
                })
        
        return images
    
    def check_image_relevance(self, image_path: str, context: str = "") -> Tuple[bool, str]:
        """
        检查图片与文档内容的相关性
        
        Args:
            image_path (str): 图片路径
            context (str): 文档上下文信息
            
        Returns:
            Tuple[bool, str]: (是否相关, 相关性分析结果)
        """
        try:
            # 读取图片并转换为base64
            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            # 构建请求
            headers = {
                'Authorization': f'Bearer {self.MULTIMODAL_CONFIG["api_key"]}',
                'Content-Type': 'application/json'
            }
            
            prompt = f"""
            请分析这张图片与以下文档内容的相关性：
            
            文档内容摘要：{context}
            
            请按以下格式回答：
            1. 相关性评分（0-10分，10分表示高度相关）：[评分]
            2. 相关性分析：[详细分析图片内容与文档的关联性]
            3. 是否保留：[是/否]
            
            评判标准：
            - 图片内容与文档主题直接相关（产品图片、技术图表、流程图等）：保留
            - 图片包含文档中提到的具体信息或概念：保留
            - 图片为装饰性图片、无关广告、个人照片等：不保留
            - 图片质量过低、无法识别内容：不保留
            
            请用中文回答。
            """
            
            data = {
                'model': self.MULTIMODAL_CONFIG['model'],
                'messages': [
                    {
                        'role': 'user',
                        'content': [
                            {'type': 'text', 'text': prompt},
                            {
                                'type': 'image_url',
                                'image_url': {
                                    'url': f'data:image/jpeg;base64,{image_data}'
                                }
                            }
                        ]
                    }
                ],
                'max_tokens': 300
            }
            
            response = requests.post(
                f"{self.MULTIMODAL_CONFIG['base_url']}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                analysis = result['choices'][0]['message']['content']
                
                # 解析相关性结果
                is_relevant = self._parse_relevance_result(analysis)
                
                logger.info(f"图片相关性检查完成: {image_path}, 相关性: {is_relevant}")
                return is_relevant, analysis
            else:
                logger.error(f"图片相关性检查失败: {response.status_code}, {response.text}")
                return True, "相关性检查失败，默认保留"  # 失败时默认保留
                
        except Exception as e:
            logger.error(f"图片相关性检查异常: {e}")
            return True, "相关性检查异常，默认保留"  # 异常时默认保留
    
    def _parse_relevance_result(self, analysis: str) -> bool:
        """
        解析相关性分析结果
        
        Args:
            analysis (str): AI分析结果
            
        Returns:
            bool: 是否相关
        """
        try:
            # 查找评分
            score_match = re.search(r'相关性评分.*?([0-9]+)', analysis)
            if score_match:
                score = int(score_match.group(1))
                if score >= 6:  # 评分6分以上认为相关
                    return True
            
            # 查找是否保留的明确答案
            if '是否保留：是' in analysis or '是否保留: 是' in analysis:
                return True
            elif '是否保留：否' in analysis or '是否保留: 否' in analysis:
                return False
            
            # 关键词判断
            relevant_keywords = ['相关', '保留', '有用', '重要', '必要']
            irrelevant_keywords = ['无关', '删除', '清理', '装饰', '广告']
            
            relevant_count = sum(1 for keyword in relevant_keywords if keyword in analysis)
            irrelevant_count = sum(1 for keyword in irrelevant_keywords if keyword in analysis)
            
            return relevant_count > irrelevant_count
            
        except Exception as e:
            logger.error(f"解析相关性结果失败: {e}")
            return True  # 解析失败时默认保留
    
    def annotate_image_with_multimodal(self, image_path: str, context: str = "") -> str:
        """
        使用多模态模型标注图片
        
        Args:
            image_path (str): 图片路径
            context (str): 上下文信息
            
        Returns:
            str: 图片描述
        """
        try:
            # 读取图片并转换为base64
            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            # 构建请求
            headers = {
                'Authorization': f'Bearer {self.MULTIMODAL_CONFIG["api_key"]}',
                'Content-Type': 'application/json'
            }
            
            prompt = f"""
            请详细描述这张图片的内容，包括：
            1. 图片中的主要对象和元素
            2. 图片的布局和结构
            3. 图片中的文字信息（如果有）
            4. 图片与以下上下文的关联性：{context}
            
            请用中文回答，描述要准确、详细且有助于理解文档内容。
            """
            
            data = {
                'model': self.MULTIMODAL_CONFIG['model'],
                'messages': [
                    {
                        'role': 'user',
                        'content': [
                            {'type': 'text', 'text': prompt},
                            {
                                'type': 'image_url',
                                'image_url': {
                                    'url': f'data:image/jpeg;base64,{image_data}'
                                }
                            }
                        ]
                    }
                ],
                'max_tokens': 500
            }
            
            response = requests.post(
                f"{self.MULTIMODAL_CONFIG['base_url']}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                description = result['choices'][0]['message']['content']
                logger.info(f"图片标注成功: {image_path}")
                return description
            else:
                logger.error(f"图片标注失败: {response.status_code}, {response.text}")
                return "图片标注失败"
                
        except Exception as e:
            logger.error(f"图片标注异常: {e}")
            return "图片标注异常"
    
    def process_document_content(self, doc_info: DocumentInfo) -> DocumentInfo:
        """
        处理文档内容，包括解析和图片标注
        
        Args:
            doc_info (DocumentInfo): 文档信息
            
        Returns:
            DocumentInfo: 处理后的文档信息
        """
        try:
            if doc_info.file_format == 'markdown':
                # 直接读取Markdown文件
                with open(doc_info.file_path, 'r', encoding='utf-8') as f:
                    doc_info.content = f.read()
                
                # 提取图片
                doc_info.images = self.extract_images_from_markdown(
                    doc_info.content, 
                    os.path.dirname(doc_info.file_path)
                )
                
            elif doc_info.file_format == 'word':
                # 这里可以集成Word文档解析逻辑
                # 暂时使用占位符
                doc_info.content = f"Word文档内容: {doc_info.file_path}"
                doc_info.images = []
                
            elif doc_info.file_format == 'pdf':
                # 使用TextIn API解析PDF
                parse_result = self.parse_pdf_with_textin(doc_info.file_path)
                
                if parse_result and 'markdown' in parse_result:
                    doc_info.content = parse_result['markdown']
                    
                    # 提取图片信息
                    doc_info.images = self.extract_images_from_markdown(
                        doc_info.content,
                        os.path.dirname(doc_info.file_path)
                    )
                else:
                    logger.warning(f"PDF解析失败，使用占位符内容: {doc_info.file_path}")
                    doc_info.content = f"PDF文档解析失败: {doc_info.file_path}"
                    doc_info.images = []
            
            # 为图片添加多模态标注和相关性检测
            if doc_info.images:
                relevant_images = []
                removed_images = []
                
                for img in doc_info.images:
                    # 检查图片相关性
                    is_relevant, relevance_analysis = self.check_image_relevance(
                        img['path'], 
                        doc_info.content[:800]  # 使用前800字符作为上下文
                    )
                    
                    if is_relevant:
                        # 相关图片：添加标注
                        img['description'] = self.annotate_image_with_multimodal(
                            img['path'], 
                            doc_info.content[:500]  # 使用前500字符作为上下文
                        )
                        img['relevance_analysis'] = relevance_analysis
                        relevant_images.append(img)
                        
                        # 更新Markdown内容，添加图片描述
                        old_img_md = f"![{img['alt_text']}]({img['path']})"
                        new_img_md = f"![{img['alt_text']}]({img['path']})\n\n**图片描述**: {img['description']}\n"
                        doc_info.content = doc_info.content.replace(old_img_md, new_img_md)
                        
                        logger.info(f"保留相关图片: {img['path']}")
                    else:
                        # 无关图片：从内容中移除
                        img_md_patterns = [
                            f"![{img['alt_text']}]({img['path']})",
                            f"![{img['alt_text']}]({os.path.basename(img['path'])})",
                            f"![]({img['path']})",
                            f"![]({os.path.basename(img['path'])})"
                        ]
                        
                        for pattern in img_md_patterns:
                            doc_info.content = doc_info.content.replace(pattern, "")
                        
                        img['relevance_analysis'] = relevance_analysis
                        removed_images.append(img)
                        
                        logger.info(f"移除无关图片: {img['path']}")
                
                # 更新图片列表为相关图片
                doc_info.images = relevant_images
                doc_info.removed_images = removed_images
                
                # 记录处理统计
                logger.info(f"图片处理完成 - 保留: {len(relevant_images)}, 移除: {len(removed_images)}")
                
                # 在文档末尾添加图片处理说明
                if removed_images:
                    processing_note = f"\n\n---\n**图片处理说明**: 本文档共处理 {len(relevant_images) + len(removed_images)} 张图片，保留 {len(relevant_images)} 张相关图片，移除 {len(removed_images)} 张无关图片。\n"
                    doc_info.content += processing_note
            
            logger.info(f"文档内容处理完成: {doc_info.file_path}")
            
        except Exception as e:
            logger.error(f"文档内容处理失败: {e}")
            doc_info.content = f"文档处理失败: {str(e)}"
            doc_info.images = []
        
        return doc_info
    
    def check_fastgpt_existing_content(self, product_id: str, category: str) -> Optional[Dict]:
        """
        检查FastGPT中是否已存在相关内容
        
        Args:
            product_id (str): 产品ID
            category (str): 文档类别
            
        Returns:
            Optional[Dict]: 现有内容信息
        """
        try:
            # 查询数据库中的同步记录
            query = """
            SELECT fastgpt_id, content_hash, last_sync_time 
            FROM pending_sync_data 
            WHERE source_id = %s AND metadata->>'category' = %s 
            AND sync_status = 'synced'
            ORDER BY last_sync_time DESC 
            LIMIT 1
            """
            
            result = self.sync_manager.execute_query(query, (product_id, category))
            
            if result:
                return {
                    'fastgpt_id': result[0][0],
                    'content_hash': result[0][1],
                    'last_sync_time': result[0][2]
                }
            
            return None
            
        except Exception as e:
            logger.error(f"检查FastGPT现有内容失败: {e}")
            return None
    
    def create_incremental_content(self, new_content: str, existing_content_hash: str) -> str:
        """
        创建增量内容
        
        Args:
            new_content (str): 新内容
            existing_content_hash (str): 现有内容哈希
            
        Returns:
            str: 增量内容
        """
        try:
            new_hash = hashlib.md5(new_content.encode('utf-8')).hexdigest()
            
            if new_hash == existing_content_hash:
                logger.info("内容无变化，无需增量更新")
                return ""
            
            # 这里可以实现更复杂的增量内容生成逻辑
            # 暂时返回完整的新内容
            incremental_content = f"""
# 内容更新 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{new_content}

---
*本次更新包含最新的文档内容和图片标注*
            """
            
            logger.info("生成增量内容成功")
            return incremental_content
            
        except Exception as e:
            logger.error(f"创建增量内容失败: {e}")
            return new_content
    
    def process_product_documents(self, product_id: str, category: str, base_path: str) -> bool:
        """
        处理产品文档的完整流程
        
        Args:
            product_id (str): 产品ID
            category (str): 文档类别
            base_path (str): 扫描基础路径
            
        Returns:
            bool: 处理是否成功
        """
        try:
            logger.info(f"开始处理产品文档: {product_id}, 类别: {category}")
            
            # 1. 扫描文档
            documents = self.scan_product_documents(product_id, category, base_path)
            if not documents:
                logger.warning(f"未找到相关文档: {product_id}, {category}")
                return False
            
            # 2. 选择最优文档
            selected_doc = self.select_optimal_document(documents)
            if not selected_doc:
                logger.warning(f"未选择到合适文档: {product_id}, {category}")
                return False
            
            # 3. 处理文档内容
            processed_doc = self.process_document_content(selected_doc)
            
            # 4. 检查FastGPT中的现有内容
            existing_content = self.check_fastgpt_existing_content(product_id, category)
            
            # 5. 生成最终内容
            final_content = processed_doc.content
            change_type = 'create'
            
            if existing_content:
                # 生成增量内容
                incremental_content = self.create_incremental_content(
                    processed_doc.content, 
                    existing_content['content_hash']
                )
                
                if incremental_content:
                    final_content = incremental_content
                    change_type = 'update'
                    processed_doc.fastgpt_id = existing_content['fastgpt_id']
                else:
                    logger.info(f"内容无变化，跳过处理: {product_id}, {category}")
                    return True
            
            # 6. 创建待同步记录
            sync_data = {
                'source_type': 'enhanced_processor',
                'source_id': product_id,
                'change_type': change_type,
                'title': f"{product_id} - {category}",
                'content': final_content,
                'metadata': {
                    'category': category,
                    'file_path': processed_doc.file_path,
                    'file_format': processed_doc.file_format,
                    'file_hash': processed_doc.file_hash,
                    'images': processed_doc.images,
                    'removed_images': processed_doc.removed_images or [],
                    'priority': processed_doc.priority,
                    'image_processing_stats': {
                        'total_images': len((processed_doc.images or []) + (processed_doc.removed_images or [])),
                        'relevant_images': len(processed_doc.images or []),
                        'removed_images': len(processed_doc.removed_images or [])
                    }
                },
                'fastgpt_id': processed_doc.fastgpt_id
            }
            
            # 插入待同步数据
            success = self.sync_manager.create_pending_sync_data([sync_data])
            
            if success:
                logger.info(f"文档处理完成并创建待同步记录: {product_id}, {category}")
                return True
            else:
                logger.error(f"创建待同步记录失败: {product_id}, {category}")
                return False
                
        except Exception as e:
            logger.error(f"处理产品文档失败: {e}")
            return False
    
    def batch_process_products(self, products_config: List[Dict]) -> Dict[str, Any]:
        """
        批量处理产品文档
        
        Args:
            products_config (List[Dict]): 产品配置列表
                格式: [{'product_id': 'xxx', 'category': 'xxx', 'base_path': 'xxx'}, ...]
                
        Returns:
            Dict[str, Any]: 处理结果统计
        """
        results = {
            'total': len(products_config),
            'success': 0,
            'failed': 0,
            'details': []
        }
        
        for config in products_config:
            try:
                success = self.process_product_documents(
                    config['product_id'],
                    config['category'],
                    config['base_path']
                )
                
                if success:
                    results['success'] += 1
                    results['details'].append({
                        'product_id': config['product_id'],
                        'category': config['category'],
                        'status': 'success'
                    })
                else:
                    results['failed'] += 1
                    results['details'].append({
                        'product_id': config['product_id'],
                        'category': config['category'],
                        'status': 'failed'
                    })
                    
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'product_id': config.get('product_id', 'unknown'),
                    'category': config.get('category', 'unknown'),
                    'status': 'error',
                    'error': str(e)
                })
                logger.error(f"批量处理失败: {e}")
        
        logger.info(f"批量处理完成: 总计 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}")
        return results
    
    async def process_documents_enhanced(self, file_paths: List[str], business_blocks: List[str]) -> Dict[str, Any]:
        """增强文档处理（集成千问3标注和文档替换）"""
        import time
        import asyncio
        
        start_time = time.time()
        results = {
            'processed_files': [],
            'failed_files': [],
            'generated_indexes': [],
            'qwen_annotations': [],
            'replaced_documents': [],
            'performance_metrics': {},
            'total_cost': 0.0
        }
        
        try:
            # 第一步：检查并替换作废文档
            logger.info("开始检查作废文档...")
            replacement_results = await self.doc_replacement_manager.check_and_replace_documents(file_paths)
            
            # 更新文件列表（使用替换后的文档）
            updated_file_paths = []
            for file_path in file_paths:
                replacement = next((r for r in replacement_results if r.original_path == file_path), None)
                if replacement and replacement.replacement_found:
                    updated_file_paths.append(replacement.replacement_path)
                    results['replaced_documents'].append({
                        'original': file_path,
                        'replacement': replacement.replacement_path,
                        'confidence': replacement.confidence,
                        'reason': replacement.replacement_reason
                    })
                    self.stats['replaced_documents'] += 1
                else:
                    updated_file_paths.append(file_path)
            
            # 更新统计
            self.stats['total_files'] = len(updated_file_paths)
            
            # 第二步：并发处理文档（集成千问3标注）
            tasks = []
            for file_path in updated_file_paths:
                task = self._process_single_document_with_qwen(file_path, business_blocks)
                tasks.append(task)
            
            # 执行并发处理
            document_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for i, result in enumerate(document_results):
                if isinstance(result, Exception):
                    results['failed_files'].append({
                        'file': updated_file_paths[i],
                        'error': str(result)
                    })
                    self.stats['failed_files'] += 1
                else:
                    results['processed_files'].append(result)
                    if result.get('indexes'):
                        results['generated_indexes'].extend(result['indexes'])
                    if result.get('qwen_annotation'):
                        results['qwen_annotations'].append(result['qwen_annotation'])
                        self.stats['qwen_annotations'] += 1
                    results['total_cost'] += result.get('cost', 0)
                    self.stats['processed_files'] += 1
            
            # 性能监控
            processing_time = time.time() - start_time
            self.stats['processing_time'] = processing_time
            self.stats['total_cost'] = results['total_cost']
            
            results['performance_metrics'] = {
                'total_processing_time': processing_time,
                'average_time_per_file': processing_time / len(updated_file_paths) if updated_file_paths else 0,
                'files_per_second': len(updated_file_paths) / processing_time if processing_time > 0 else 0,
                'total_cost': results['total_cost'],
                'qwen_annotations_count': self.stats['qwen_annotations'],
                'replaced_documents_count': self.stats['replaced_documents']
            }
            
            logger.info(f"增强文档处理完成: {self.stats['processed_files']}/{self.stats['total_files']} 成功")
            logger.info(f"千问3标注: {self.stats['qwen_annotations']} 个")
            logger.info(f"文档替换: {self.stats['replaced_documents']} 个")
            
        except Exception as e:
            logger.error(f"增强文档处理过程中发生错误: {str(e)}")
            raise
        
        return results
    
    async def _process_single_document_with_qwen(self, file_path: str, business_blocks: List[str]) -> Dict[str, Any]:
        """处理单个文档（集成千问3标注）"""
        try:
            logger.info(f"开始处理文档: {file_path}")
            
            # 第一步：使用硅基流动生成器处理文档
            async with self.siliconflow_generator as generator:
                base_result = await generator._process_single_document(file_path, business_blocks)
            
            # 第二步：使用千问3进行索引标注
            if base_result.get('content'):
                qwen_annotation = await self.qwen_annotator.annotate_document(
                    content=base_result['content'],
                    title=base_result.get('title', Path(file_path).stem),
                    business_blocks=business_blocks
                )
                
                # 将千问3标注结果集成到基础结果中
                if qwen_annotation:
                    base_result['qwen_annotation'] = qwen_annotation
                    
                    # 增强索引数据
                    if base_result.get('indexes'):
                        for index in base_result['indexes']:
                            # 添加千问3生成的关键词
                            if qwen_annotation.get('keywords'):
                                index['metadata']['qwen_keywords'] = qwen_annotation['keywords']
                            
                            # 添加千问3生成的摘要
                            if qwen_annotation.get('summary'):
                                index['metadata']['qwen_summary'] = qwen_annotation['summary']
                            
                            # 添加千问3生成的问答对
                            if qwen_annotation.get('qa_pairs'):
                                index['metadata']['qwen_qa_pairs'] = qwen_annotation['qa_pairs']
                            
                            # 添加相关性分析
                            if qwen_annotation.get('relevance_analysis'):
                                index['metadata']['qwen_relevance'] = qwen_annotation['relevance_analysis']
            
            logger.info(f"文档处理完成: {file_path}")
            return base_result
            
        except Exception as e:
            logger.error(f"处理文档失败 {file_path}: {str(e)}")
            raise
    
    async def sync_to_fastgpt_enhanced(self, results: Dict[str, Any], 
                                     knowledge_base_id: str,
                                     exclude_original_data: bool = True) -> Dict[str, Any]:
        """增强版FastGPT同步（支持知识库指定和原始数据排除）"""
        try:
            logger.info(f"开始同步到FastGPT知识库: {knowledge_base_id}")
            
            sync_results = {
                'total_items': 0,
                'synced_items': 0,
                'failed_items': 0,
                'excluded_original_data': exclude_original_data,
                'knowledge_base_id': knowledge_base_id,
                'sync_details': []
            }
            
            # 准备同步数据
            sync_data = []
            
            # 处理生成的索引
            for index in results.get('generated_indexes', []):
                sync_item = {
                    'type': 'knowledge_content',
                    'title': index.get('title', ''),
                    'content': index.get('content', ''),
                    'metadata': index.get('metadata', {}),
                    'knowledge_base_id': knowledge_base_id
                }
                
                # 如果排除原始数据，则不包含原始文档内容
                if exclude_original_data:
                    # 移除原始数据字段
                    sync_item['metadata'].pop('original_content', None)
                    sync_item['metadata'].pop('raw_data', None)
                    sync_item['metadata']['data_type'] = 'processed_knowledge_only'
                
                sync_data.append(sync_item)
            
            # 处理千问3标注
            for annotation in results.get('qwen_annotations', []):
                sync_item = {
                    'type': 'qwen_annotation',
                    'title': f"千问3标注: {annotation.get('title', '')}",
                    'content': annotation.get('summary', ''),
                    'metadata': {
                        'qwen_keywords': annotation.get('keywords', []),
                        'qwen_qa_pairs': annotation.get('qa_pairs', []),
                        'qwen_relevance': annotation.get('relevance_analysis', {}),
                        'annotation_type': 'qwen3_enhanced',
                        'knowledge_base_id': knowledge_base_id
                    }
                }
                sync_data.append(sync_item)
            
            sync_results['total_items'] = len(sync_data)
            
            # 使用现有的同步管理器进行同步
            for item in sync_data:
                try:
                    success = await self.sync_manager.sync_single_item(item)
                    if success:
                        sync_results['synced_items'] += 1
                    else:
                        sync_results['failed_items'] += 1
                except Exception as e:
                    logger.error(f"同步单项失败: {str(e)}")
                    sync_results['failed_items'] += 1
            
            logger.info(f"FastGPT同步完成: {sync_results['synced_items']}/{sync_results['total_items']} 成功")
            
            if exclude_original_data:
                logger.info("✅ 已按要求排除原始数据，仅同步知识内容")
            
            return sync_results
            
        except Exception as e:
            logger.error(f"FastGPT同步过程中发生错误: {str(e)}")
            raise
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            'stats': self.stats.copy(),
            'qwen_annotator_stats': self.qwen_annotator.get_stats(),
            'doc_replacement_stats': self.doc_replacement_manager.get_stats()
        }

def main():
    """主函数 - 示例用法"""
    # 数据库配置
    db_config = DATABASE_CONFIG
    
    # 创建处理器实例
    processor = EnhancedDocumentProcessor(db_config)
    
    # 示例：处理单个产品文档
    success = processor.process_product_documents(
        product_id="PRODUCT_001",
        category="用户手册",
        base_path="/path/to/product/documents"
    )
    
    if success:
        print("文档处理成功")
    else:
        print("文档处理失败")
    
    # 示例：批量处理
    products_config = [
        {
            'product_id': 'PRODUCT_001',
            'category': '用户手册',
            'base_path': '/path/to/product001/manuals'
        },
        {
            'product_id': 'PRODUCT_002',
            'category': '技术规格',
            'base_path': '/path/to/product002/specs'
        }
    ]
    
    results = processor.batch_process_products(products_config)
    print(f"批量处理结果: {json.dumps(results, indent=2, ensure_ascii=False)}")

if __name__ == "__main__":
    main()