# -*- coding: utf-8 -*-
"""
千问3索引标注器
功能：
1. 集成阿里云千问3 API
2. 智能索引标注和关键词提取
3. 生成高质量的文档摘要
4. 创建问答对和相关性分析
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
import aiohttp
import hashlib
from datetime import datetime
import os
from dataclasses import dataclass

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class IndexAnnotation:
    """索引标注数据类"""
    title: str
    summary: str
    keywords: List[str]
    tags: List[str]
    category: str
    business_value: str
    target_audience: str
    difficulty_level: str
    related_products: List[str]
    qa_pairs: List[Dict[str, str]]
    confidence_score: float
    processing_time: float
    model_version: str

class QwenIndexAnnotator:
    """千问3索引标注器"""
    
    def __init__(self, api_key: str = None):
        """
        初始化千问3索引标注器
        
        Args:
            api_key: 阿里云API密钥
        """
        self.api_key = api_key or os.getenv('DASHSCOPE_API_KEY')
        if not self.api_key:
            raise ValueError("请设置DASHSCOPE_API_KEY环境变量或提供api_key参数")
        
        # API配置
        self.api_base = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        self.model = "qwen-max"  # 千问3最新模型
        
        # 请求配置
        self.max_retries = 3
        self.timeout = 60
        self.rate_limit_delay = 1  # 秒
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_tokens_used': 0,
            'total_cost': 0.0
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if hasattr(self, 'session'):
            await self.session.close()
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'X-DashScope-SSE': 'disable'
        }
    
    async def _call_qwen_api(self, prompt: str, max_tokens: int = 2000) -> Dict[str, Any]:
        """调用千问API"""
        payload = {
            "model": self.model,
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            },
            "parameters": {
                "max_tokens": max_tokens,
                "temperature": 0.1,  # 较低温度确保结果稳定
                "top_p": 0.9,
                "repetition_penalty": 1.1
            }
        }
        
        self.stats['total_requests'] += 1
        
        for attempt in range(self.max_retries):
            try:
                async with self.session.post(
                    self.api_base,
                    headers=self._get_headers(),
                    json=payload
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        self.stats['successful_requests'] += 1
                        
                        # 更新使用统计
                        if 'usage' in result:
                            usage = result['usage']
                            self.stats['total_tokens_used'] += usage.get('total_tokens', 0)
                            # 估算成本（需要根据实际价格调整）
                            self.stats['total_cost'] += usage.get('total_tokens', 0) * 0.002 / 1000
                        
                        return result
                    
                    elif response.status == 429:  # 速率限制
                        wait_time = (attempt + 1) * 2
                        logger.warning(f"API速率限制，等待{wait_time}秒后重试...")
                        await asyncio.sleep(wait_time)
                        continue
                    
                    else:
                        error_text = await response.text()
                        logger.error(f"API调用失败: {response.status} - {error_text}")
                        
            except Exception as e:
                logger.error(f"API调用异常 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.rate_limit_delay * (attempt + 1))
        
        self.stats['failed_requests'] += 1
        raise Exception(f"千问API调用失败，已重试{self.max_retries}次")
    
    def _parse_index_response(self, response: Dict[str, Any]) -> Optional[IndexAnnotation]:
        """解析API响应"""
        try:
            if 'output' not in response or 'text' not in response['output']:
                logger.error("API响应格式错误")
                return None
            
            content = response['output']['text'].strip()
            
            # 尝试解析JSON
            if content.startswith('```json'):
                content = content[7:-3].strip()
            elif content.startswith('```'):
                content = content[3:-3].strip()
            
            try:
                data = json.loads(content)
            except json.JSONDecodeError:
                # 如果不是JSON格式，尝试提取关键信息
                logger.warning("响应不是标准JSON格式，尝试解析文本")
                return self._parse_text_response(content)
            
            # 验证必需字段
            required_fields = ['title', 'summary', 'keywords', 'tags']
            for field in required_fields:
                if field not in data:
                    logger.warning(f"缺少必需字段: {field}")
                    data[field] = self._get_default_value(field)
            
            # 计算置信度分数
            confidence_score = self._calculate_confidence_score(data)
            
            return IndexAnnotation(
                title=data.get('title', ''),
                summary=data.get('summary', ''),
                keywords=data.get('keywords', []),
                tags=data.get('tags', []),
                category=data.get('category', ''),
                business_value=data.get('business_value', ''),
                target_audience=data.get('target_audience', ''),
                difficulty_level=data.get('difficulty_level', '中等'),
                related_products=data.get('related_products', []),
                qa_pairs=data.get('qa_pairs', []),
                confidence_score=confidence_score,
                processing_time=response.get('usage', {}).get('total_time', 0),
                model_version=self.model
            )
            
        except Exception as e:
            logger.error(f"解析索引响应失败: {str(e)}")
            return None
    
    def _parse_text_response(self, content: str) -> IndexAnnotation:
        """解析文本格式的响应"""
        # 简单的文本解析逻辑
        lines = content.split('\n')
        
        title = ''
        summary = ''
        keywords = []
        tags = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('标题:') or line.startswith('Title:'):
                title = line.split(':', 1)[1].strip()
            elif line.startswith('摘要:') or line.startswith('Summary:'):
                summary = line.split(':', 1)[1].strip()
            elif line.startswith('关键词:') or line.startswith('Keywords:'):
                keywords_str = line.split(':', 1)[1].strip()
                keywords = [kw.strip() for kw in keywords_str.split(',')]
            elif line.startswith('标签:') or line.startswith('Tags:'):
                tags_str = line.split(':', 1)[1].strip()
                tags = [tag.strip() for tag in tags_str.split(',')]
        
        return IndexAnnotation(
            title=title or '未知标题',
            summary=summary or '暂无摘要',
            keywords=keywords,
            tags=tags,
            category='',
            business_value='',
            target_audience='',
            difficulty_level='中等',
            related_products=[],
            qa_pairs=[],
            confidence_score=0.5,  # 文本解析的置信度较低
            processing_time=0,
            model_version=self.model
        )
    
    def _get_default_value(self, field: str) -> Any:
        """获取字段的默认值"""
        defaults = {
            'title': '未知标题',
            'summary': '暂无摘要',
            'keywords': [],
            'tags': [],
            'category': '其他',
            'business_value': '待评估',
            'target_audience': '通用用户',
            'difficulty_level': '中等',
            'related_products': [],
            'qa_pairs': []
        }
        return defaults.get(field, '')
    
    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """计算置信度分数"""
        score = 0.0
        
        # 标题质量 (20%)
        if data.get('title') and len(data['title']) > 5:
            score += 0.2
        
        # 摘要质量 (25%)
        summary = data.get('summary', '')
        if summary and 50 <= len(summary) <= 500:
            score += 0.25
        
        # 关键词数量 (20%)
        keywords = data.get('keywords', [])
        if 3 <= len(keywords) <= 10:
            score += 0.2
        
        # 标签数量 (15%)
        tags = data.get('tags', [])
        if 2 <= len(tags) <= 8:
            score += 0.15
        
        # 问答对质量 (20%)
        qa_pairs = data.get('qa_pairs', [])
        if qa_pairs and len(qa_pairs) >= 1:
            score += 0.2
        
        return min(score, 1.0)
    
    async def generate_smart_index(self, content: str, product_info: Dict[str, Any]) -> Optional[IndexAnnotation]:
        """生成智能索引"""
        start_time = datetime.now()
        
        # 构建优化的提示词
        prompt = self._build_index_prompt(content, product_info)
        
        try:
            # 调用千问API
            response = await self._call_qwen_api(prompt)
            
            # 解析响应
            annotation = self._parse_index_response(response)
            
            if annotation:
                processing_time = (datetime.now() - start_time).total_seconds()
                annotation.processing_time = processing_time
                
                logger.info(f"索引标注完成，置信度: {annotation.confidence_score:.2f}")
                return annotation
            else:
                logger.error("索引标注解析失败")
                return None
                
        except Exception as e:
            logger.error(f"生成智能索引失败: {str(e)}")
            return None
    
    def _build_index_prompt(self, content: str, product_info: Dict[str, Any]) -> str:
        """构建索引标注提示词"""
        # 限制内容长度
        max_content_length = 3000
        if len(content) > max_content_length:
            content = content[:max_content_length] + "..."
        
        prompt = f"""
作为专业的知识管理和信息架构专家，请为以下产品文档生成高质量的索引标注。

## 产品信息
- 产品名称：{product_info.get('name', '未知产品')}
- 产品类别：{product_info.get('category', '未知类别')}
- 业务块：{product_info.get('business_block', '未知业务块')}
- 文档类型：{product_info.get('doc_type', '技术文档')}

## 文档内容
{content}

## 任务要求
请严格按照以下JSON格式生成索引标注，确保JSON格式正确且完整：

```json
{{
    "title": "简洁明确的文档标题（10-50字）",
    "summary": "精准的文档摘要，突出核心价值和关键信息（100-300字）",
    "keywords": ["关键词1", "关键词2", "关键词3", "关键词4", "关键词5"],
    "tags": ["标签1", "标签2", "标签3"],
    "category": "文档类别（如：产品手册/技术文档/安装指南/故障排除等）",
    "business_value": "业务价值描述，说明此文档对用户的实际帮助",
    "target_audience": "目标用户群体（如：技术人员/销售人员/最终用户等）",
    "difficulty_level": "简单/中等/复杂",
    "related_products": ["相关产品1", "相关产品2"],
    "qa_pairs": [
        {{
            "question": "基于文档内容的常见问题1",
            "answer": "详细准确的答案1"
        }},
        {{
            "question": "基于文档内容的常见问题2",
            "answer": "详细准确的答案2"
        }}
    ]
}}
```

## 质量标准
1. **准确性**：所有信息必须基于文档内容，不得编造
2. **实用性**：关键词和标签要有助于检索和分类
3. **完整性**：摘要要涵盖文档的主要内容点
4. **专业性**：使用行业标准术语和表达
5. **用户导向**：问答对要解决实际使用中的问题

请确保返回的是标准的JSON格式，不要包含任何额外的文字说明。
"""
        
        return prompt
    
    async def batch_generate_indexes(self, documents: List[Dict[str, Any]]) -> List[Tuple[str, Optional[IndexAnnotation]]]:
        """批量生成索引"""
        results = []
        
        for i, doc in enumerate(documents):
            try:
                logger.info(f"处理文档 {i+1}/{len(documents)}: {doc.get('name', 'Unknown')}")
                
                annotation = await self.generate_smart_index(
                    doc['content'], 
                    doc.get('product_info', {})
                )
                
                results.append((doc.get('id', f'doc_{i}'), annotation))
                
                # 添加延迟避免API限制
                if i < len(documents) - 1:
                    await asyncio.sleep(self.rate_limit_delay)
                    
            except Exception as e:
                logger.error(f"处理文档失败: {str(e)}")
                results.append((doc.get('id', f'doc_{i}'), None))
        
        return results
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        success_rate = 0
        if self.stats['total_requests'] > 0:
            success_rate = (self.stats['successful_requests'] / self.stats['total_requests']) * 100
        
        return {
            'total_requests': self.stats['total_requests'],
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'success_rate': round(success_rate, 2),
            'total_tokens_used': self.stats['total_tokens_used'],
            'estimated_cost': round(self.stats['total_cost'], 4),
            'model_version': self.model
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_tokens_used': 0,
            'total_cost': 0.0
        }

# 使用示例
async def main():
    """使用示例"""
    # 配置API密钥
    api_key = "your_dashscope_api_key_here"
    
    # 示例文档
    sample_doc = {
        'id': 'doc_001',
        'name': '门禁控制器安装指南',
        'content': """
        # 门禁控制器安装指南
        
        ## 产品概述
        本产品是一款高性能的门禁控制器，支持多种读卡方式，适用于办公楼、住宅小区等场所的门禁管理。
        
        ## 安装步骤
        1. 确认安装位置
        2. 连接电源线
        3. 连接网络线
        4. 安装读卡器
        5. 系统调试
        
        ## 技术参数
        - 工作电压：DC 12V
        - 工作温度：-20°C ~ +60°C
        - 通讯方式：TCP/IP、RS485
        - 存储容量：10万张卡片
        """,
        'product_info': {
            'name': '智能门禁控制器 AC-2000',
            'category': '门禁设备',
            'business_block': '门禁系统',
            'doc_type': '安装指南'
        }
    }
    
    async with QwenIndexAnnotator(api_key) as annotator:
        # 生成单个索引
        annotation = await annotator.generate_smart_index(
            sample_doc['content'],
            sample_doc['product_info']
        )
        
        if annotation:
            print("\n=== 索引标注结果 ===")
            print(f"标题: {annotation.title}")
            print(f"摘要: {annotation.summary}")
            print(f"关键词: {', '.join(annotation.keywords)}")
            print(f"标签: {', '.join(annotation.tags)}")
            print(f"置信度: {annotation.confidence_score:.2f}")
            print(f"处理时间: {annotation.processing_time:.2f}秒")
            
            if annotation.qa_pairs:
                print("\n问答对:")
                for i, qa in enumerate(annotation.qa_pairs, 1):
                    print(f"  Q{i}: {qa['question']}")
                    print(f"  A{i}: {qa['answer']}")
        
        # 显示统计信息
        stats = annotator.get_processing_stats()
        print("\n=== 处理统计 ===")
        for key, value in stats.items():
            print(f"{key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())