#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硅基流动索引生成系统 - 快速启动脚本
功能：
1. 一键启动所有服务
2. 环境检查和依赖安装
3. 配置向导
4. 服务状态监控
"""

import os
import sys
import subprocess
import asyncio
import json
import time
from pathlib import Path
from typing import Dict, List, Optional
import threading
import webbrowser

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

class SystemStarter:
    """系统启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.config_file = self.project_root / "config.json"
        self.requirements_file = self.project_root / "requirements.txt"
        self.processes = {}
        
    def check_python_version(self) -> bool:
        """
        检查Python版本
        
        Returns:
            是否满足版本要求
        """
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python版本过低，需要Python 3.8或更高版本")
            print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
            return False
        
        print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def create_requirements_file(self):
        """
        创建requirements.txt文件
        """
        requirements = [
            "aiohttp>=3.8.0",
            "aiofiles>=22.1.0",
            "streamlit>=1.28.0",
            "pandas>=1.5.0",
            "numpy>=1.21.0",
            "python-dotenv>=0.19.0",
            "pyyaml>=6.0",
            "requests>=2.28.0",
            "pillow>=9.0.0",
            "markdown>=3.4.0",
            "beautifulsoup4>=4.11.0",
            "python-multipart>=0.0.5",
            "uvicorn>=0.18.0",
            "fastapi>=0.95.0",
            "plotly>=5.15.0",
            "streamlit-option-menu>=0.3.6"
        ]
        
        with open(self.requirements_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(requirements))
        
        print(f"✅ 已创建依赖文件: {self.requirements_file}")
    
    def install_dependencies(self) -> bool:
        """
        安装依赖
        
        Returns:
            是否安装成功
        """
        if not self.requirements_file.exists():
            self.create_requirements_file()
        
        print("📦 开始安装依赖包...")
        
        try:
            # 升级pip
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            
            # 安装依赖
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", "-r", str(self.requirements_file)],
                check=True, capture_output=True, text=True
            )
            
            print("✅ 依赖安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def setup_config(self) -> bool:
        """
        配置设置向导
        
        Returns:
            是否配置成功
        """
        print("\n🔧 配置设置向导")
        print("="*50)
        
        config = {
            'siliconflow': {
                'api_key': 'sk-kmaipghbqavpzfnhpuuybpgrimcroynvsqlfkbnhcjcdulxj',
                'models': {
                    'text': 'Qwen/Qwen2.5-72B-Instruct',
                    'embedding': 'BAAI/bge-large-zh-v1.5'
                }
            },
            'fastgpt': {
                'api_url': 'https://api.fastgpt.in/api',
                'api_key': '',
                'dataset_id': ''
            },
            'streamlit': {
                'port': 8501,
                'host': 'localhost'
            }
        }
        
        # 如果配置文件已存在，加载现有配置
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
                    config.update(existing_config)
                print("📄 已加载现有配置")
            except Exception as e:
                print(f"⚠️  加载现有配置失败: {e}")
        
        # 交互式配置
        print("\n请输入配置信息（直接回车使用默认值）:")
        
        # FastGPT配置
        print("\n📊 FastGPT配置:")
        api_url = input(f"API地址 [{config['fastgpt']['api_url']}]: ").strip()
        if api_url:
            config['fastgpt']['api_url'] = api_url
        
        api_key = input(f"API密钥 [{config['fastgpt']['api_key'] or '未设置'}]: ").strip()
        if api_key:
            config['fastgpt']['api_key'] = api_key
        
        dataset_id = input(f"数据集ID [{config['fastgpt']['dataset_id'] or '未设置'}]: ").strip()
        if dataset_id:
            config['fastgpt']['dataset_id'] = dataset_id
        
        # Streamlit配置
        print("\n🌐 Streamlit配置:")
        port = input(f"端口 [{config['streamlit']['port']}]: ").strip()
        if port and port.isdigit():
            config['streamlit']['port'] = int(port)
        
        # 保存配置
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"✅ 配置已保存到: {self.config_file}")
            return True
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
            return False
    
    def create_directories(self):
        """
        创建必要的目录
        """
        directories = ['logs', 'results', 'temp', 'uploads']
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(exist_ok=True)
        
        print("✅ 目录结构创建完成")
    
    def start_streamlit_app(self) -> bool:
        """
        启动Streamlit应用
        
        Returns:
            是否启动成功
        """
        try:
            # 加载配置
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            port = config.get('streamlit', {}).get('port', 8501)
            host = config.get('streamlit', {}).get('host', 'localhost')
            
            # 启动Streamlit
            cmd = [
                sys.executable, "-m", "streamlit", "run", 
                str(self.project_root / "streamlit_index_app.py"),
                "--server.port", str(port),
                "--server.address", host,
                "--server.headless", "true"
            ]
            
            print(f"🚀 启动Streamlit应用 (http://{host}:{port})")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['streamlit'] = process
            
            # 等待启动
            time.sleep(3)
            
            if process.poll() is None:
                print("✅ Streamlit应用启动成功")
                
                # 自动打开浏览器
                try:
                    webbrowser.open(f"http://{host}:{port}")
                    print("🌐 已自动打开浏览器")
                except:
                    print(f"🌐 请手动打开浏览器访问: http://{host}:{port}")
                
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"❌ Streamlit启动失败")
                print(f"错误输出: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 启动Streamlit失败: {e}")
            return False
    
    def monitor_processes(self):
        """
        监控进程状态
        """
        print("\n📊 服务状态监控")
        print("按 Ctrl+C 停止所有服务")
        print("="*50)
        
        try:
            while True:
                status_info = []
                
                for name, process in self.processes.items():
                    if process.poll() is None:
                        status_info.append(f"✅ {name}: 运行中")
                    else:
                        status_info.append(f"❌ {name}: 已停止")
                
                # 清屏并显示状态
                os.system('cls' if os.name == 'nt' else 'clear')
                print("\n📊 服务状态监控")
                print("按 Ctrl+C 停止所有服务")
                print("="*50)
                
                for info in status_info:
                    print(info)
                
                print(f"\n⏰ 监控时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                
                time.sleep(5)
                
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号，正在关闭服务...")
            self.stop_all_processes()
    
    def stop_all_processes(self):
        """
        停止所有进程
        """
        for name, process in self.processes.items():
            try:
                if process.poll() is None:
                    print(f"🛑 停止 {name}...")
                    process.terminate()
                    
                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                        print(f"✅ {name} 已停止")
                    except subprocess.TimeoutExpired:
                        print(f"⚠️  强制终止 {name}")
                        process.kill()
                        process.wait()
            except Exception as e:
                print(f"❌ 停止 {name} 失败: {e}")
        
        print("✅ 所有服务已停止")
    
    def run_quick_test(self) -> bool:
        """
        运行快速测试
        
        Returns:
            测试是否通过
        """
        print("\n🧪 运行快速测试...")
        
        try:
            # 测试索引生成器
            cmd = [sys.executable, str(self.project_root / "run_index_generator.py"), "--test-only"]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                print("✅ 快速测试通过")
                return True
            else:
                print(f"❌ 快速测试失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ 测试超时")
            return False
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            return False
    
    def show_menu(self):
        """
        显示主菜单
        """
        while True:
            print("\n" + "="*60)
            print("🤖 硅基流动索引生成系统 - 启动菜单")
            print("="*60)
            print("1. 🚀 一键启动 (推荐)")
            print("2. 🔧 配置设置")
            print("3. 📦 安装依赖")
            print("4. 🧪 运行测试")
            print("5. 🌐 仅启动前端")
            print("6. 📊 查看系统状态")
            print("7. 📖 查看帮助")
            print("0. 🚪 退出")
            print("="*60)
            
            choice = input("请选择操作 [1]: ").strip() or "1"
            
            if choice == "1":
                self.full_startup()
                break
            elif choice == "2":
                self.setup_config()
            elif choice == "3":
                self.install_dependencies()
            elif choice == "4":
                self.run_quick_test()
            elif choice == "5":
                if self.config_file.exists():
                    self.start_streamlit_app()
                    self.monitor_processes()
                    break
                else:
                    print("❌ 请先进行配置设置")
            elif choice == "6":
                self.show_system_status()
            elif choice == "7":
                self.show_help()
            elif choice == "0":
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请重新输入")
    
    def full_startup(self):
        """
        完整启动流程
        """
        print("\n🚀 开始完整启动流程...")
        
        # 1. 检查Python版本
        if not self.check_python_version():
            return
        
        # 2. 创建目录
        self.create_directories()
        
        # 3. 安装依赖
        if not self.install_dependencies():
            return
        
        # 4. 配置设置
        if not self.config_file.exists():
            if not self.setup_config():
                return
        
        # 5. 运行测试
        if not self.run_quick_test():
            print("⚠️  测试失败，但仍可继续启动")
        
        # 6. 启动服务
        if self.start_streamlit_app():
            self.monitor_processes()
        else:
            print("❌ 启动失败")
    
    def show_system_status(self):
        """
        显示系统状态
        """
        print("\n📊 系统状态")
        print("="*40)
        
        # Python版本
        version = sys.version_info
        print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
        
        # 配置文件
        if self.config_file.exists():
            print("✅ 配置文件: 已存在")
        else:
            print("❌ 配置文件: 不存在")
        
        # 依赖文件
        if self.requirements_file.exists():
            print("✅ 依赖文件: 已存在")
        else:
            print("❌ 依赖文件: 不存在")
        
        # 目录结构
        required_dirs = ['logs', 'results', 'temp', 'uploads']
        missing_dirs = [d for d in required_dirs if not (self.project_root / d).exists()]
        
        if not missing_dirs:
            print("✅ 目录结构: 完整")
        else:
            print(f"⚠️  目录结构: 缺少 {', '.join(missing_dirs)}")
        
        # 核心文件
        core_files = [
            'siliconflow_index_generator.py',
            'fastgpt_api_client.py',
            'streamlit_index_app.py',
            'run_index_generator.py'
        ]
        
        missing_files = [f for f in core_files if not (self.project_root / f).exists()]
        
        if not missing_files:
            print("✅ 核心文件: 完整")
        else:
            print(f"❌ 核心文件: 缺少 {', '.join(missing_files)}")
    
    def show_help(self):
        """
        显示帮助信息
        """
        help_text = """
📖 硅基流动索引生成系统 - 帮助文档

🎯 系统功能:
• 使用硅基流动大模型生成知识库索引
• 支持多种文档格式处理
• 自动同步到FastGPT知识库
• 提供Web界面管理

🚀 快速开始:
1. 选择"一键启动"进行完整安装和配置
2. 在配置向导中输入FastGPT的API信息
3. 系统会自动启动Web界面
4. 通过浏览器访问管理界面

🔧 配置说明:
• FastGPT API密钥: 用于连接FastGPT服务
• 数据集ID: 目标知识库的ID
• 硅基流动API: 已预配置，无需修改

📁 文件结构:
• config.json: 主配置文件
• logs/: 日志文件目录
• results/: 处理结果目录
• uploads/: 上传文件目录

🆘 常见问题:
• 如果启动失败，请检查Python版本(需要3.8+)
• 如果依赖安装失败，请检查网络连接
• 如果API调用失败，请检查密钥配置

📞 技术支持:
• 查看logs目录下的日志文件
• 运行"运行测试"检查系统状态
• 确保所有配置信息正确
        """
        
        print(help_text)
        input("\n按回车键返回主菜单...")

def main():
    """
    主函数
    """
    try:
        starter = SystemStarter()
        starter.show_menu()
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()