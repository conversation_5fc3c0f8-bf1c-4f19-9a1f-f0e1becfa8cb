#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强文档处理器配置文件
包含TextIn API、多模态模型等配置信息
"""

import os
from typing import Dict, Any

# TextIn API配置
TEXTIN_CONFIG = {
    'base_url': 'https://api.textin.com/ai/service/v1',
    'app_id': os.getenv('TEXTIN_APP_ID', 'your_textin_app_id'),
    'secret_code': os.getenv('TEXTIN_SECRET_CODE', 'your_textin_secret_code'),
    'timeout': 60,
    'max_retries': 3,
    'supported_formats': ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx']
}

# 多模态模型配置
MULTIMODAL_CONFIG = {
    'provider': 'openai',  # 可选: openai, azure, claude
    'openai': {
        'api_key': os.getenv('OPENAI_API_KEY', 'your_openai_api_key'),
        'model': 'gpt-4-vision-preview',
        'base_url': 'https://api.openai.com/v1',
        'max_tokens': 500,
        'timeout': 30
    },
    'azure': {
        'api_key': os.getenv('AZURE_OPENAI_API_KEY', ''),
        'endpoint': os.getenv('AZURE_OPENAI_ENDPOINT', ''),
        'deployment_name': os.getenv('AZURE_OPENAI_DEPLOYMENT', ''),
        'api_version': '2023-12-01-preview'
    },
    'claude': {
        'api_key': os.getenv('CLAUDE_API_KEY', ''),
        'model': 'claude-3-opus-20240229',
        'base_url': 'https://api.anthropic.com'
    }
}

# 文件格式优先级配置
FILE_FORMAT_CONFIG = {
    'priority': {
        'markdown': 1,
        'md': 1,
        'word': 2,
        'docx': 2,
        'doc': 2,
        'pdf': 3
    },
    'extensions': {
        'markdown': ['.md', '.markdown'],
        'word': ['.docx', '.doc'],
        'pdf': ['.pdf']
    },
    'max_file_size': 50 * 1024 * 1024,  # 50MB
    'encoding': 'utf-8'
}

# 图片处理配置
IMAGE_CONFIG = {
    'supported_formats': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
    'max_size': 10 * 1024 * 1024,  # 10MB
    'annotation_prompt': """
请详细描述这张图片的内容，包括：
1. 图片中的主要对象和元素
2. 图片的布局和结构
3. 图片中的文字信息（如果有）
4. 图片与文档内容的关联性

请用中文回答，描述要准确、详细且有助于理解文档内容。
""",
    'context_length': 500  # 用作上下文的字符数
}

# 内容处理配置
CONTENT_CONFIG = {
    'max_content_length': 100000,  # 最大内容长度
    'chunk_size': 4000,  # 分块大小
    'overlap_size': 200,  # 重叠大小
    'quality_threshold': 0.8,  # 内容质量阈值
    'incremental_update': True,  # 启用增量更新
    'content_hash_algorithm': 'md5'
}

# FastGPT知识库配置
FASTGPT_CONFIG = {
    'api_base_url': os.getenv('FASTGPT_API_URL', 'https://api.fastgpt.in/api'),
    'api_key': os.getenv('FASTGPT_API_KEY', 'your_fastgpt_api_key'),
    'knowledge_base_id': os.getenv('FASTGPT_KB_ID', 'your_knowledge_base_id'),
    'timeout': 30,
    'max_retries': 3,
    'batch_size': 10,
    'rate_limit': {
        'requests_per_minute': 60,
        'requests_per_hour': 1000
    }
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_path': 'logs/enhanced_processor.log',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
    'encoding': 'utf-8'
}

# 处理策略配置
PROCESSING_STRATEGY = {
    'document_selection': {
        'prefer_latest': True,  # 优先选择最新文档
        'prefer_complete': True,  # 优先选择完整文档
        'min_content_length': 100  # 最小内容长度
    },
    'content_enhancement': {
        'enable_image_annotation': True,  # 启用图片标注
        'enable_content_summary': True,  # 启用内容摘要
        'enable_keyword_extraction': True,  # 启用关键词提取
        'enable_quality_check': True  # 启用质量检查
    },
    'sync_strategy': {
        'auto_approve_high_quality': False,  # 自动批准高质量内容
        'require_manual_review': True,  # 需要人工审核
        'enable_incremental_sync': True,  # 启用增量同步
        'sync_frequency': 'daily'  # 同步频率
    }
}

# 错误处理配置
ERROR_HANDLING = {
    'max_retries': 3,
    'retry_delay': 5,  # 秒
    'timeout': 60,
    'fallback_strategies': {
        'pdf_parse_fail': 'use_ocr',  # PDF解析失败时使用OCR
        'image_annotation_fail': 'use_alt_text',  # 图片标注失败时使用alt文本
        'api_fail': 'queue_for_retry'  # API失败时加入重试队列
    }
}

# 质量控制配置
QUALITY_CONTROL = {
    'content_quality_checks': {
        'min_word_count': 50,
        'max_word_count': 50000,
        'check_encoding': True,
        'check_completeness': True,
        'check_readability': True
    },
    'image_quality_checks': {
        'min_resolution': (100, 100),
        'max_resolution': (4000, 4000),
        'check_clarity': True,
        'check_relevance': True
    },
    'metadata_requirements': {
        'require_title': True,
        'require_category': True,
        'require_product_id': True,
        'require_timestamp': True
    }
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    'parallel_processing': {
        'enable': True,
        'max_workers': 4,
        'chunk_size': 10
    },
    'caching': {
        'enable': True,
        'cache_dir': 'cache',
        'max_cache_size': 1024 * 1024 * 1024,  # 1GB
        'cache_ttl': 3600  # 1小时
    },
    'memory_management': {
        'max_memory_usage': 2 * 1024 * 1024 * 1024,  # 2GB
        'gc_threshold': 0.8,
        'enable_memory_monitoring': True
    }
}

# 安全配置
SECURITY_CONFIG = {
    'api_key_encryption': True,
    'content_sanitization': True,
    'file_type_validation': True,
    'path_traversal_protection': True,
    'max_file_size_check': True,
    'virus_scan': False,  # 需要集成杀毒软件
    'allowed_file_extensions': ['.md', '.pdf', '.docx', '.doc', '.txt'],
    'blocked_file_patterns': ['*.exe', '*.bat', '*.cmd', '*.ps1']
}

# 监控配置
MONITORING_CONFIG = {
    'enable_metrics': True,
    'metrics_endpoint': '/metrics',
    'health_check_endpoint': '/health',
    'performance_tracking': {
        'track_processing_time': True,
        'track_memory_usage': True,
        'track_api_calls': True,
        'track_error_rates': True
    },
    'alerts': {
        'enable_email_alerts': False,
        'enable_webhook_alerts': False,
        'error_threshold': 0.1,  # 10%错误率触发告警
        'response_time_threshold': 30  # 30秒响应时间触发告警
    }
}

def get_config(config_name: str) -> Dict[str, Any]:
    """
    获取指定配置
    
    Args:
        config_name (str): 配置名称
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    configs = {
        'textin': TEXTIN_CONFIG,
        'multimodal': MULTIMODAL_CONFIG,
        'file_format': FILE_FORMAT_CONFIG,
        'image': IMAGE_CONFIG,
        'content': CONTENT_CONFIG,
        'fastgpt': FASTGPT_CONFIG,
        'logging': LOGGING_CONFIG,
        'processing': PROCESSING_STRATEGY,
        'error': ERROR_HANDLING,
        'quality': QUALITY_CONTROL,
        'performance': PERFORMANCE_CONFIG,
        'security': SECURITY_CONFIG,
        'monitoring': MONITORING_CONFIG
    }
    
    return configs.get(config_name, {})

def validate_config() -> bool:
    """
    验证配置的完整性和有效性
    
    Returns:
        bool: 配置是否有效
    """
    required_env_vars = [
        'TEXTIN_APP_ID',
        'TEXTIN_SECRET_CODE',
        'OPENAI_API_KEY',
        'FASTGPT_API_KEY',
        'FASTGPT_KB_ID'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var) or os.getenv(var) == f'your_{var.lower()}':
            missing_vars.append(var)
    
    if missing_vars:
        print(f"警告: 以下环境变量未正确配置: {', '.join(missing_vars)}")
        return False
    
    return True

if __name__ == "__main__":
    # 验证配置
    if validate_config():
        print("配置验证通过")
    else:
        print("配置验证失败，请检查环境变量")
    
    # 打印配置信息
    print("\n当前配置:")
    for name in ['textin', 'multimodal', 'fastgpt']:
        config = get_config(name)
        print(f"{name.upper()}: {config}")