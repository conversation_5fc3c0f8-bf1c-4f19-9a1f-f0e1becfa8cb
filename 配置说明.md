# 产品知识库数据处理系统 - 配置说明

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.7 或更高版本
- PostgreSQL 数据库

### 2. 配置修改

在运行系统之前，请修改 `config.py` 文件中的配置信息：

#### 数据库配置

```python
DATABASE_CONFIG = {
    'host': 'localhost',  # 数据库服务器地址
    'user': 'postgres',   # 数据库用户名
    'password': 'your_password',  # 请修改为您的数据库密码
    'dbname': 'product_knowledge_db',  # 数据库名称
    'port': 5432
}
```

**重要提示：**
- 请将 `your_password` 替换为您的实际数据库密码
- 确保数据库服务正在运行
- 确保指定的数据库已创建（如果不存在，系统会尝试创建表结构）

#### API配置

```python
API_CONFIG = {
    'base_url': 'https://zkmall.zktecoip.com',
    'username': 'your_username',  # 请修改为您的云商用户名
    'password': 'your_password',  # 请修改为您的云商密码
    'download_dir': os.path.join(SCRIPT_DIR, 'downloads')
}
```

**重要提示：**
- 请将 `your_username` 和 `your_password` 替换为您的实际云商账户信息
- 确保账户有权限访问产品数据

#### 文件路径配置

```python
FILE_PATHS = {
    'allcollections': os.path.join(SCRIPT_DIR, 'allcollections.json'),
    'product_structure': os.path.join(SCRIPT_DIR, '国内产品结构细化表.json'),
    'local_files_base': os.path.join('E:', 'pyydemo', 'work', 'new_work', 'get_mongo_alldata', 'AI客服知识库')
}
```

**重要提示：**
- `local_files_base` 路径请根据您的实际文件存储位置修改
- 确保路径存在且有读取权限

### 3. 运行系统

配置完成后，双击运行 `run_all.bat` 文件，或在命令行中执行：

```bash
run_all.bat
```

### 4. 处理流程

系统将按以下顺序执行：

1. **环境检查**：检查Python环境和必要文件
2. **依赖安装**：自动安装所需的Python包
3. **数据库初始化**：创建必要的数据表
4. **历史数据导入**：导入 `allcollections.json` 中的数据（标记为作废）
5. **云商数据获取**：从API获取最新产品数据
6. **本地文件扫描**：扫描本地文件系统中的产品资料
7. **数据处理完成**：所有数据导入完成

### 5. 故障排除

#### 常见问题

**问题1：数据库连接失败**
- 检查数据库服务是否启动
- 验证用户名和密码是否正确
- 确认数据库主机地址和端口

**问题2：API认证失败**
- 检查云商用户名和密码
- 确认账户是否有效且未被锁定
- 检查网络连接

**问题3：文件未找到**
- 确认 `allcollections.json` 文件存在
- 确认 `国内产品结构细化表.json` 文件存在
- 检查本地文件基础路径是否正确

**问题4：权限错误**
- 确保对文件目录有读写权限
- 以管理员身份运行批处理文件

#### 日志查看

系统运行时会生成详细的日志文件：
- 位置：`logs/product_processor.log`
- 包含详细的处理过程和错误信息

### 6. 安全注意事项

- **不要将包含真实密码的 `config.py` 文件提交到版本控制系统**
- 建议为数据库创建专用用户，仅授予必要权限
- 定期更换API密码
- 确保日志文件不包含敏感信息

### 7. 性能优化建议

- 对于大量文件的处理，建议在非业务高峰期运行
- 可以通过修改 `PROCESS_CONFIG` 中的 `batch_size` 来调整批处理大小
- 网络较慢时可以增加 `timeout` 值

### 8. 维护建议

- 定期备份数据库
- 定期清理下载的临时文件
- 监控日志文件大小，必要时进行轮转
- 定期更新产品结构数据文件