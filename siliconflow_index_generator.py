#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硅基流动大模型索引生成器
功能：
1. 使用硅基流动大模型生成产品属性索引
2. 生成知识片段标题和内容索引
3. 通过FastGPT接口写入索引数据
4. 支持按产品业务块分类同步
"""

import asyncio
import aiohttp
import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import hashlib
import re
from pathlib import Path
import time

from config.siliconflow_config import SiliconFlowConfig
from utils.file_processor import FileProcessor
from utils.image_analyzer import ImageAnalyzer
from utils.content_optimizer import ContentOptimizer
from utils.vector_processor import VectorProcessor
from utils.performance_monitor import PerformanceMonitor
from fastgpt_api_client import FastGPTAPIClient
from qwen_index_annotator import QwenIndexAnnotator
from document_replacement_manager import DocumentReplacementManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/index_generator.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ProductAttribute:
    """产品属性数据结构"""
    name: str
    value: str
    category: str
    description: str = ""
    weight: float = 1.0
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

@dataclass
class KnowledgeSegment:
    """知识片段数据结构"""
    title: str
    content: str
    product_name: str
    business_block: str
    attributes: List[ProductAttribute]
    keywords: List[str] = None
    summary: str = ""
    relevance_score: float = 1.0
    
    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []

@dataclass
class IndexData:
    """索引数据结构"""
    id: str
    title: str
    content: str
    metadata: Dict[str, Any]
    vector_content: str
    tags: List[str]
    business_block: str
    created_at: str
    
class SiliconFlowIndexGenerator:
    """硅基流动索引生成器"""
    
    def __init__(self, api_key: str, fastgpt_config: Dict[str, str]):
        """
        初始化索引生成器
        
        Args:
            api_key: 硅基流动API密钥
            fastgpt_config: FastGPT配置信息
        """
        self.api_key = api_key
        self.api_base = "https://api.siliconflow.cn/v1"
        self.fastgpt_config = fastgpt_config
        
        # 模型配置
        self.models = {
            'text': 'Qwen/Qwen2.5-72B-Instruct',
            'embedding': 'BAAI/bge-large-zh-v1.5'
        }
        
        # 业务块配置
        self.business_blocks = {
            '门禁系统': ['门禁控制器', '读卡器', '门禁软件', '门禁配件'],
            '考勤系统': ['考勤机', '考勤软件', '考勤配件'],
            '访客系统': ['访客机', '访客软件', '访客配件'],
            '消费系统': ['消费机', '消费软件', '消费配件'],
            '通道系统': ['通道闸', '通道控制器', '通道软件'],
            '停车系统': ['停车设备', '停车软件', '停车配件']
        }
        
        # 新增：千问3索引标注器
        self.qwen_annotator = QwenIndexAnnotator()
        
        # 新增：文档替换管理器
        self.doc_replacement_manager = DocumentReplacementManager()
        
        # 索引生成统计
        self.stats = {
            'total_processed': 0,
            'successful_indexes': 0,
            'failed_indexes': 0,
            'total_cost': 0.0,
            'qwen_annotations': 0,
            'replaced_documents': 0
        }
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if hasattr(self, 'session'):
            await self.session.close()
    
    def get_headers(self) -> Dict[str, str]:
        """获取API请求头"""
        return {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    async def call_siliconflow_api(self, model: str, messages: List[Dict], task_name: str) -> Dict[str, Any]:
        """
        调用硅基流动API
        
        Args:
            model: 模型名称
            messages: 消息列表
            task_name: 任务名称
            
        Returns:
            API响应结果
        """
        try:
            url = f"{self.api_base}/chat/completions"
            headers = self.get_headers()
            
            payload = {
                'model': model,
                'messages': messages,
                'temperature': 0.3,
                'max_tokens': 2000,
                'stream': False
            }
            
            logger.info(f"调用硅基流动API - 任务: {task_name}, 模型: {model}")
            
            async with self.session.post(url, headers=headers, json=payload, timeout=60) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    # 统计成本
                    usage = result.get('usage', {})
                    cost = self.calculate_cost(usage)
                    self.stats['total_cost'] += cost
                    
                    logger.info(f"API调用成功 - 任务: {task_name}, 成本: ${cost:.4f}")
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"API调用失败 - 状态码: {response.status}, 错误: {error_text}")
                    raise Exception(f"API调用失败: {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"API调用异常 - 任务: {task_name}, 错误: {str(e)}")
            raise
    
    def calculate_cost(self, usage: Dict[str, int]) -> float:
        """
        计算API调用成本
        
        Args:
            usage: API使用统计
            
        Returns:
            成本金额
        """
        # 硅基流动定价（示例价格，请根据实际调整）
        input_cost_per_1k = 0.0005  # 输入token成本
        output_cost_per_1k = 0.0015  # 输出token成本
        
        input_tokens = usage.get('prompt_tokens', 0)
        output_tokens = usage.get('completion_tokens', 0)
        
        cost = (input_tokens / 1000 * input_cost_per_1k + 
                output_tokens / 1000 * output_cost_per_1k)
        
        return cost
    
    async def generate_product_attributes_index(self, product_data: Dict[str, Any]) -> List[ProductAttribute]:
        """
        生成产品属性索引
        
        Args:
            product_data: 产品数据
            
        Returns:
            产品属性列表
        """
        try:
            # 构建提示词
            prompt = f"""
请分析以下产品数据，提取关键属性并生成索引信息：

产品数据：
{json.dumps(product_data, ensure_ascii=False, indent=2)}

请按以下JSON格式返回产品属性索引：
{{
  "attributes": [
    {{
      "name": "属性名称",
      "value": "属性值",
      "category": "属性分类",
      "description": "属性描述",
      "weight": 权重值(0-1),
      "tags": ["标签1", "标签2"]
    }}
  ]
}}

要求：
1. 提取所有重要的产品属性
2. 为每个属性分配合适的分类
3. 生成描述性的标签
4. 根据重要性设置权重
5. 确保属性名称标准化
"""
            
            messages = [
                {"role": "user", "content": prompt}
            ]
            
            response = await self.call_siliconflow_api(
                self.models['text'], 
                messages, 
                f"产品属性索引生成-{product_data.get('name', 'unknown')}"
            )
            
            # 解析响应
            content = response['choices'][0]['message']['content']
            
            # 提取JSON内容
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                result_data = json.loads(json_match.group())
                attributes = []
                
                for attr_data in result_data.get('attributes', []):
                    attribute = ProductAttribute(
                        name=attr_data['name'],
                        value=attr_data['value'],
                        category=attr_data['category'],
                        description=attr_data.get('description', ''),
                        weight=attr_data.get('weight', 1.0),
                        tags=attr_data.get('tags', [])
                    )
                    attributes.append(attribute)
                
                logger.info(f"成功生成 {len(attributes)} 个产品属性索引")
                return attributes
            else:
                logger.warning("无法解析产品属性索引响应")
                return []
                
        except Exception as e:
            logger.error(f"生成产品属性索引失败: {str(e)}")
            return []
    
    async def generate_knowledge_segment_index(self, title: str, content: str, 
                                             product_name: str, business_block: str,
                                             attributes: List[ProductAttribute]) -> KnowledgeSegment:
        """
        生成知识片段索引
        
        Args:
            title: 知识片段标题
            content: 知识片段内容
            product_name: 产品名称
            business_block: 业务块
            attributes: 产品属性列表
            
        Returns:
            知识片段对象
        """
        try:
            # 构建提示词
            attributes_text = "\n".join([
                f"- {attr.name}: {attr.value} ({attr.category})"
                for attr in attributes
            ])
            
            prompt = f"""
请分析以下知识片段，生成优化的索引信息：

标题：{title}
内容：{content}
产品名称：{product_name}
业务块：{business_block}

产品属性：
{attributes_text}

请按以下JSON格式返回索引信息：
{{
  "optimized_title": "优化后的标题",
  "summary": "内容摘要(100字以内)",
  "keywords": ["关键词1", "关键词2", "关键词3"],
  "relevance_score": 相关性评分(0-1),
  "enhanced_content": "增强后的内容(包含关键信息提取)"
}}

要求：
1. 优化标题使其更具描述性
2. 生成简洁的内容摘要
3. 提取5-10个关键词
4. 评估与产品属性的相关性
5. 增强内容的可搜索性
"""
            
            messages = [
                {"role": "user", "content": prompt}
            ]
            
            response = await self.call_siliconflow_api(
                self.models['text'], 
                messages, 
                f"知识片段索引生成-{title[:20]}"
            )
            
            # 解析响应
            content_response = response['choices'][0]['message']['content']
            
            # 提取JSON内容
            json_match = re.search(r'\{.*\}', content_response, re.DOTALL)
            if json_match:
                result_data = json.loads(json_match.group())
                
                segment = KnowledgeSegment(
                    title=result_data.get('optimized_title', title),
                    content=result_data.get('enhanced_content', content),
                    product_name=product_name,
                    business_block=business_block,
                    attributes=attributes,
                    keywords=result_data.get('keywords', []),
                    summary=result_data.get('summary', ''),
                    relevance_score=result_data.get('relevance_score', 1.0)
                )
                
                logger.info(f"成功生成知识片段索引: {segment.title}")
                return segment
            else:
                logger.warning("无法解析知识片段索引响应")
                return KnowledgeSegment(
                    title=title,
                    content=content,
                    product_name=product_name,
                    business_block=business_block,
                    attributes=attributes
                )
                
        except Exception as e:
            logger.error(f"生成知识片段索引失败: {str(e)}")
            return KnowledgeSegment(
                title=title,
                content=content,
                product_name=product_name,
                business_block=business_block,
                attributes=attributes
            )
    
    def create_index_data(self, segment: KnowledgeSegment) -> IndexData:
        """
        创建索引数据
        
        Args:
            segment: 知识片段
            
        Returns:
            索引数据对象
        """
        # 生成唯一ID
        content_hash = hashlib.md5(
            f"{segment.title}{segment.content}{segment.product_name}".encode('utf-8')
        ).hexdigest()[:16]
        
        # 构建元数据
        metadata = {
            'product_name': segment.product_name,
            'business_block': segment.business_block,
            'summary': segment.summary,
            'relevance_score': segment.relevance_score,
            'attributes': [asdict(attr) for attr in segment.attributes],
            'keywords': segment.keywords,
            'source_type': 'enhanced_document',
            'processing_method': 'siliconflow_ai'
        }
        
        # 构建向量化内容（用于embedding）
        vector_content = f"""
标题: {segment.title}
摘要: {segment.summary}
内容: {segment.content}
产品: {segment.product_name}
业务块: {segment.business_block}
关键词: {', '.join(segment.keywords)}
属性: {', '.join([f"{attr.name}:{attr.value}" for attr in segment.attributes])}
"""
        
        # 构建标签
        tags = [
            segment.business_block,
            segment.product_name,
            *segment.keywords,
            *[attr.category for attr in segment.attributes],
            *[tag for attr in segment.attributes for tag in attr.tags]
        ]
        
        # 去重并过滤空值
        tags = list(set([tag for tag in tags if tag and tag.strip()]))
        
        return IndexData(
            id=content_hash,
            title=segment.title,
            content=segment.content,
            metadata=metadata,
            vector_content=vector_content,
            tags=tags,
            business_block=segment.business_block,
            created_at=datetime.now().isoformat()
        )
    
    async def write_to_fastgpt(self, index_data: IndexData) -> bool:
        """
        将索引数据写入FastGPT
        
        Args:
            index_data: 索引数据
            
        Returns:
            是否成功
        """
        try:
            # FastGPT API配置
            fastgpt_url = self.fastgpt_config['api_url']
            fastgpt_key = self.fastgpt_config['api_key']
            dataset_id = self.fastgpt_config['dataset_id']
            
            headers = {
                'Authorization': f'Bearer {fastgpt_key}',
                'Content-Type': 'application/json'
            }
            
            # 构建FastGPT数据格式
            payload = {
                'datasetId': dataset_id,
                'parentId': None,
                'q': index_data.title,
                'a': index_data.content,
                'indexes': [
                    {
                        'text': index_data.vector_content,
                        'dataId': index_data.id
                    }
                ],
                'metadata': index_data.metadata,
                'tags': index_data.tags
            }
            
            logger.info(f"写入FastGPT - 标题: {index_data.title}")
            
            async with self.session.post(
                f"{fastgpt_url}/core/dataset/data/insertData",
                headers=headers,
                json=payload,
                timeout=30
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"成功写入FastGPT - ID: {index_data.id}")
                    self.stats['successful_indexes'] += 1
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"写入FastGPT失败 - 状态码: {response.status}, 错误: {error_text}")
                    self.stats['failed_indexes'] += 1
                    return False
                    
        except Exception as e:
            logger.error(f"写入FastGPT异常: {str(e)}")
            self.stats['failed_indexes'] += 1
            return False
    
    async def process_product_documents(self, product_name: str, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        处理产品文档并生成索引
        
        Args:
            product_name: 产品名称
            documents: 文档列表
            
        Returns:
            处理结果统计
        """
        logger.info(f"开始处理产品文档: {product_name}")
        
        # 确定业务块
        business_block = self.determine_business_block(product_name)
        
        results = {
            'product_name': product_name,
            'business_block': business_block,
            'processed_documents': 0,
            'successful_indexes': 0,
            'failed_indexes': 0,
            'generated_indexes': []
        }
        
        for doc in documents:
            try:
                self.stats['total_processed'] += 1
                results['processed_documents'] += 1
                
                # 生成产品属性索引
                attributes = await self.generate_product_attributes_index(doc)
                
                # 分割文档内容为知识片段
                segments = self.split_document_content(doc.get('content', ''), doc.get('title', ''))
                
                for segment_title, segment_content in segments:
                    # 生成知识片段索引
                    knowledge_segment = await self.generate_knowledge_segment_index(
                        segment_title, segment_content, product_name, business_block, attributes
                    )
                    
                    # 创建索引数据
                    index_data = self.create_index_data(knowledge_segment)
                    
                    # 写入FastGPT
                    success = await self.write_to_fastgpt(index_data)
                    
                    if success:
                        results['successful_indexes'] += 1
                        results['generated_indexes'].append({
                            'id': index_data.id,
                            'title': index_data.title,
                            'tags': index_data.tags[:5]  # 只保存前5个标签
                        })
                    else:
                        results['failed_indexes'] += 1
                
                # 添加延迟避免API限制
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"处理文档失败: {str(e)}")
                results['failed_indexes'] += 1
        
        logger.info(f"完成产品文档处理: {product_name}, 成功: {results['successful_indexes']}, 失败: {results['failed_indexes']}")
        return results
    
    def determine_business_block(self, product_name: str) -> str:
        """
        确定产品所属业务块
        
        Args:
            product_name: 产品名称
            
        Returns:
            业务块名称
        """
        product_name_lower = product_name.lower()
        
        for block, keywords in self.business_blocks.items():
            for keyword in keywords:
                if keyword.lower() in product_name_lower:
                    return block
        
        # 默认业务块
        return '其他系统'
    
    def split_document_content(self, content: str, title: str) -> List[Tuple[str, str]]:
        """
        分割文档内容为知识片段
        
        Args:
            content: 文档内容
            title: 文档标题
            
        Returns:
            知识片段列表 (标题, 内容)
        """
        segments = []
        
        # 按段落分割
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        current_segment = ""
        segment_count = 1
        
        for paragraph in paragraphs:
            # 检查是否是标题行
            if (paragraph.startswith('#') or 
                len(paragraph) < 100 and 
                ('：' in paragraph or '：' in paragraph or paragraph.endswith('：'))):
                
                # 保存当前片段
                if current_segment:
                    segment_title = f"{title} - 第{segment_count}部分"
                    segments.append((segment_title, current_segment.strip()))
                    segment_count += 1
                
                # 开始新片段
                current_segment = paragraph + "\n\n"
            else:
                current_segment += paragraph + "\n\n"
                
                # 如果片段过长，分割
                if len(current_segment) > 1500:
                    segment_title = f"{title} - 第{segment_count}部分"
                    segments.append((segment_title, current_segment.strip()))
                    segment_count += 1
                    current_segment = ""
        
        # 保存最后一个片段
        if current_segment:
            segment_title = f"{title} - 第{segment_count}部分"
            segments.append((segment_title, current_segment.strip()))
        
        # 如果没有分割出片段，使用整个内容
        if not segments:
            segments.append((title, content))
        
        return segments
    
    async def batch_process_by_business_block(self, business_block: str, products_data: Dict[str, List[Dict]]) -> Dict[str, Any]:
        """
        按业务块批量处理产品数据
        
        Args:
            business_block: 业务块名称
            products_data: 产品数据字典
            
        Returns:
            处理结果
        """
        logger.info(f"开始批量处理业务块: {business_block}")
        
        results = {
            'business_block': business_block,
            'total_products': len(products_data),
            'processed_products': 0,
            'total_indexes': 0,
            'successful_indexes': 0,
            'failed_indexes': 0,
            'products_results': []
        }
        
        for product_name, documents in products_data.items():
            try:
                product_result = await self.process_product_documents(product_name, documents)
                results['products_results'].append(product_result)
                results['processed_products'] += 1
                results['total_indexes'] += product_result['successful_indexes'] + product_result['failed_indexes']
                results['successful_indexes'] += product_result['successful_indexes']
                results['failed_indexes'] += product_result['failed_indexes']
                
            except Exception as e:
                logger.error(f"处理产品失败: {product_name}, 错误: {str(e)}")
                results['failed_indexes'] += 1
        
        logger.info(f"完成业务块处理: {business_block}, 总索引: {results['total_indexes']}, 成功: {results['successful_indexes']}")
        return results
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """
        获取处理摘要
        
        Returns:
            处理摘要信息
        """
        total_processed = self.stats['total_processed']
        success_rate = (self.stats['successful_indexes'] / max(total_processed, 1)) * 100
        
        return {
            'statistics': self.stats,
            'success_rate': success_rate,
            'average_cost_per_document': self.stats['total_cost'] / max(total_processed, 1),
            'timestamp': datetime.now().isoformat()
        }

# 使用示例
async def main():
    """主函数示例"""
    # 配置信息
    api_key = "sk-kmaipghbqavpzfnhpuuybpgrimcroynvsqlfkbnhcjcdulxj"
    
    fastgpt_config = {
        'api_url': 'https://api.fastgpt.in/api',
        'api_key': 'fastgpt-your-api-key',
        'dataset_id': 'your-dataset-id'
    }
    
    # 示例产品数据
    sample_products = {
        '门禁系统': {
            'ZK-AC2000门禁控制器': [
                {
                    'title': 'ZK-AC2000产品规格',
                    'content': '双门门禁控制器，支持TCP/IP通讯，内置Web服务器，支持多种读卡器接口...',
                    'name': 'ZK-AC2000',
                    'category': '门禁控制器',
                    'specifications': {
                        '通讯方式': 'TCP/IP',
                        '门数': '2门',
                        '用户容量': '30000'
                    }
                }
            ]
        }
    }
    
    # 创建索引生成器
    async with SiliconFlowIndexGenerator(api_key, fastgpt_config) as generator:
        # 按业务块处理
        for business_block, products_data in sample_products.items():
            result = await generator.batch_process_by_business_block(business_block, products_data)
            print(f"业务块 {business_block} 处理完成:")
            print(f"  - 处理产品数: {result['processed_products']}")
            print(f"  - 成功索引数: {result['successful_indexes']}")
            print(f"  - 失败索引数: {result['failed_indexes']}")
        
        # 获取处理摘要
        summary = generator.get_processing_summary()
        print(f"\n处理摘要:")
        print(f"  - 总处理数: {summary['statistics']['total_processed']}")
        print(f"  - 成功率: {summary['success_rate']:.1f}%")
        print(f"  - 总成本: ${summary['statistics']['total_cost']:.4f}")
        print(f"  - 平均成本: ${summary['average_cost_per_document']:.4f}/文档")

if __name__ == "__main__":
    asyncio.run(main())