#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品模型映射与迁移方案
基于Excel数据分析结果，生成数据库迁移脚本和映射关系
"""

import json
import pandas as pd
from datetime import datetime
import re

class ProductModelMapper:
    """产品模型映射器"""
    
    def __init__(self):
        self.load_excel_data()
        self.field_mappings = self.create_field_mappings()
        self.category_hierarchy = self.build_category_hierarchy()
        
    def load_excel_data(self):
        """加载Excel数据"""
        try:
            with open('D:/sync-db-fastgpt/excel_analysis_results.json', 'r', encoding='utf-8') as f:
                self.excel_data = json.load(f)
            print("Excel数据加载成功")
        except Exception as e:
            print(f"加载Excel数据失败: {e}")
            self.excel_data = {}
    
    def create_field_mappings(self):
        """创建字段映射关系"""
        mappings = {
            # allcollections.xlsx 字段映射
            'allcollections': {
                'id-fastgpt': 'fastgpt_id',
                'name': 'document_name',
                '资料用途': 'material_usage',
                '产品业务范畴': 'business_scope',
                '产品上市时间': 'launch_time',
                '产品型号': 'model',
                '产品名称': 'name',
                '产品所支持的软件': 'supported_software'
            },
            # 产品结构细化表字段映射
            'product_structure': {
                '产品分类': 'category_name',
                '类别': 'category_type',
                '系列': 'series_name',
                '产品型号': 'model',
                '备注（定义对应型号所包含的内容）': 'collection_requirements',
                '收集情况': 'collection_status'
            }
        }
        return mappings
    
    def build_category_hierarchy(self):
        """构建产品分类层次结构"""
        hierarchy = {}
        
        if 'product_structure' in self.excel_data:
            data = self.excel_data['product_structure']['data']
            
            for item in data:
                category = item.get('产品分类', '')
                sub_category = item.get('类别', '')
                series = item.get('系列', '')
                model = item.get('产品型号', '')
                
                if category not in hierarchy:
                    hierarchy[category] = {
                        'level': 1,
                        'type': '产品分类',
                        'children': {}
                    }
                
                if sub_category and sub_category not in hierarchy[category]['children']:
                    hierarchy[category]['children'][sub_category] = {
                        'level': 2,
                        'type': '类别',
                        'parent': category,
                        'children': {}
                    }
                
                if series and sub_category:
                    if series not in hierarchy[category]['children'][sub_category]['children']:
                        hierarchy[category]['children'][sub_category]['children'][series] = {
                            'level': 3,
                            'type': '系列',
                            'parent': sub_category,
                            'models': []
                        }
                    
                    if model:
                        hierarchy[category]['children'][sub_category]['children'][series]['models'].append({
                            'model': model,
                            'collection_requirements': item.get('备注（定义对应型号所包含的内容）', ''),
                            'collection_status': item.get('收集情况', '未开始')
                        })
        
        return hierarchy
    
    def generate_category_insert_sql(self):
        """生成产品分类插入SQL"""
        sql_statements = []
        category_id = 1
        
        # 插入顶级分类
        for category_name, category_info in self.category_hierarchy.items():
            sql = f"""
INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES ({category_id}, '{category_name}', NULL, 1, '产品分类', NULL, '进行中');
"""
            sql_statements.append(sql)
            parent_id = category_id
            category_id += 1
            
            # 插入二级分类（类别）
            for sub_name, sub_info in category_info.get('children', {}).items():
                sql = f"""
INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES ({category_id}, '{sub_name}', {parent_id}, 2, '类别', NULL, '进行中');
"""
                sql_statements.append(sql)
                sub_parent_id = category_id
                category_id += 1
                
                # 插入三级分类（系列）
                for series_name, series_info in sub_info.get('children', {}).items():
                    sql = f"""
INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES ({category_id}, '{series_name}', {sub_parent_id}, 3, '系列', '{series_name}', '进行中');
"""
                    sql_statements.append(sql)
                    category_id += 1
        
        return sql_statements
    
    def generate_document_type_insert_sql(self):
        """生成文档类型插入SQL"""
        document_types = [
            ('彩页', '产品宣传彩页', '彩页', True, 5, '.pdf,.doc,.docx', 10485760),
            ('入门指南', '产品入门指南', '入门指南', True, 4, '.pdf,.doc,.docx', 10485760),
            ('用户手册', '产品用户手册', '用户手册', True, 3, '.pdf,.doc,.docx', 52428800),
            ('技术文档', '技术规格文档', '技术文档', False, 2, '.pdf,.doc,.docx', 52428800),
            ('宣传资料', '产品宣传资料', '宣传资料', False, 1, '.pdf,.doc,.docx,.ppt,.pptx', 52428800),
            ('培训资料', '产品培训资料', '培训资料', False, 1, '.pdf,.doc,.docx,.ppt,.pptx', 104857600)
        ]
        
        sql_statements = []
        for i, (name, desc, category, required, priority, extensions, max_size) in enumerate(document_types, 1):
            sql = f"""
INSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) 
VALUES ({i}, '{name}', '{desc}', '{category}', {required}, {priority}, '{extensions}', {max_size}, TRUE);
"""
            sql_statements.append(sql)
        
        return sql_statements
    
    def analyze_data_quality(self):
        """分析数据质量"""
        quality_report = {
            'allcollections': {},
            'product_structure': {}
        }
        
        # 分析allcollections数据质量
        if 'allcollections' in self.excel_data:
            data = self.excel_data['allcollections']['data']
            total_records = len(data)
            
            # 检查必填字段完整性
            required_fields = ['产品型号', '产品名称', '资料用途']
            field_completeness = {}
            
            for field in required_fields:
                non_empty = sum(1 for item in data if item.get(field) and str(item.get(field)).strip())
                field_completeness[field] = {
                    'total': total_records,
                    'non_empty': non_empty,
                    'completeness_rate': non_empty / total_records if total_records > 0 else 0
                }
            
            # 分析产品型号分布
            model_distribution = {}
            for item in data:
                model = item.get('产品型号', '未知')
                model_distribution[model] = model_distribution.get(model, 0) + 1
            
            # 分析业务范畴分布
            scope_distribution = {}
            for item in data:
                scope = item.get('产品业务范畴', '未知')
                scope_distribution[scope] = scope_distribution.get(scope, 0) + 1
            
            quality_report['allcollections'] = {
                'total_records': total_records,
                'field_completeness': field_completeness,
                'model_distribution': dict(sorted(model_distribution.items(), key=lambda x: x[1], reverse=True)[:20]),
                'scope_distribution': scope_distribution
            }
        
        # 分析产品结构数据质量
        if 'product_structure' in self.excel_data:
            data = self.excel_data['product_structure']['data']
            total_records = len(data)
            
            # 统计收集状态
            collection_status = {}
            for item in data:
                status = item.get('收集情况', '未知')
                collection_status[status] = collection_status.get(status, 0) + 1
            
            # 统计产品分类分布
            category_distribution = {}
            for item in data:
                category = item.get('产品分类', '未知')
                category_distribution[category] = category_distribution.get(category, 0) + 1
            
            quality_report['product_structure'] = {
                'total_records': total_records,
                'collection_status': collection_status,
                'category_distribution': category_distribution
            }
        
        return quality_report
    
    def generate_migration_plan(self):
        """生成数据迁移计划"""
        migration_plan = {
            'phase1': {
                'name': '基础数据迁移',
                'description': '迁移产品分类、文档类型等基础数据',
                'tasks': [
                    '创建ProductCategory表数据',
                    '创建DocumentType表数据',
                    '建立分类层次关系'
                ]
            },
            'phase2': {
                'name': '产品数据迁移',
                'description': '迁移产品基本信息',
                'tasks': [
                    '从allcollections.xlsx提取产品信息',
                    '数据清洗和标准化',
                    '创建Product表数据'
                ]
            },
            'phase3': {
                'name': '文档数据迁移',
                'description': '迁移产品文档信息',
                'tasks': [
                    '从allcollections.xlsx提取文档信息',
                    '建立产品与文档关联关系',
                    '创建ProductDocument表数据'
                ]
            },
            'phase4': {
                'name': '数据验证和优化',
                'description': '验证数据完整性并优化',
                'tasks': [
                    '数据完整性检查',
                    '重复数据清理',
                    '索引优化',
                    '数据质量报告生成'
                ]
            }
        }
        
        return migration_plan
    
    def export_results(self):
        """导出分析结果"""
        results = {
            'field_mappings': self.field_mappings,
            'category_hierarchy': self.category_hierarchy,
            'category_insert_sql': self.generate_category_insert_sql(),
            'document_type_insert_sql': self.generate_document_type_insert_sql(),
            'data_quality_report': self.analyze_data_quality(),
            'migration_plan': self.generate_migration_plan(),
            'generated_at': datetime.now().isoformat()
        }
        
        # 保存为JSON文件
        with open('D:/sync-db-fastgpt/产品模型映射分析结果.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 生成SQL迁移脚本
        sql_script = []
        sql_script.append('-- 产品分类数据插入脚本')
        sql_script.extend(results['category_insert_sql'])
        sql_script.append('\n-- 文档类型数据插入脚本')
        sql_script.extend(results['document_type_insert_sql'])
        
        with open('D:/sync-db-fastgpt/数据迁移脚本.sql', 'w', encoding='utf-8') as f:
            f.write('\n'.join(sql_script))
        
        print("分析结果已导出:")
        print("- 产品模型映射分析结果.json")
        print("- 数据迁移脚本.sql")
        
        return results

def main():
    """主函数"""
    print("开始产品模型映射与迁移分析...")
    
    mapper = ProductModelMapper()
    results = mapper.export_results()
    
    # 打印关键统计信息
    print("\n=== 分析摘要 ===")
    
    if 'data_quality_report' in results:
        quality = results['data_quality_report']
        
        if 'allcollections' in quality:
            print(f"\nallcollections数据:")
            print(f"  总记录数: {quality['allcollections']['total_records']}")
            print(f"  产品型号种类: {len(quality['allcollections']['model_distribution'])}")
            print(f"  业务范畴种类: {len(quality['allcollections']['scope_distribution'])}")
        
        if 'product_structure' in quality:
            print(f"\n产品结构数据:")
            print(f"  总记录数: {quality['product_structure']['total_records']}")
            print(f"  产品分类数: {len(quality['product_structure']['category_distribution'])}")
            print(f"  收集状态分布: {quality['product_structure']['collection_status']}")
    
    print(f"\n分类层次结构: {len(results['category_hierarchy'])} 个顶级分类")
    print(f"迁移计划: {len(results['migration_plan'])} 个阶段")
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()