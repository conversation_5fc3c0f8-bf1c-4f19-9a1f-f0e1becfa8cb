# -*- coding: utf-8 -*-
"""
管理员审核Web应用
提供Web界面供管理员审核知识库同步变更
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
import json
from datetime import datetime
from fastgpt_sync_manager import FastGPTSyncManager
from config import DATABASE_CONFIG
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'your_secret_key_here'  # 请修改为安全的密钥

# FastGPT配置 - 请根据实际情况修改
FASTGPT_CONFIG = {
    'api_url': 'http://localhost:3000',
    'api_key': 'your_fastgpt_api_key',
    'dataset_id': 'your_dataset_id',
    'collection_id': 'your_collection_id',
    'bill_id': None
}

# 创建同步管理器实例
sync_manager = None

def get_sync_manager():
    """获取同步管理器实例"""
    global sync_manager
    if sync_manager is None:
        sync_manager = FastGPTSyncManager(FASTGPT_CONFIG)
    return sync_manager

@app.route('/')
def index():
    """主页 - 显示统计信息和待审核列表"""
    try:
        manager = get_sync_manager()
        
        # 获取统计信息
        stats = manager.get_sync_statistics()
        
        # 获取待审核变更
        pending_changes = manager.get_pending_changes(limit=20)
        
        return render_template('index.html', 
                             stats=stats, 
                             pending_changes=pending_changes)
    except Exception as e:
        logger.error(f"加载主页失败: {e}")
        return f"加载失败: {e}", 500

@app.route('/api/detect_changes', methods=['POST'])
def detect_changes():
    """API - 检测数据变更"""
    try:
        manager = get_sync_manager()
        changes = manager.detect_changes()
        return jsonify({
            'success': True,
            'data': changes
        })
    except Exception as e:
        logger.error(f"检测变更失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/pending_changes')
def get_pending_changes():
    """API - 获取待审核变更列表"""
    try:
        limit = request.args.get('limit', 50, type=int)
        manager = get_sync_manager()
        changes = manager.get_pending_changes(limit=limit)
        
        # 转换datetime对象为字符串
        for change in changes:
            if change.get('created_at'):
                change['created_at'] = change['created_at'].isoformat()
        
        return jsonify({
            'success': True,
            'data': changes
        })
    except Exception as e:
        logger.error(f"获取待审核变更失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/approve_changes', methods=['POST'])
def approve_changes():
    """API - 批准变更"""
    try:
        data = request.get_json()
        change_ids = data.get('change_ids', [])
        approved_by = data.get('approved_by', 'admin')
        
        if not change_ids:
            return jsonify({
                'success': False,
                'error': '请选择要批准的变更'
            }), 400
        
        manager = get_sync_manager()
        result = manager.approve_changes(change_ids, approved_by)
        
        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        logger.error(f"批准变更失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sync_approved', methods=['POST'])
def sync_approved_changes():
    """API - 同步已批准的变更到FastGPT"""
    try:
        manager = get_sync_manager()
        result = manager.sync_approved_changes()
        
        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        logger.error(f"同步变更失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/statistics')
def get_statistics():
    """API - 获取同步统计信息"""
    try:
        manager = get_sync_manager()
        stats = manager.get_sync_statistics()
        
        # 转换datetime对象为字符串
        if stats.get('last_sync_time'):
            stats['last_sync_time'] = stats['last_sync_time'].isoformat()
        
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/change/<int:change_id>')
def view_change_detail(change_id):
    """查看变更详情"""
    try:
        manager = get_sync_manager()
        
        # 获取变更详情
        with manager.conn.cursor() as cursor:
            cursor.execute("""
                SELECT psd.*, ss.sync_status, ss.fastgpt_data_id, ss.approved_by, ss.approved_at
                FROM pending_sync_data psd
                LEFT JOIN sync_status ss ON psd.source_type = ss.source_type 
                                          AND psd.source_id = ss.source_id
                WHERE psd.id = %s
            """, (change_id,))
            
            change = cursor.fetchone()
            
            if not change:
                return "变更不存在", 404
            
            # 转换为字典格式
            columns = [desc[0] for desc in cursor.description]
            change_dict = dict(zip(columns, change))
            
            # 解析metadata
            if change_dict.get('metadata'):
                change_dict['metadata'] = json.loads(change_dict['metadata'])
            
        return render_template('change_detail.html', change=change_dict)
        
    except Exception as e:
        logger.error(f"查看变更详情失败: {e}")
        return f"加载失败: {e}", 500

@app.route('/batch_review')
def batch_review():
    """批量审核页面"""
    try:
        manager = get_sync_manager()
        pending_changes = manager.get_pending_changes(limit=100)
        
        return render_template('batch_review.html', pending_changes=pending_changes)
        
    except Exception as e:
        logger.error(f"加载批量审核页面失败: {e}")
        return f"加载失败: {e}", 500

@app.route('/sync_logs')
def view_sync_logs():
    """查看同步日志"""
    try:
        manager = get_sync_manager()
        
        # 获取最近的同步日志
        with manager.conn.cursor() as cursor:
            cursor.execute("""
                SELECT * FROM sync_logs
                ORDER BY created_at DESC
                LIMIT 100
            """)
            
            logs = cursor.fetchall()
            
            # 转换为字典格式
            columns = [desc[0] for desc in cursor.description]
            logs_list = [dict(zip(columns, log)) for log in logs]
            
            # 解析details JSON
            for log in logs_list:
                if log.get('details'):
                    try:
                        log['details'] = json.loads(log['details'])
                    except:
                        pass
        
        return render_template('sync_logs.html', logs=logs_list)
        
    except Exception as e:
        logger.error(f"查看同步日志失败: {e}")
        return f"加载失败: {e}", 500

@app.template_filter('datetime_format')
def datetime_format(value):
    """日期时间格式化过滤器"""
    if value is None:
        return ''
    if isinstance(value, str):
        try:
            value = datetime.fromisoformat(value.replace('Z', '+00:00'))
        except:
            return value
    return value.strftime('%Y-%m-%d %H:%M:%S')

@app.template_filter('json_pretty')
def json_pretty(value):
    """JSON美化过滤器"""
    if isinstance(value, (dict, list)):
        return json.dumps(value, indent=2, ensure_ascii=False)
    return value

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)