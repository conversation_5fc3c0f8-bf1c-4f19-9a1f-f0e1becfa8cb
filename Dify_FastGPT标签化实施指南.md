# Dify/FastGPT标签化实施指南

## 概述

本指南详细说明如何将熵基产品知识库的属性数据转换为Dify和FastGPT平台的标签系统，以实现更精准的检索效果。

## 标签体系设计

### 1. 标签分类体系

#### 产品分类标签
```
分类:考勤产品
分类:门禁产品
分类:访客产品
分类:消费产品
分类:通道产品
```

#### 产品系列标签
```
系列:X系列
系列:ZK系列
系列:S系列
系列:F系列
系列:MA系列
```

#### 功能特征标签
```
功能:指纹识别
功能:人脸识别
功能:虹膜识别
功能:掌纹识别
功能:刷卡识别
功能:考勤管理
功能:门禁控制
功能:访客管理
功能:消费管理
功能:通道管理
```

#### 业务范畴标签
```
业务:企业考勤
业务:工厂管理
业务:学校管理
业务:医院管理
业务:政府机关
业务:商业场所
```

#### 文档类型标签
```
文档:技术规格
文档:安装手册
文档:用户手册
文档:故障排除
文档:软件说明
文档:API文档
文档:常见问题
```

#### 软件支持标签
```
软件:ZKTime
软件:ZKAccess
软件:ZKBioSecurity
软件:ADMS
软件:InBio
```

### 2. 标签提取规则

#### 自动标签提取
```python
class AutoTagExtractor:
    """自动标签提取器"""
    
    def __init__(self):
        # 功能关键词映射
        self.feature_keywords = {
            '指纹': '功能:指纹识别',
            '人脸': '功能:人脸识别',
            '面部': '功能:人脸识别',
            '虹膜': '功能:虹膜识别',
            '掌纹': '功能:掌纹识别',
            '刷卡': '功能:刷卡识别',
            'RFID': '功能:刷卡识别',
            '考勤': '功能:考勤管理',
            '门禁': '功能:门禁控制',
            '访客': '功能:访客管理',
            '消费': '功能:消费管理',
            '通道': '功能:通道管理'
        }
        
        # 业务场景关键词映射
        self.business_keywords = {
            '企业': '业务:企业考勤',
            '工厂': '业务:工厂管理',
            '学校': '业务:学校管理',
            '医院': '业务:医院管理',
            '政府': '业务:政府机关',
            '商场': '业务:商业场所',
            '酒店': '业务:商业场所'
        }
        
        # 文档类型关键词映射
        self.doc_type_keywords = {
            '技术参数': '文档:技术规格',
            '规格': '文档:技术规格',
            '安装': '文档:安装手册',
            '配置': '文档:安装手册',
            '用户': '文档:用户手册',
            '使用': '文档:用户手册',
            '故障': '文档:故障排除',
            '问题': '文档:故障排除',
            '软件': '文档:软件说明',
            'API': '文档:API文档',
            '接口': '文档:API文档',
            'FAQ': '文档:常见问题',
            '常见问题': '文档:常见问题'
        }
    
    def extract_from_product_name(self, product_name: str) -> List[str]:
        """从产品名称提取标签"""
        tags = []
        name_lower = product_name.lower()
        
        # 提取功能标签
        for keyword, tag in self.feature_keywords.items():
            if keyword in product_name:
                tags.append(tag)
        
        return tags
    
    def extract_from_document_content(self, content: str) -> List[str]:
        """从文档内容提取标签"""
        tags = []
        content_lower = content.lower()
        
        # 提取文档类型标签
        for keyword, tag in self.doc_type_keywords.items():
            if keyword in content:
                tags.append(tag)
                break  # 只取第一个匹配的类型
        
        # 提取业务场景标签
        for keyword, tag in self.business_keywords.items():
            if keyword in content:
                tags.append(tag)
        
        return tags
```

## Dify平台实施方案

### 1. 知识库配置

#### 元数据字段设置
```yaml
metadata_fields:
  - name: "product_model"
    type: "string"
    description: "产品型号"
    searchable: true
    filterable: true
    
  - name: "product_name"
    type: "string"
    description: "产品名称"
    searchable: true
    
  - name: "category_tags"
    type: "array"
    description: "分类标签"
    searchable: true
    filterable: true
    
  - name: "feature_tags"
    type: "array"
    description: "功能标签"
    searchable: true
    filterable: true
    
  - name: "business_tags"
    type: "array"
    description: "业务标签"
    searchable: true
    filterable: true
    
  - name: "document_tags"
    type: "array"
    description: "文档标签"
    searchable: true
    filterable: true
    
  - name: "software_tags"
    type: "array"
    description: "软件标签"
    searchable: true
    filterable: true
```

#### 检索过滤器配置
```python
class DifySearchOptimizer:
    """Dify检索优化器"""
    
    def build_search_filters(self, user_query: str) -> dict:
        """构建检索过滤器"""
        filters = {}
        
        # 产品型号过滤
        model = self.extract_product_model(user_query)
        if model:
            filters['product_model'] = {'$eq': model}
        
        # 功能标签过滤
        features = self.extract_features(user_query)
        if features:
            filters['feature_tags'] = {'$in': features}
        
        # 文档类型过滤
        doc_type = self.extract_document_type(user_query)
        if doc_type:
            filters['document_tags'] = {'$in': [doc_type]}
        
        return filters
    
    def extract_product_model(self, query: str) -> str:
        """提取产品型号"""
        import re
        # 匹配常见型号格式
        patterns = [
            r'[A-Z]\d+[A-Z]*',  # X628, S922等
            r'ZK[A-Z0-9]+',     # ZK系列
            r'MA[A-Z0-9]+',     # MA系列
        ]
        
        for pattern in patterns:
            match = re.search(pattern, query.upper())
            if match:
                return match.group()
        return None
    
    def extract_features(self, query: str) -> List[str]:
        """提取功能特征"""
        features = []
        feature_map = {
            '指纹': '功能:指纹识别',
            '人脸': '功能:人脸识别',
            '考勤': '功能:考勤管理',
            '门禁': '功能:门禁控制',
            '访客': '功能:访客管理'
        }
        
        for keyword, feature in feature_map.items():
            if keyword in query:
                features.append(feature)
        
        return features
    
    def extract_document_type(self, query: str) -> str:
        """提取文档类型"""
        doc_type_map = {
            '参数': '文档:技术规格',
            '规格': '文档:技术规格',
            '安装': '文档:安装手册',
            '配置': '文档:安装手册',
            '故障': '文档:故障排除',
            '问题': '文档:故障排除'
        }
        
        for keyword, doc_type in doc_type_map.items():
            if keyword in query:
                return doc_type
        
        return None
```

### 2. 工作流优化

#### 智能检索工作流
```yaml
workflow:
  name: "智能产品检索"
  description: "基于标签的智能产品信息检索"
  
  nodes:
    # 1. 查询分析节点
    - id: "query_analysis"
      type: "llm"
      name: "查询分析"
      config:
        model: "gpt-3.5-turbo"
        temperature: 0.1
        prompt: |
          分析用户查询，提取以下信息：
          1. 产品型号（如果提到）
          2. 产品功能（指纹、人脸、考勤、门禁等）
          3. 文档类型（技术参数、安装、故障等）
          4. 业务场景（企业、学校、工厂等）
          
          用户查询：{{#1711.query#}}
          
          请以JSON格式返回：
          {
            "product_model": "提取的型号或null",
            "features": ["功能列表"],
            "document_type": "文档类型或null",
            "business_scope": "业务场景或null"
          }
      
    # 2. 过滤器构建节点
    - id: "filter_builder"
      type: "code"
      name: "过滤器构建"
      config:
        code: |
          def main(query_analysis):
              import json
              
              analysis = json.loads(query_analysis)
              filters = {}
              
              # 构建过滤条件
              if analysis.get('product_model'):
                  filters['product_model'] = analysis['product_model']
              
              if analysis.get('features'):
                  feature_tags = [f"功能:{f}" for f in analysis['features']]
                  filters['feature_tags'] = feature_tags
              
              if analysis.get('document_type'):
                  filters['document_tags'] = [f"文档:{analysis['document_type']}"]
              
              return {
                  "filters": filters,
                  "enhanced_query": query_analysis.get('enhanced_query', '')
              }
    
    # 3. 知识库检索节点
    - id: "knowledge_search"
      type: "knowledge_retrieval"
      name: "知识库检索"
      config:
        dataset_ids: ["熵基产品知识库"]
        retrieval_mode: "semantic_search"
        top_k: 8
        score_threshold: 0.5
        rerank_model: "rerank-1"
        filters: "{{#filter_builder.filters#}}"
    
    # 4. 答案生成节点
    - id: "answer_generation"
      type: "llm"
      name: "答案生成"
      config:
        model: "gpt-3.5-turbo"
        temperature: 0.2
        prompt: |
          基于检索到的产品资料，为用户提供准确、专业的回答。
          
          用户问题：{{#1711.query#}}
          检索结果：{{#knowledge_search.result#}}
          
          回答要求：
          1. 如果涉及技术参数，请保持数据准确性
          2. 如果是故障排除，请提供具体步骤
          3. 如果是安装配置，请按顺序说明
          4. 如果信息不足，请明确说明
          
          请提供专业、详细的回答：
```

## FastGPT平台实施方案

### 1. 知识库配置

#### 数据集配置
```json
{
  "dataset_config": {
    "name": "熵基产品知识库",
    "intro": "熵基科技产品技术资料知识库",
    "avatar": "/logo/zkteco.png",
    
    "chunk_config": {
      "chunk_size": 800,
      "chunk_overlap": 100,
      "separator": "\n\n",
      "custom_separator": ["##", "###", "技术参数", "安装步骤"]
    },
    
    "metadata_config": {
      "auto_extract": true,
      "extract_rules": [
        {
          "field": "product_model",
          "regex": "[A-Z]\\d+[A-Z]*|ZK[A-Z0-9]+|MA[A-Z0-9]+",
          "description": "产品型号自动提取"
        },
        {
          "field": "category",
          "keywords": ["考勤", "门禁", "访客", "消费", "通道"],
          "description": "产品分类自动识别"
        }
      ]
    },
    
    "qa_config": {
      "auto_generate": true,
      "qa_prompt": "基于以下产品资料内容，生成3-5个用户可能询问的问题和对应的专业回答：\n\n{{chunk_content}}",
      "max_qa_pairs": 5
    }
  }
}
```

#### 检索配置优化
```json
{
  "search_config": {
    "similarity_threshold": 0.4,
    "max_tokens": 3000,
    "search_mode": "embedding",
    "rerank_model": "bge-reranker-large",
    
    "filter_config": {
      "enable_filters": true,
      "filter_fields": [
        "product_model",
        "category", 
        "feature_tags",
        "document_type"
      ]
    },
    
    "boost_config": {
      "title_boost": 1.5,
      "keyword_boost": 1.3,
      "recent_boost": 1.2
    }
  }
}
```

### 2. 应用配置

#### 技术支持助手配置
```json
{
  "app_config": {
    "name": "熵基技术支持助手",
    "intro": "专业的熵基产品技术支持，提供产品咨询、技术参数查询、故障排除等服务",
    "avatar": "/avatar/tech_support.png",
    
    "welcome_config": {
      "welcome_text": "您好！我是熵基技术支持助手。\n\n我可以帮您：\n• 查询产品技术参数\n• 解答安装配置问题\n• 协助故障排除\n• 提供软件使用指导\n\n请告诉我您需要了解的产品型号或遇到的问题。",
      "variables": [
        {
          "key": "product_model",
          "label": "产品型号",
          "type": "string",
          "placeholder": "如：X628、ZK4500等",
          "required": false
        },
        {
          "key": "issue_category",
          "label": "问题类型",
          "type": "select",
          "options": [
            {"label": "技术参数查询", "value": "tech_spec"},
            {"label": "安装配置", "value": "installation"},
            {"label": "故障排除", "value": "troubleshooting"},
            {"label": "软件使用", "value": "software"},
            {"label": "其他问题", "value": "other"}
          ],
          "required": false
        }
      ]
    },
    
    "chat_config": {
      "system_prompt": "你是熵基科技的专业技术支持助手。请基于知识库内容提供准确、专业的技术支持。\n\n回答要求：\n1. 保持技术参数的准确性\n2. 提供具体的操作步骤\n3. 使用专业但易懂的语言\n4. 如果信息不足，明确告知并建议联系技术支持\n5. 优先使用产品型号进行精确匹配",
      "temperature": 0.1,
      "max_tokens": 2000,
      "top_p": 0.9
    }
  }
}
```

## 标签化数据处理脚本

### 1. 数据预处理脚本

```python
import pandas as pd
import json
import re
from typing import List, Dict

class ProductDataProcessor:
    """产品数据标签化处理器"""
    
    def __init__(self):
        self.tag_extractor = AutoTagExtractor()
    
    def process_excel_data(self, excel_file: str) -> List[Dict]:
        """处理Excel数据并生成标签"""
        df = pd.read_excel(excel_file)
        processed_data = []
        
        for _, row in df.iterrows():
            # 基础信息
            product_data = {
                'fastgpt_id': row.get('id-fastgpt', ''),
                'document_name': row.get('name', ''),
                'product_model': row.get('产品型号', ''),
                'product_name': row.get('产品名称', ''),
                'material_usage': row.get('资料用途', ''),
                'business_scope': row.get('产品业务范畴', ''),
                'supported_software': row.get('产品所支持的软件', '')
            }
            
            # 生成标签
            tags = self.generate_tags(product_data)
            product_data['tags'] = tags
            
            # 生成元数据
            metadata = self.generate_metadata(product_data)
            product_data['metadata'] = metadata
            
            processed_data.append(product_data)
        
        return processed_data
    
    def generate_tags(self, product_data: Dict) -> List[str]:
        """生成产品标签"""
        all_tags = []
        
        # 分类标签
        if product_data.get('business_scope'):
            all_tags.append(f"业务:{product_data['business_scope']}")
        
        # 功能标签（从产品名称提取）
        if product_data.get('product_name'):
            feature_tags = self.tag_extractor.extract_from_product_name(
                product_data['product_name']
            )
            all_tags.extend(feature_tags)
        
        # 用途标签
        if product_data.get('material_usage'):
            all_tags.append(f"用途:{product_data['material_usage']}")
        
        # 软件标签
        if product_data.get('supported_software'):
            software_list = [s.strip() for s in product_data['supported_software'].split('，')]
            for software in software_list:
                if software:
                    all_tags.append(f"软件:{software}")
        
        return list(set(all_tags))  # 去重
    
    def generate_metadata(self, product_data: Dict) -> Dict:
        """生成FastGPT/Dify元数据"""
        metadata = {
            'product_model': product_data.get('product_model', ''),
            'product_name': product_data.get('product_name', ''),
            'business_scope': product_data.get('business_scope', ''),
            'material_usage': product_data.get('material_usage', ''),
            'tags': product_data.get('tags', []),
            'source': 'excel_import',
            'last_updated': pd.Timestamp.now().isoformat()
        }
        
        return metadata
    
    def export_for_dify(self, processed_data: List[Dict], output_file: str):
        """导出Dify格式数据"""
        dify_data = []
        
        for item in processed_data:
            dify_item = {
                'content': f"产品名称：{item['product_name']}\n产品型号：{item['product_model']}\n业务范畴：{item['business_scope']}\n资料用途：{item['material_usage']}\n支持软件：{item['supported_software']}",
                'metadata': item['metadata']
            }
            dify_data.append(dify_item)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(dify_data, f, ensure_ascii=False, indent=2)
    
    def export_for_fastgpt(self, processed_data: List[Dict], output_file: str):
        """导出FastGPT格式数据"""
        fastgpt_data = {
            'dataset_name': '熵基产品知识库',
            'chunks': []
        }
        
        for item in processed_data:
            chunk = {
                'content': f"# {item['product_name']} ({item['product_model']})\n\n**业务范畴：** {item['business_scope']}\n**资料用途：** {item['material_usage']}\n**支持软件：** {item['supported_software']}",
                'metadata': item['metadata']
            }
            fastgpt_data['chunks'].append(chunk)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(fastgpt_data, f, ensure_ascii=False, indent=2)

# 使用示例
if __name__ == "__main__":
    processor = ProductDataProcessor()
    
    # 处理Excel数据
    processed_data = processor.process_excel_data('allcollections(1).xlsx')
    
    # 导出Dify格式
    processor.export_for_dify(processed_data, 'dify_knowledge_base.json')
    
    # 导出FastGPT格式
    processor.export_for_fastgpt(processed_data, 'fastgpt_knowledge_base.json')
    
    print(f"处理完成，共处理 {len(processed_data)} 条记录")
```

### 2. 标签验证脚本

```python
class TagValidator:
    """标签验证器"""
    
    def __init__(self):
        self.valid_categories = ['考勤产品', '门禁产品', '访客产品', '消费产品']
        self.valid_features = ['指纹识别', '人脸识别', '考勤管理', '门禁控制', '访客管理']
        self.valid_doc_types = ['技术规格', '安装手册', '用户手册', '故障排除']
    
    def validate_tags(self, tags: List[str]) -> Dict:
        """验证标签有效性"""
        validation_result = {
            'valid_tags': [],
            'invalid_tags': [],
            'suggestions': []
        }
        
        for tag in tags:
            if self.is_valid_tag(tag):
                validation_result['valid_tags'].append(tag)
            else:
                validation_result['invalid_tags'].append(tag)
                suggestion = self.suggest_correction(tag)
                if suggestion:
                    validation_result['suggestions'].append({
                        'original': tag,
                        'suggested': suggestion
                    })
        
        return validation_result
    
    def is_valid_tag(self, tag: str) -> bool:
        """检查标签是否有效"""
        if tag.startswith('分类:'):
            category = tag.replace('分类:', '')
            return category in self.valid_categories
        elif tag.startswith('功能:'):
            feature = tag.replace('功能:', '')
            return feature in self.valid_features
        elif tag.startswith('文档:'):
            doc_type = tag.replace('文档:', '')
            return doc_type in self.valid_doc_types
        else:
            return True  # 其他类型标签暂时认为有效
    
    def suggest_correction(self, tag: str) -> str:
        """建议标签修正"""
        # 简单的相似度匹配建议
        if tag.startswith('分类:'):
            category = tag.replace('分类:', '')
            for valid_cat in self.valid_categories:
                if category in valid_cat or valid_cat in category:
                    return f'分类:{valid_cat}'
        
        return None
```

## 实施步骤

### 阶段1：数据准备（1-2天）
1. 运行数据预处理脚本
2. 生成标签化数据
3. 验证标签质量
4. 准备导入文件

### 阶段2：平台配置（2-3天）
1. 配置Dify/FastGPT知识库
2. 设置元数据字段
3. 配置检索过滤器
4. 测试基础功能

### 阶段3：数据导入（1-2天）
1. 批量导入标签化数据
2. 验证导入结果
3. 测试检索效果
4. 调优参数设置

### 阶段4：效果优化（持续）
1. 监控检索准确率
2. 收集用户反馈
3. 优化标签体系
4. 持续改进检索策略

## 预期效果

### 检索精准度提升
- **产品型号查询**：准确率提升至95%+
- **功能特征检索**：相关性提升60%+
- **文档类型过滤**：检索效率提升50%+

### 用户体验改善
- **响应速度**：平均检索时间<2秒
- **答案质量**：专业准确度提升70%+
- **使用便利性**：支持自然语言查询

### 维护效率提升
- **数据更新**：自动化标签生成
- **质量控制**：标签验证机制
- **扩展性**：支持新产品快速接入

通过标签化改造，熵基产品知识库将在Dify/FastGPT平台上实现更精准、更智能的检索体验。