# -*- coding: utf-8 -*-
"""
FastGPT知识库同步管理器
实现多数据源到FastGPT知识库的增量更新
支持管理员审核确认机制
"""

import logging
import json
import hashlib
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import psycopg2
from psycopg2.extras import RealDictCursor
import os
from config import DATABASE_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FastGPTSyncManager:
    """
    FastGPT知识库同步管理器
    负责管理多数据源到FastGPT知识库的增量更新
    """
    
    def __init__(self, fastgpt_config: Dict):
        """
        初始化同步管理器
        
        Args:
            fastgpt_config: FastGPT配置信息
                {
                    'api_url': 'FastGPT API地址',
                    'api_key': 'API密钥',
                    'dataset_id': '知识库ID',
                    'collection_id': '集合ID（可选）'
                }
        """
        self.fastgpt_config = fastgpt_config
        self.db_config = DATABASE_CONFIG
        self.conn = None
        
        # 初始化数据库连接
        self._connect_database()
        
        # 创建同步相关表
        self._initialize_sync_tables()
    
    def _connect_database(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def _initialize_sync_tables(self):
        """初始化同步相关的数据库表"""
        try:
            with self.conn.cursor() as cursor:
                # 创建同步状态表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS sync_status (
                        id SERIAL PRIMARY KEY,
                        source_type VARCHAR(50) NOT NULL,  -- 数据源类型：yunshang_api, local_files, legacy
                        source_id VARCHAR(255) NOT NULL,   -- 源数据ID（产品ID或文档ID）
                        content_hash VARCHAR(64) NOT NULL, -- 内容哈希值
                        sync_status VARCHAR(20) DEFAULT 'pending', -- 同步状态：pending, approved, synced, failed
                        fastgpt_data_id VARCHAR(255),      -- FastGPT中的数据ID
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        approved_at TIMESTAMP,
                        approved_by VARCHAR(100),
                        sync_error TEXT,
                        UNIQUE(source_type, source_id)
                    )
                """)
                
                # 创建待同步数据表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS pending_sync_data (
                        id SERIAL PRIMARY KEY,
                        source_type VARCHAR(50) NOT NULL,
                        source_id VARCHAR(255) NOT NULL,
                        title VARCHAR(500) NOT NULL,       -- 数据标题
                        content TEXT NOT NULL,             -- 数据内容
                        metadata JSONB,                    -- 元数据（产品信息、文件信息等）
                        content_hash VARCHAR(64) NOT NULL,
                        change_type VARCHAR(20) NOT NULL,  -- 变更类型：create, update, delete
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(source_type, source_id)
                    )
                """)
                
                # 创建同步日志表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS sync_logs (
                        id SERIAL PRIMARY KEY,
                        source_type VARCHAR(50) NOT NULL,
                        source_id VARCHAR(255) NOT NULL,
                        action VARCHAR(50) NOT NULL,       -- 操作类型：detect_change, approve, sync, error
                        details JSONB,                     -- 详细信息
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_sync_status_source ON sync_status(source_type, source_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_pending_sync_source ON pending_sync_data(source_type, source_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_sync_logs_source ON sync_logs(source_type, source_id)")
                
                self.conn.commit()
                logger.info("同步表初始化完成")
                
        except Exception as e:
            logger.error(f"初始化同步表失败: {e}")
            self.conn.rollback()
            raise
    
    def detect_changes(self) -> Dict[str, int]:
        """
        检测所有数据源的变更
        
        Returns:
            变更统计信息
        """
        stats = {
            'new_items': 0,
            'updated_items': 0,
            'deleted_items': 0
        }
        
        # 检测云商API数据变更
        stats_yunshang = self._detect_yunshang_changes()
        
        # 检测本地文件变更
        stats_local = self._detect_local_file_changes()
        
        # 检测历史数据变更
        stats_legacy = self._detect_legacy_changes()
        
        # 合并统计
        for key in stats:
            stats[key] = stats_yunshang.get(key, 0) + stats_local.get(key, 0) + stats_legacy.get(key, 0)
        
        logger.info(f"变更检测完成: {stats}")
        return stats
    
    def _detect_yunshang_changes(self) -> Dict[str, int]:
        """检测云商API数据变更"""
        stats = {'new_items': 0, 'updated_items': 0, 'deleted_items': 0}
        
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # 获取所有云商产品数据
                cursor.execute("""
                    SELECT p.product_id, p.name, p.model, p.category_name,
                           pd.document_id, pd.file_path, pd.file_name, pd.file_size,
                           pd.file_format, pd.file_hash, pd.download_url
                    FROM Product p
                    LEFT JOIN ProductDocument pd ON p.product_id = pd.product_id
                    WHERE p.data_source = 'yunshang_api'
                    ORDER BY p.product_id, pd.document_id
                """)
                
                products = cursor.fetchall()
                
                # 按产品分组处理
                current_product = None
                product_docs = []
                
                for row in products:
                    if current_product != row['product_id']:
                        if current_product is not None:
                            # 处理上一个产品
                            change_stats = self._process_product_changes(
                                'yunshang_api', current_product, product_docs
                            )
                            for key in stats:
                                stats[key] += change_stats.get(key, 0)
                        
                        current_product = row['product_id']
                        product_docs = []
                    
                    if row['document_id']:
                        product_docs.append(row)
                
                # 处理最后一个产品
                if current_product is not None:
                    change_stats = self._process_product_changes(
                        'yunshang_api', current_product, product_docs
                    )
                    for key in stats:
                        stats[key] += change_stats.get(key, 0)
                        
        except Exception as e:
            logger.error(f"检测云商数据变更失败: {e}")
        
        return stats
    
    def _detect_local_file_changes(self) -> Dict[str, int]:
        """检测本地文件数据变更"""
        stats = {'new_items': 0, 'updated_items': 0, 'deleted_items': 0}
        
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # 获取所有本地文件数据
                cursor.execute("""
                    SELECT p.product_id, p.name, p.model, p.category_name,
                           pd.document_id, pd.file_path, pd.file_name, pd.file_size,
                           pd.file_format, pd.file_hash
                    FROM Product p
                    LEFT JOIN ProductDocument pd ON p.product_id = pd.product_id
                    WHERE p.data_source = 'local'
                    ORDER BY p.product_id, pd.document_id
                """)
                
                products = cursor.fetchall()
                
                # 按产品分组处理
                current_product = None
                product_docs = []
                
                for row in products:
                    if current_product != row['product_id']:
                        if current_product is not None:
                            # 处理上一个产品
                            change_stats = self._process_product_changes(
                                'local_files', current_product, product_docs
                            )
                            for key in stats:
                                stats[key] += change_stats.get(key, 0)
                        
                        current_product = row['product_id']
                        product_docs = []
                    
                    if row['document_id']:
                        product_docs.append(row)
                
                # 处理最后一个产品
                if current_product is not None:
                    change_stats = self._process_product_changes(
                        'local_files', current_product, product_docs
                    )
                    for key in stats:
                        stats[key] += change_stats.get(key, 0)
                        
        except Exception as e:
            logger.error(f"检测本地文件变更失败: {e}")
        
        return stats
    
    def _detect_legacy_changes(self) -> Dict[str, int]:
        """检测历史数据变更"""
        stats = {'new_items': 0, 'updated_items': 0, 'deleted_items': 0}
        
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # 获取所有历史数据
                cursor.execute("""
                    SELECT p.product_id, p.name, p.model, p.category_name,
                           pd.document_id, pd.file_path, pd.file_name, pd.file_size,
                           pd.file_format, pd.file_hash
                    FROM Product p
                    LEFT JOIN ProductDocument pd ON p.product_id = pd.product_id
                    WHERE p.data_source = 'legacy'
                    ORDER BY p.product_id, pd.document_id
                """)
                
                products = cursor.fetchall()
                
                # 按产品分组处理
                current_product = None
                product_docs = []
                
                for row in products:
                    if current_product != row['product_id']:
                        if current_product is not None:
                            # 处理上一个产品
                            change_stats = self._process_product_changes(
                                'legacy', current_product, product_docs
                            )
                            for key in stats:
                                stats[key] += change_stats.get(key, 0)
                        
                        current_product = row['product_id']
                        product_docs = []
                    
                    if row['document_id']:
                        product_docs.append(row)
                
                # 处理最后一个产品
                if current_product is not None:
                    change_stats = self._process_product_changes(
                        'legacy', current_product, product_docs
                    )
                    for key in stats:
                        stats[key] += change_stats.get(key, 0)
                        
        except Exception as e:
            logger.error(f"检测历史数据变更失败: {e}")
        
        return stats
    
    def _process_product_changes(self, source_type: str, product_id: str, docs: List[Dict]) -> Dict[str, int]:
        """
        处理单个产品的变更检测
        
        Args:
            source_type: 数据源类型
            product_id: 产品ID
            docs: 产品文档列表
        
        Returns:
            变更统计
        """
        stats = {'new_items': 0, 'updated_items': 0, 'deleted_items': 0}
        
        try:
            # 生成产品内容
            content = self._generate_product_content(product_id, docs)
            content_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()
            
            # 检查是否已存在
            with self.conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute("""
                    SELECT content_hash, sync_status FROM sync_status
                    WHERE source_type = %s AND source_id = %s
                """, (source_type, product_id))
                
                existing = cursor.fetchone()
                
                if existing is None:
                    # 新增数据
                    self._add_pending_sync_data(
                        source_type, product_id, content, content_hash, 'create', docs
                    )
                    stats['new_items'] += 1
                    
                elif existing['content_hash'] != content_hash:
                    # 内容有变更
                    self._add_pending_sync_data(
                        source_type, product_id, content, content_hash, 'update', docs
                    )
                    stats['updated_items'] += 1
                    
        except Exception as e:
            logger.error(f"处理产品变更失败 {product_id}: {e}")
        
        return stats
    
    def _generate_product_content(self, product_id: str, docs: List[Dict]) -> str:
        """
        生成产品的知识库内容
        
        Args:
            product_id: 产品ID
            docs: 文档列表
        
        Returns:
            格式化的内容字符串
        """
        if not docs:
            return f"产品ID: {product_id}\n暂无相关文档"
        
        # 获取产品基本信息
        first_doc = docs[0]
        product_name = first_doc.get('name', '未知产品')
        product_model = first_doc.get('model', '未知型号')
        category_name = first_doc.get('category_name', '未知分类')
        
        content_parts = [
            f"产品名称: {product_name}",
            f"产品型号: {product_model}",
            f"产品分类: {category_name}",
            f"产品ID: {product_id}",
            "",
            "相关文档:"
        ]
        
        # 添加文档信息
        for doc in docs:
            if doc.get('document_id'):
                doc_info = [
                    f"- 文档名称: {doc.get('file_name', '未知文档')}",
                    f"  文档格式: {doc.get('file_format', '未知格式')}",
                    f"  文档大小: {doc.get('file_size', 0)} 字节"
                ]
                
                if doc.get('download_url'):
                    doc_info.append(f"  下载链接: {doc['download_url']}")
                elif doc.get('file_path'):
                    doc_info.append(f"  文件路径: {doc['file_path']}")
                
                content_parts.extend(doc_info)
                content_parts.append("")
        
        return "\n".join(content_parts)
    
    def _add_pending_sync_data(self, source_type: str, source_id: str, content: str, 
                              content_hash: str, change_type: str, docs: List[Dict]):
        """
        添加待同步数据
        
        Args:
            source_type: 数据源类型
            source_id: 源数据ID
            content: 内容
            content_hash: 内容哈希
            change_type: 变更类型
            docs: 文档列表
        """
        try:
            # 生成标题
            if docs:
                title = f"{docs[0].get('name', '未知产品')} - {docs[0].get('model', '未知型号')}"
            else:
                title = f"产品 {source_id}"
            
            # 生成元数据
            metadata = {
                'product_id': source_id,
                'source_type': source_type,
                'document_count': len(docs),
                'documents': docs
            }
            
            with self.conn.cursor() as cursor:
                # 插入或更新待同步数据
                cursor.execute("""
                    INSERT INTO pending_sync_data 
                    (source_type, source_id, title, content, metadata, content_hash, change_type)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (source_type, source_id) 
                    DO UPDATE SET 
                        title = EXCLUDED.title,
                        content = EXCLUDED.content,
                        metadata = EXCLUDED.metadata,
                        content_hash = EXCLUDED.content_hash,
                        change_type = EXCLUDED.change_type,
                        created_at = CURRENT_TIMESTAMP
                """, (source_type, source_id, title, content, json.dumps(metadata), content_hash, change_type))
                
                # 记录日志
                cursor.execute("""
                    INSERT INTO sync_logs (source_type, source_id, action, details)
                    VALUES (%s, %s, %s, %s)
                """, (source_type, source_id, 'detect_change', json.dumps({
                    'change_type': change_type,
                    'content_hash': content_hash,
                    'document_count': len(docs)
                })))
                
                self.conn.commit()
                
        except Exception as e:
            logger.error(f"添加待同步数据失败: {e}")
            self.conn.rollback()
            raise
    
    def get_pending_changes(self, limit: int = 50) -> List[Dict]:
        """
        获取待审核的变更列表
        
        Args:
            limit: 返回数量限制
        
        Returns:
            待审核变更列表
        """
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute("""
                    SELECT psd.*, ss.sync_status, ss.fastgpt_data_id
                    FROM pending_sync_data psd
                    LEFT JOIN sync_status ss ON psd.source_type = ss.source_type 
                                              AND psd.source_id = ss.source_id
                    WHERE ss.sync_status IS NULL OR ss.sync_status = 'pending'
                    ORDER BY psd.created_at DESC
                    LIMIT %s
                """, (limit,))
                
                return cursor.fetchall()
                
        except Exception as e:
            logger.error(f"获取待审核变更失败: {e}")
            return []
    
    def approve_changes(self, change_ids: List[int], approved_by: str) -> Dict[str, int]:
        """
        批准变更
        
        Args:
            change_ids: 变更ID列表
            approved_by: 审批人
        
        Returns:
            操作结果统计
        """
        stats = {'approved': 0, 'failed': 0}
        
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cursor:
                for change_id in change_ids:
                    try:
                        # 获取变更详情
                        cursor.execute("""
                            SELECT * FROM pending_sync_data WHERE id = %s
                        """, (change_id,))
                        
                        change = cursor.fetchone()
                        if not change:
                            continue
                        
                        # 更新或插入同步状态
                        cursor.execute("""
                            INSERT INTO sync_status 
                            (source_type, source_id, content_hash, sync_status, approved_at, approved_by)
                            VALUES (%s, %s, %s, %s, %s, %s)
                            ON CONFLICT (source_type, source_id)
                            DO UPDATE SET
                                content_hash = EXCLUDED.content_hash,
                                sync_status = 'approved',
                                approved_at = EXCLUDED.approved_at,
                                approved_by = EXCLUDED.approved_by,
                                updated_at = CURRENT_TIMESTAMP
                        """, (change['source_type'], change['source_id'], change['content_hash'], 
                              'approved', datetime.now(), approved_by))
                        
                        # 记录日志
                        cursor.execute("""
                            INSERT INTO sync_logs (source_type, source_id, action, details)
                            VALUES (%s, %s, %s, %s)
                        """, (change['source_type'], change['source_id'], 'approve', json.dumps({
                            'approved_by': approved_by,
                            'change_id': change_id,
                            'change_type': change['change_type']
                        })))
                        
                        stats['approved'] += 1
                        
                    except Exception as e:
                        logger.error(f"批准变更失败 {change_id}: {e}")
                        stats['failed'] += 1
                
                self.conn.commit()
                
        except Exception as e:
            logger.error(f"批准变更操作失败: {e}")
            self.conn.rollback()
        
        return stats
    
    def sync_approved_changes(self) -> Dict[str, int]:
        """
        同步已批准的变更到FastGPT
        
        Returns:
            同步结果统计
        """
        stats = {'synced': 0, 'failed': 0}
        
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # 获取已批准但未同步的变更
                cursor.execute("""
                    SELECT psd.*, ss.fastgpt_data_id
                    FROM pending_sync_data psd
                    JOIN sync_status ss ON psd.source_type = ss.source_type 
                                        AND psd.source_id = ss.source_id
                    WHERE ss.sync_status = 'approved'
                    ORDER BY psd.created_at
                """)
                
                changes = cursor.fetchall()
                
                for change in changes:
                    try:
                        if change['change_type'] == 'delete':
                            # 删除数据
                            success = self._delete_fastgpt_data(change['fastgpt_data_id'])
                        else:
                            # 创建或更新数据
                            fastgpt_data_id = self._sync_to_fastgpt(change)
                            success = fastgpt_data_id is not None
                            
                            if success:
                                # 更新FastGPT数据ID
                                cursor.execute("""
                                    UPDATE sync_status 
                                    SET fastgpt_data_id = %s, sync_status = 'synced', updated_at = CURRENT_TIMESTAMP
                                    WHERE source_type = %s AND source_id = %s
                                """, (fastgpt_data_id, change['source_type'], change['source_id']))
                        
                        if success:
                            # 更新同步状态
                            cursor.execute("""
                                UPDATE sync_status 
                                SET sync_status = 'synced', updated_at = CURRENT_TIMESTAMP
                                WHERE source_type = %s AND source_id = %s
                            """, (change['source_type'], change['source_id']))
                            
                            # 记录成功日志
                            cursor.execute("""
                                INSERT INTO sync_logs (source_type, source_id, action, details)
                                VALUES (%s, %s, %s, %s)
                            """, (change['source_type'], change['source_id'], 'sync', json.dumps({
                                'success': True,
                                'change_type': change['change_type'],
                                'fastgpt_data_id': fastgpt_data_id if change['change_type'] != 'delete' else change['fastgpt_data_id']
                            })))
                            
                            stats['synced'] += 1
                        else:
                            raise Exception("同步到FastGPT失败")
                            
                    except Exception as e:
                        logger.error(f"同步变更失败 {change['source_id']}: {e}")
                        
                        # 更新错误状态
                        cursor.execute("""
                            UPDATE sync_status 
                            SET sync_status = 'failed', sync_error = %s, updated_at = CURRENT_TIMESTAMP
                            WHERE source_type = %s AND source_id = %s
                        """, (str(e), change['source_type'], change['source_id']))
                        
                        # 记录错误日志
                        cursor.execute("""
                            INSERT INTO sync_logs (source_type, source_id, action, details)
                            VALUES (%s, %s, %s, %s)
                        """, (change['source_type'], change['source_id'], 'error', json.dumps({
                            'error': str(e),
                            'change_type': change['change_type']
                        })))
                        
                        stats['failed'] += 1
                
                self.conn.commit()
                
        except Exception as e:
            logger.error(f"同步操作失败: {e}")
            self.conn.rollback()
        
        return stats
    
    def _sync_to_fastgpt(self, change: Dict) -> Optional[str]:
        """
        同步数据到FastGPT
        
        Args:
            change: 变更数据
        
        Returns:
            FastGPT数据ID，失败返回None
        """
        try:
            # 准备请求数据
            data = {
                'collectionId': self.fastgpt_config.get('collection_id'),
                'trainingType': 'chunk',
                'data': [{
                    'q': change['title'],
                    'a': change['content']
                }]
            }
            
            # 如果有billId配置，添加到请求中
            if self.fastgpt_config.get('bill_id'):
                data['billId'] = self.fastgpt_config['bill_id']
            
            # 发送请求
            headers = {
                'Authorization': f"Bearer {self.fastgpt_config['api_key']}",
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                f"{self.fastgpt_config['api_url']}/api/core/dataset/data/pushData",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 200:
                    # 返回一个标识符（这里使用源ID作为标识）
                    return f"{change['source_type']}_{change['source_id']}"
                else:
                    logger.error(f"FastGPT API返回错误: {result}")
                    return None
            else:
                logger.error(f"FastGPT API请求失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"同步到FastGPT失败: {e}")
            return None
    
    def _delete_fastgpt_data(self, fastgpt_data_id: str) -> bool:
        """
        从FastGPT删除数据
        
        Args:
            fastgpt_data_id: FastGPT数据ID
        
        Returns:
            是否成功
        """
        try:
            # 注意：这里需要根据实际的FastGPT删除API来实现
            # 当前FastGPT文档中没有提供批量删除API，可能需要逐个删除
            logger.warning(f"删除FastGPT数据功能待实现: {fastgpt_data_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除FastGPT数据失败: {e}")
            return False
    
    def get_sync_statistics(self) -> Dict:
        """
        获取同步统计信息
        
        Returns:
            统计信息
        """
        try:
            with self.conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # 获取各状态的数量
                cursor.execute("""
                    SELECT 
                        source_type,
                        sync_status,
                        COUNT(*) as count
                    FROM sync_status
                    GROUP BY source_type, sync_status
                """)
                
                status_stats = cursor.fetchall()
                
                # 获取待审核数量
                cursor.execute("""
                    SELECT COUNT(*) as pending_count
                    FROM pending_sync_data psd
                    LEFT JOIN sync_status ss ON psd.source_type = ss.source_type 
                                              AND psd.source_id = ss.source_id
                    WHERE ss.sync_status IS NULL OR ss.sync_status = 'pending'
                """)
                
                pending_result = cursor.fetchone()
                
                # 获取最近同步时间
                cursor.execute("""
                    SELECT MAX(updated_at) as last_sync_time
                    FROM sync_status
                    WHERE sync_status = 'synced'
                """)
                
                last_sync_result = cursor.fetchone()
                
                return {
                    'status_breakdown': status_stats,
                    'pending_approval_count': pending_result['pending_count'] if pending_result else 0,
                    'last_sync_time': last_sync_result['last_sync_time'] if last_sync_result else None
                }
                
        except Exception as e:
            logger.error(f"获取同步统计失败: {e}")
            return {}
    
    def cleanup_old_logs(self, days: int = 30):
        """
        清理旧的同步日志
        
        Args:
            days: 保留天数
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with self.conn.cursor() as cursor:
                cursor.execute("""
                    DELETE FROM sync_logs 
                    WHERE created_at < %s
                """, (cutoff_date,))
                
                deleted_count = cursor.rowcount
                self.conn.commit()
                
                logger.info(f"清理了 {deleted_count} 条旧日志记录")
                
        except Exception as e:
            logger.error(f"清理旧日志失败: {e}")
            self.conn.rollback()
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            logger.info("数据库连接已关闭")


def main():
    """主函数示例"""
    # FastGPT配置
    fastgpt_config = {
        'api_url': 'http://localhost:3000',  # 请修改为实际的FastGPT地址
        'api_key': 'your_fastgpt_api_key',   # 请修改为实际的API密钥
        'dataset_id': 'your_dataset_id',     # 请修改为实际的知识库ID
        'collection_id': 'your_collection_id', # 请修改为实际的集合ID
        'bill_id': None  # 可选的账单ID
    }
    
    # 创建同步管理器
    sync_manager = FastGPTSyncManager(fastgpt_config)
    
    try:
        # 检测变更
        print("检测数据变更...")
        changes = sync_manager.detect_changes()
        print(f"检测到变更: {changes}")
        
        # 获取待审核变更
        print("\n获取待审核变更...")
        pending_changes = sync_manager.get_pending_changes()
        print(f"待审核变更数量: {len(pending_changes)}")
        
        # 显示前5个待审核变更
        for i, change in enumerate(pending_changes[:5]):
            print(f"\n变更 {i+1}:")
            print(f"  ID: {change['id']}")
            print(f"  数据源: {change['source_type']}")
            print(f"  标题: {change['title']}")
            print(f"  变更类型: {change['change_type']}")
            print(f"  创建时间: {change['created_at']}")
        
        # 获取统计信息
        print("\n同步统计信息:")
        stats = sync_manager.get_sync_statistics()
        print(json.dumps(stats, indent=2, default=str))
        
    finally:
        sync_manager.close()


if __name__ == '__main__':
    main()