<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>变更详情 - FastGPT知识库同步管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css" rel="stylesheet">
    <style>
        .metadata-table th {
            width: 150px;
            background-color: #f8f9fa;
        }
        .content-section {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        .diff-added {
            background-color: #d4edda;
            color: #155724;
        }
        .diff-removed {
            background-color: #f8d7da;
            color: #721c24;
        }
        .json-viewer {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .back-button {
            position: sticky;
            top: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-database"></i> FastGPT知识库同步管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="bi bi-house"></i> 首页
                </a>
                <a class="nav-link" href="/batch_review">
                    <i class="bi bi-check2-square"></i> 批量审核
                </a>
                <a class="nav-link" href="/sync_logs">
                    <i class="bi bi-journal-text"></i> 同步日志
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 返回按钮 -->
        <div class="back-button mb-3">
            <a href="/" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
        </div>

        <!-- 变更基本信息 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i> 变更详情
                </h5>
                <div>
                    {% if change.sync_status == 'pending_approval' %}
                        <button class="btn btn-success btn-sm" onclick="approveChange({{ change.id }})">
                            <i class="bi bi-check"></i> 批准变更
                        </button>
                    {% endif %}
                    {% if change.sync_status == 'approved' %}
                        <span class="badge bg-success">已批准</span>
                    {% elif change.sync_status == 'synced' %}
                        <span class="badge bg-info">已同步</span>
                    {% elif change.sync_status == 'failed' %}
                        <span class="badge bg-danger">同步失败</span>
                    {% else %}
                        <span class="badge bg-warning">待审核</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <table class="table table-borderless metadata-table">
                    <tr>
                        <th>变更ID</th>
                        <td>{{ change.id }}</td>
                    </tr>
                    <tr>
                        <th>数据源类型</th>
                        <td>
                            <span class="badge bg-secondary">{{ change.source_type }}</span>
                        </td>
                    </tr>
                    <tr>
                        <th>变更类型</th>
                        <td>
                            {% if change.change_type == 'create' %}
                                <span class="badge bg-success">新增</span>
                            {% elif change.change_type == 'update' %}
                                <span class="badge bg-warning">更新</span>
                            {% elif change.change_type == 'delete' %}
                                <span class="badge bg-danger">删除</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>标题</th>
                        <td>{{ change.title }}</td>
                    </tr>
                    <tr>
                        <th>源ID</th>
                        <td><code>{{ change.source_id }}</code></td>
                    </tr>
                    <tr>
                        <th>创建时间</th>
                        <td>{{ change.created_at | datetime_format }}</td>
                    </tr>
                    {% if change.approved_at %}
                    <tr>
                        <th>批准时间</th>
                        <td>{{ change.approved_at | datetime_format }}</td>
                    </tr>
                    {% endif %}
                    {% if change.approved_by %}
                    <tr>
                        <th>批准人</th>
                        <td>{{ change.approved_by }}</td>
                    </tr>
                    {% endif %}
                    {% if change.synced_at %}
                    <tr>
                        <th>同步时间</th>
                        <td>{{ change.synced_at | datetime_format }}</td>
                    </tr>
                    {% endif %}
                    {% if change.fastgpt_id %}
                    <tr>
                        <th>FastGPT ID</th>
                        <td><code>{{ change.fastgpt_id }}</code></td>
                    </tr>
                    {% endif %}
                    {% if change.error_message %}
                    <tr>
                        <th>错误信息</th>
                        <td>
                            <div class="alert alert-danger mb-0">
                                <i class="bi bi-exclamation-triangle"></i>
                                {{ change.error_message }}
                            </div>
                        </td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>

        <!-- 内容详情 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-file-text"></i> 内容详情
                </h5>
            </div>
            <div class="card-body">
                <div class="content-section">
                    {% if change.change_type == 'delete' %}
                        <div class="alert alert-warning">
                            <i class="bi bi-trash"></i>
                            此变更为删除操作，无内容显示
                        </div>
                    {% else %}
                        <pre class="mb-0">{{ change.content }}</pre>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 元数据 -->
        {% if change.metadata %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i> 元数据
                </h5>
            </div>
            <div class="card-body">
                <div class="content-section json-viewer">
                    <pre class="mb-0">{{ change.metadata | json_pretty }}</pre>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 相关同步日志 -->
        {% if sync_logs %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-journal-text"></i> 相关同步日志
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>操作</th>
                                <th>状态</th>
                                <th>消息</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in sync_logs %}
                            <tr>
                                <td>
                                    <small>{{ log.created_at | datetime_format }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ log.operation }}</span>
                                </td>
                                <td>
                                    {% if log.status == 'success' %}
                                        <span class="badge bg-success">成功</span>
                                    {% elif log.status == 'error' %}
                                        <span class="badge bg-danger">失败</span>
                                    {% else %}
                                        <span class="badge bg-warning">{{ log.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ log.message }}</small>
                                    {% if log.error_details %}
                                        <br>
                                        <small class="text-danger">{{ log.error_details }}</small>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 操作历史 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history"></i> 操作历史
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6>变更创建</h6>
                            <p class="text-muted mb-0">{{ change.created_at | datetime_format }}</p>
                        </div>
                    </div>
                    
                    {% if change.approved_at %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6>变更批准</h6>
                            <p class="text-muted mb-0">
                                {{ change.approved_at | datetime_format }}
                                {% if change.approved_by %}
                                    <br>批准人: {{ change.approved_by }}
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if change.synced_at %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6>同步完成</h6>
                            <p class="text-muted mb-0">
                                {{ change.synced_at | datetime_format }}
                                {% if change.fastgpt_id %}
                                    <br>FastGPT ID: <code>{{ change.fastgpt_id }}</code>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if change.error_message %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-danger"></div>
                        <div class="timeline-content">
                            <h6>同步失败</h6>
                            <p class="text-danger mb-0">{{ change.error_message }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notification-toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="bi bi-info-circle text-primary me-2"></i>
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toast-message">
                <!-- 消息内容 -->
            </div>
        </div>
    </div>

    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }
        
        .timeline-marker {
            position: absolute;
            left: -23px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 3px solid #fff;
            box-shadow: 0 0 0 2px #dee2e6;
        }
        
        .timeline-content h6 {
            margin-bottom: 5px;
            font-weight: 600;
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script>
        // 显示通知
        function showNotification(message, type = 'info') {
            const toast = document.getElementById('notification-toast');
            const toastMessage = document.getElementById('toast-message');
            const toastHeader = toast.querySelector('.toast-header i');
            
            toastMessage.textContent = message;
            
            // 设置图标和颜色
            toastHeader.className = `bi me-2`;
            if (type === 'success') {
                toastHeader.classList.add('bi-check-circle', 'text-success');
            } else if (type === 'error') {
                toastHeader.classList.add('bi-exclamation-triangle', 'text-danger');
            } else {
                toastHeader.classList.add('bi-info-circle', 'text-primary');
            }
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        // 批准变更
        async function approveChange(changeId) {
            try {
                const response = await fetch('/api/approve_changes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        change_ids: [changeId],
                        approved_by: 'admin'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('批准成功', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('批准失败: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('批准失败: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>