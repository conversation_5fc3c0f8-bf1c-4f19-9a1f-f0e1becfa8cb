# 统一的产品知识库数据处理系统依赖包
# 解决版本冲突，统一管理所有依赖

# ===== 核心框架 =====
streamlit>=1.28.0,<2.0.0
fastapi>=0.104.0,<1.0.0
uvicorn[standard]>=0.24.0,<1.0.0

# ===== 数据库 =====
psycopg2-binary>=2.9.7,<3.0.0
sqlalchemy>=2.0.0,<3.0.0
alembic>=1.12.0,<2.0.0

# ===== HTTP请求和异步 =====
aiohttp>=3.8.5,<4.0.0
aiofiles>=23.2.1,<24.0.0
httpx>=0.25.0,<1.0.0
requests>=2.31.0,<3.0.0

# ===== 数据处理 =====
pandas>=2.0.3,<3.0.0
numpy>=1.24.3,<2.0.0
pydantic>=2.4.0,<3.0.0

# ===== 文档处理 =====
python-docx>=0.8.11,<1.0.0
PyPDF2>=3.0.1,<4.0.0
pdfplumber>=0.9.0,<1.0.0
markdown>=3.4.4,<4.0.0
beautifulsoup4>=4.12.0,<5.0.0
lxml>=4.9.0,<5.0.0

# ===== 图像处理 =====
Pillow>=10.0.0,<11.0.0
opencv-python>=********,<5.0.0

# ===== AI和机器学习 =====
openai>=1.3.0,<2.0.0
anthropic>=0.7.0,<1.0.0

# ===== 文本处理 =====
jieba>=0.42.1,<1.0.0
textstat>=0.7.3,<1.0.0

# ===== 文件处理 =====
openpyxl>=3.1.2,<4.0.0
chardet>=5.2.0,<6.0.0
python-multipart>=0.0.6,<1.0.0

# ===== 配置和环境 =====
python-dotenv>=1.0.0,<2.0.0
PyYAML>=6.0.1,<7.0.0
pydantic-settings>=2.0.0,<3.0.0

# ===== 缓存和存储 =====
redis>=4.6.0,<5.0.0
diskcache>=5.6.3,<6.0.0

# ===== 监控和性能 =====
psutil>=5.9.5,<6.0.0
memory-profiler>=0.61.0,<1.0.0

# ===== 安全 =====
cryptography>=41.0.3,<42.0.0
passlib[bcrypt]>=1.7.4,<2.0.0

# ===== 日期时间 =====
python-dateutil>=2.8.2,<3.0.0

# ===== 日志 =====
coloredlogs>=15.0.1,<16.0.0
structlog>=23.1.0,<24.0.0

# ===== Web界面增强 =====
streamlit-option-menu>=0.3.6,<1.0.0
streamlit-aggrid>=0.3.4,<1.0.0
plotly>=5.15.0,<6.0.0
altair>=5.0.0,<6.0.0

# ===== 开发和测试工具 =====
pytest>=7.4.0,<8.0.0
pytest-asyncio>=0.21.0,<1.0.0
pytest-cov>=4.1.0,<5.0.0
black>=23.7.0,<24.0.0
flake8>=6.0.0,<7.0.0
mypy>=1.5.0,<2.0.0

# ===== 任务队列 =====
celery>=5.3.0,<6.0.0
redis>=4.6.0,<5.0.0

# ===== 其他工具 =====
tqdm>=4.66.0,<5.0.0
click>=8.1.0,<9.0.0
rich>=13.5.0,<14.0.0