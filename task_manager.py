# -*- coding: utf-8 -*-
"""
任务管理模块
提供任务创建、执行、监控等功能
"""

import asyncio
import logging
import uuid
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import time

from database_manager import db_manager
from ai_processor import ai_processor

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskType(Enum):
    """任务类型枚举"""
    DATA_IMPORT = "data_import"
    AI_PROCESSING = "ai_processing"
    SYNC_FASTGPT = "sync_fastgpt"
    DATABASE_INIT = "database_init"
    FILE_SCAN = "file_scan"

@dataclass
class TaskConfig:
    """任务配置"""
    task_type: str
    task_name: str
    parameters: Dict[str, Any]
    created_by: str = "system"
    priority: int = 1
    max_retries: int = 3
    timeout: int = 3600  # 1小时

@dataclass
class TaskProgress:
    """任务进度"""
    task_id: str
    status: TaskStatus
    progress: int = 0
    total_items: int = 0
    processed_items: int = 0
    failed_items: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None

class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.running_tasks: Dict[str, TaskProgress] = {}
        self.task_handlers: Dict[str, Callable] = {}
        self.executor_thread = None
        self.stop_event = threading.Event()
        
        # 注册任务处理器
        self._register_handlers()
        
        # 启动任务执行器
        self.start_executor()
    
    def _register_handlers(self):
        """注册任务处理器"""
        self.task_handlers = {
            TaskType.DATABASE_INIT.value: self._handle_database_init,
            TaskType.DATA_IMPORT.value: self._handle_data_import,
            TaskType.AI_PROCESSING.value: self._handle_ai_processing,
            TaskType.SYNC_FASTGPT.value: self._handle_sync_fastgpt,
            TaskType.FILE_SCAN.value: self._handle_file_scan
        }
    
    def start_executor(self):
        """启动任务执行器"""
        if self.executor_thread is None or not self.executor_thread.is_alive():
            self.stop_event.clear()
            self.executor_thread = threading.Thread(target=self._task_executor, daemon=True)
            self.executor_thread.start()
            logger.info("任务执行器已启动")
    
    def stop_executor(self):
        """停止任务执行器"""
        self.stop_event.set()
        if self.executor_thread and self.executor_thread.is_alive():
            self.executor_thread.join(timeout=5)
        logger.info("任务执行器已停止")
    
    def create_task(self, config: TaskConfig) -> str:
        """创建任务"""
        task_id = str(uuid.uuid4())
        
        # 保存任务到数据库
        task_data = {
            'task_id': task_id,
            'task_type': config.task_type,
            'task_name': config.task_name,
            'task_status': TaskStatus.PENDING.value,
            'task_config': json.dumps(asdict(config)),
            'created_by': config.created_by,
            'created_at': datetime.now()
        }
        
        sql = """
            INSERT INTO processing_tasks 
            (task_id, task_type, task_name, task_status, task_config, created_by, created_at)
            VALUES (%(task_id)s, %(task_type)s, %(task_name)s, %(task_status)s, 
                    %(task_config)s, %(created_by)s, %(created_at)s)
        """
        
        if db_manager.execute_update(sql, task_data):
            logger.info(f"任务创建成功: {task_id} - {config.task_name}")
            return task_id
        else:
            logger.error(f"任务创建失败: {config.task_name}")
            raise Exception("任务创建失败")
    
    def get_task_progress(self, task_id: str) -> Optional[TaskProgress]:
        """获取任务进度"""
        # 先从内存中查找
        if task_id in self.running_tasks:
            return self.running_tasks[task_id]
        
        # 从数据库查找
        sql = """
            SELECT task_id, task_type, task_name, task_status, progress, 
                   total_items, processed_items, failed_items, start_time, 
                   end_time, error_message
            FROM processing_tasks 
            WHERE task_id = %s
        """
        
        result = db_manager.execute_query(sql, (task_id,))
        if result:
            task_data = result[0]
            return TaskProgress(
                task_id=task_data['task_id'],
                status=TaskStatus(task_data['task_status']),
                progress=task_data['progress'] or 0,
                total_items=task_data['total_items'] or 0,
                processed_items=task_data['processed_items'] or 0,
                failed_items=task_data['failed_items'] or 0,
                start_time=task_data['start_time'],
                end_time=task_data['end_time'],
                error_message=task_data['error_message']
            )
        
        return None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        # 更新内存中的任务状态
        if task_id in self.running_tasks:
            self.running_tasks[task_id].status = TaskStatus.CANCELLED
        
        # 更新数据库中的任务状态
        sql = """
            UPDATE processing_tasks 
            SET task_status = %s, updated_at = %s 
            WHERE task_id = %s
        """
        
        return db_manager.execute_update(sql, (TaskStatus.CANCELLED.value, datetime.now(), task_id))
    
    def get_active_tasks(self) -> List[TaskProgress]:
        """获取活动任务列表"""
        sql = """
            SELECT task_id, task_type, task_name, task_status, progress, 
                   total_items, processed_items, failed_items, start_time, 
                   end_time, error_message
            FROM processing_tasks 
            WHERE task_status IN (%s, %s)
            ORDER BY created_at DESC
        """
        
        results = db_manager.execute_query(sql, (TaskStatus.PENDING.value, TaskStatus.RUNNING.value))
        
        tasks = []
        for task_data in results:
            tasks.append(TaskProgress(
                task_id=task_data['task_id'],
                status=TaskStatus(task_data['task_status']),
                progress=task_data['progress'] or 0,
                total_items=task_data['total_items'] or 0,
                processed_items=task_data['processed_items'] or 0,
                failed_items=task_data['failed_items'] or 0,
                start_time=task_data['start_time'],
                end_time=task_data['end_time'],
                error_message=task_data['error_message']
            ))
        
        return tasks
    
    def _task_executor(self):
        """任务执行器主循环"""
        logger.info("任务执行器开始运行")
        
        while not self.stop_event.is_set():
            try:
                # 获取待执行的任务
                pending_tasks = self._get_pending_tasks()
                
                for task_data in pending_tasks:
                    if self.stop_event.is_set():
                        break
                    
                    # 执行任务
                    self._execute_task(task_data)
                
                # 等待一段时间再检查
                self.stop_event.wait(5)
                
            except Exception as e:
                logger.error(f"任务执行器异常: {e}")
                self.stop_event.wait(10)
        
        logger.info("任务执行器已停止")
    
    def _get_pending_tasks(self) -> List[Dict[str, Any]]:
        """获取待执行的任务"""
        sql = """
            SELECT task_id, task_type, task_name, task_config
            FROM processing_tasks 
            WHERE task_status = %s
            ORDER BY created_at ASC
            LIMIT 5
        """
        
        return db_manager.execute_query(sql, (TaskStatus.PENDING.value,))
    
    def _execute_task(self, task_data: Dict[str, Any]):
        """执行单个任务"""
        task_id = task_data['task_id']
        task_type = task_data['task_type']
        task_name = task_data['task_name']
        
        try:
            # 更新任务状态为运行中
            self._update_task_status(task_id, TaskStatus.RUNNING, start_time=datetime.now())
            
            # 创建任务进度对象
            progress = TaskProgress(
                task_id=task_id,
                status=TaskStatus.RUNNING,
                start_time=datetime.now()
            )
            self.running_tasks[task_id] = progress
            
            logger.info(f"开始执行任务: {task_id} - {task_name}")
            
            # 获取任务处理器
            handler = self.task_handlers.get(task_type)
            if not handler:
                raise Exception(f"未找到任务类型处理器: {task_type}")
            
            # 解析任务配置
            config_data = json.loads(task_data['task_config'])
            
            # 执行任务
            result = handler(task_id, config_data)
            
            # 更新任务状态为完成
            progress.status = TaskStatus.COMPLETED
            progress.end_time = datetime.now()
            progress.result = result
            
            self._update_task_status(
                task_id, TaskStatus.COMPLETED, 
                end_time=datetime.now(),
                progress=100
            )
            
            logger.info(f"任务执行完成: {task_id} - {task_name}")
            
        except Exception as e:
            logger.error(f"任务执行失败: {task_id} - {e}")
            
            # 更新任务状态为失败
            if task_id in self.running_tasks:
                self.running_tasks[task_id].status = TaskStatus.FAILED
                self.running_tasks[task_id].error_message = str(e)
                self.running_tasks[task_id].end_time = datetime.now()
            
            self._update_task_status(
                task_id, TaskStatus.FAILED,
                end_time=datetime.now(),
                error_message=str(e)
            )
        
        finally:
            # 从运行任务列表中移除
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    def _update_task_status(self, task_id: str, status: TaskStatus, **kwargs):
        """更新任务状态"""
        update_fields = ['task_status = %s', 'updated_at = %s']
        update_values = [status.value, datetime.now()]
        
        for field, value in kwargs.items():
            if value is not None:
                update_fields.append(f'{field} = %s')
                update_values.append(value)
        
        update_values.append(task_id)
        
        sql = f"""
            UPDATE processing_tasks 
            SET {', '.join(update_fields)}
            WHERE task_id = %s
        """
        
        db_manager.execute_update(sql, tuple(update_values))
    
    def _update_task_progress(self, task_id: str, progress: int, 
                            processed_items: int = None, failed_items: int = None):
        """更新任务进度"""
        # 更新内存中的进度
        if task_id in self.running_tasks:
            self.running_tasks[task_id].progress = progress
            if processed_items is not None:
                self.running_tasks[task_id].processed_items = processed_items
            if failed_items is not None:
                self.running_tasks[task_id].failed_items = failed_items
        
        # 更新数据库中的进度
        update_fields = ['progress = %s', 'updated_at = %s']
        update_values = [progress, datetime.now()]
        
        if processed_items is not None:
            update_fields.append('processed_items = %s')
            update_values.append(processed_items)
        
        if failed_items is not None:
            update_fields.append('failed_items = %s')
            update_values.append(failed_items)
        
        update_values.append(task_id)
        
        sql = f"""
            UPDATE processing_tasks 
            SET {', '.join(update_fields)}
            WHERE task_id = %s
        """
        
        db_manager.execute_update(sql, tuple(update_values))
    
    # 任务处理器方法
    def _handle_database_init(self, task_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """处理数据库初始化任务"""
        logger.info(f"执行数据库初始化任务: {task_id}")
        
        self._update_task_progress(task_id, 10)
        
        # 执行数据库初始化
        success = db_manager.initialize_database()
        
        self._update_task_progress(task_id, 100)
        
        return {
            'success': success,
            'message': '数据库初始化完成' if success else '数据库初始化失败'
        }
    
    def _handle_data_import(self, task_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """处理数据导入任务"""
        logger.info(f"执行数据导入任务: {task_id}")
        
        # 这里应该实现实际的数据导入逻辑
        self._update_task_progress(task_id, 50)
        time.sleep(2)  # 模拟处理时间
        self._update_task_progress(task_id, 100)
        
        return {
            'success': True,
            'imported_items': 100,
            'message': '数据导入完成'
        }
    
    def _handle_ai_processing(self, task_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """处理AI增强任务"""
        logger.info(f"执行AI增强任务: {task_id}")
        
        # 这里应该实现实际的AI处理逻辑
        self._update_task_progress(task_id, 30)
        time.sleep(3)  # 模拟处理时间
        self._update_task_progress(task_id, 100)
        
        return {
            'success': True,
            'processed_documents': 50,
            'total_cost': 10.5,
            'message': 'AI增强处理完成'
        }
    
    def _handle_sync_fastgpt(self, task_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """处理FastGPT同步任务"""
        logger.info(f"执行FastGPT同步任务: {task_id}")
        
        # 这里应该实现实际的同步逻辑
        self._update_task_progress(task_id, 70)
        time.sleep(2)  # 模拟处理时间
        self._update_task_progress(task_id, 100)
        
        return {
            'success': True,
            'synced_items': 25,
            'message': 'FastGPT同步完成'
        }
    
    def _handle_file_scan(self, task_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """处理文件扫描任务"""
        logger.info(f"执行文件扫描任务: {task_id}")
        
        # 这里应该实现实际的文件扫描逻辑
        self._update_task_progress(task_id, 60)
        time.sleep(1)  # 模拟处理时间
        self._update_task_progress(task_id, 100)
        
        return {
            'success': True,
            'scanned_files': 200,
            'message': '文件扫描完成'
        }

# 全局任务管理器实例
task_manager = TaskManager()
