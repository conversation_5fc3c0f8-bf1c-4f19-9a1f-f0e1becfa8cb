# config包初始化文件
# 使config目录成为Python包

__version__ = "1.0.0"
__author__ = "sync-db-fastgpt"

# 从根目录的config.py导入所需的配置
import sys
import os
import importlib.util

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
config_file_path = os.path.join(project_root, 'siliconflow_config.py')

try:
    # 使用importlib直接加载根目录的config.py文件，避免循环导入
    spec = importlib.util.spec_from_file_location("root_config", config_file_path)
    root_config = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(root_config)

    # 导入根目录config.py中的配置
    DATABASE_CONFIG = getattr(root_config, 'DATABASE_CONFIG', {})
    API_CONFIG = getattr(root_config, 'API_CONFIG', {})
    FILE_PATHS = getattr(root_config, 'FILE_PATHS', {})
    LOG_CONFIG = getattr(root_config, 'LOG_CONFIG', {})
    PROCESS_CONFIG = getattr(root_config, 'PROCESS_CONFIG', {})
    TABLE_CONFIG = getattr(root_config, 'TABLE_CONFIG', {})

    # 导出这些配置，使其可以通过 from config import ... 访问
    __all__ = [
        'DATABASE_CONFIG',
        'API_CONFIG',
        'FILE_PATHS',
        'LOG_CONFIG',
        'PROCESS_CONFIG',
        'TABLE_CONFIG'
    ]

except Exception as e:
    print(f"警告: 无法导入根目录config.py中的配置: {e}")
    # 提供默认配置以防导入失败
    DATABASE_CONFIG = {}
    API_CONFIG = {}
    FILE_PATHS = {}
    LOG_CONFIG = {}
    PROCESS_CONFIG = {}
    TABLE_CONFIG = {}