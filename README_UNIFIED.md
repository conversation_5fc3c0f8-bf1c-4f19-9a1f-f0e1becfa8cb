# 产品知识库数据处理系统 - 统一版本

## 🎯 项目概述

这是一个基于硅基流动大模型的智能知识库索引生成与FastGPT同步系统的统一版本。该版本整合了所有功能模块，提供了统一的配置管理、错误处理、任务管理和Web界面。

## ✨ 主要特性

### 🔧 统一配置管理
- 统一的配置文件格式（YAML）
- 环境变量支持
- 配置验证和热重载
- 向后兼容旧配置文件

### 🎨 现代化Web界面
- 基于Streamlit的响应式界面
- 实时任务监控和进度显示
- 直观的配置管理界面
- 详细的日志监控

### 🤖 AI增强处理
- 多模型支持（视觉、文本、代码、轻量级）
- 智能图片相关性分析
- 内容结构优化
- 自动问答对生成

### 📊 任务管理系统
- 异步任务执行
- 实时进度跟踪
- 任务队列管理
- 错误处理和重试机制

### 🗄️ 数据库管理
- 连接池管理
- 自动表结构创建
- 数据迁移支持
- 性能优化

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- PostgreSQL 12+
- 8GB+ RAM（推荐）
- 网络连接（用于AI API调用）

### 2. 安装和配置

#### 方法一：自动安装（推荐）

```bash
# 克隆项目
git clone <repository_url>
cd sync-db-fastgpt

# 运行自动安装脚本
python run_unified_app.py
```

#### 方法二：手动安装

```bash
# 安装依赖
pip install -r requirements_unified.txt

# 复制配置模板
cp unified_config.yaml.template unified_config.yaml
cp .env.template .env

# 编辑配置文件
# 修改 unified_config.yaml 和 .env 中的配置项

# 启动应用
python run_unified_app.py
```

### 3. 配置说明

#### 数据库配置
```yaml
database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "your_password"
  dbname: "product_knowledge_db"
```

#### API配置
```yaml
siliconflow:
  api_key: "your_api_key"
  api_base_url: "https://api.siliconflow.cn/v1"

fastgpt:
  api_key: "your_fastgpt_key"
  dataset_id: "your_dataset_id"
```

### 4. 启动应用

```bash
# 完整启动（包含环境检查、依赖安装、数据库初始化）
python run_unified_app.py

# 仅检查环境
python run_unified_app.py --check-only

# 跳过依赖安装
python run_unified_app.py --skip-deps

# 指定端口
python run_unified_app.py --port 8502
```

## 📱 使用指南

### 仪表板
- 查看系统状态概览
- 快速健康检查
- 系统统计信息

### 数据处理
- 配置数据源（云商API、本地文件、历史数据）
- 设置处理参数
- 启动批量处理任务
- 监控处理进度

### 同步管理
- 检测数据变更
- 管理待审核内容
- 执行FastGPT同步
- 查看同步报告

### AI增强
- 配置AI模型参数
- 图片相关性分析
- 内容结构优化
- 问答对生成

### 配置管理
- 在线编辑配置
- 测试连接状态
- 保存和验证配置
- 创建必要目录

### 日志监控
- 实时日志查看
- 日志级别过滤
- 错误统计分析
- 自动刷新功能

## 🔧 高级功能

### 任务管理API

```python
from task_manager import task_manager, TaskConfig, TaskType

# 创建任务
config = TaskConfig(
    task_type=TaskType.AI_PROCESSING.value,
    task_name="AI增强处理",
    parameters={"batch_size": 100}
)
task_id = task_manager.create_task(config)

# 查询进度
progress = task_manager.get_task_progress(task_id)
print(f"进度: {progress.progress}%")
```

### AI处理API

```python
from ai_processor import ai_processor
import asyncio

async def process_content():
    async with ai_processor as processor:
        # 内容优化
        result = await processor.optimize_content_structure(content)
        
        # 图片分析
        image_result = await processor.analyze_image_relevance(
            image_path, context
        )
        
        # 关键词提取
        keywords = await processor.extract_keywords(content)
```

### 数据库操作

```python
from database_manager import db_manager

# 查询数据
results = db_manager.execute_query(
    "SELECT * FROM Product WHERE category = %s", 
    ("门禁",)
)

# 更新数据
success = db_manager.execute_update(
    "UPDATE Product SET status = %s WHERE id = %s",
    ("active", "product_id")
)
```

## 🛠️ 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务是否启动
   - 验证数据库配置信息
   - 确认网络连接

2. **API调用失败**
   - 检查API密钥是否正确
   - 验证网络连接
   - 查看API配额限制

3. **依赖安装失败**
   - 更新pip版本：`pip install --upgrade pip`
   - 使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple`
   - 检查Python版本兼容性

4. **内存不足**
   - 减少批处理大小
   - 降低并发请求数
   - 增加系统内存

### 日志分析

```bash
# 查看应用日志
tail -f logs/unified_app.log

# 查看错误日志
grep ERROR logs/unified_app.log

# 查看特定任务日志
grep "task_id" logs/unified_app.log
```

## 📊 性能优化

### 数据库优化
- 使用连接池
- 创建适当索引
- 定期清理日志表
- 优化查询语句

### AI处理优化
- 启用结果缓存
- 控制并发请求数
- 压缩图片大小
- 批量处理文档

### 系统优化
- 监控内存使用
- 定期清理临时文件
- 优化文件I/O操作
- 使用异步处理

## 🔒 安全建议

1. **API密钥管理**
   - 使用环境变量存储密钥
   - 定期轮换API密钥
   - 限制API访问权限

2. **数据库安全**
   - 使用强密码
   - 限制数据库访问IP
   - 定期备份数据

3. **文件安全**
   - 验证上传文件类型
   - 限制文件大小
   - 扫描恶意内容

## 📈 监控和维护

### 系统监控
- CPU和内存使用率
- 数据库连接数
- API调用频率和成本
- 任务执行状态

### 定期维护
- 清理过期日志
- 备份重要数据
- 更新依赖包
- 检查安全漏洞

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 📞 支持

如有问题或建议，请：
1. 查看文档和FAQ
2. 搜索已有Issues
3. 创建新的Issue
4. 联系开发团队

---

**注意**: 这是一个统一整合版本，包含了原项目的所有功能并进行了优化和改进。建议使用此版本替代原有的分散模块。
