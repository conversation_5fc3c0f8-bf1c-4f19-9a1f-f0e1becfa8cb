# 开发和测试依赖
# Development and Testing Dependencies

# 测试框架
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.1
pytest-asyncio>=0.21.1

# 代码质量
flake8>=6.0.0
black>=23.7.0
isort>=5.12.0
mypy>=1.5.0
pylint>=2.17.0

# 代码覆盖率
coverage>=7.3.0

# 文档生成
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# 性能分析
line-profiler>=4.1.0
memory-profiler>=0.61.0

# 调试工具
ipdb>=0.13.13
pdbpp>=0.10.3

# 开发工具
pre-commit>=3.3.0
tox>=4.6.0

# API测试
requests-mock>=1.11.0
responses>=0.23.0

# 数据库测试
pytest-postgresql>=5.0.0
factory-boy>=3.3.0

# 环境管理
python-dotenv>=1.0.0

# 类型检查
types-requests>=2.31.0
types-PyYAML>=6.0.0

# 安全检查
bandit>=1.7.5
safety>=2.3.0

# 依赖管理
pip-tools>=7.1.0
pipdeptree>=2.13.0