#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强文档处理器测试脚本

这个脚本用于测试增强文档处理器的各项功能，包括：
1. 文件格式优先级选择
2. PDF解析功能
3. 多模态图片标注
4. 增量内容补充
5. FastGPT集成

使用方法:
    python test_enhanced_processor.py
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from enhanced_document_processor import EnhancedDocumentProcessor
    from enhanced_config import get_config, validate_config
    from config import DATABASE_CONFIG
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有依赖已正确安装")
    sys.exit(1)


class TestEnhancedProcessor:
    """增强文档处理器测试类"""
    
    def __init__(self):
        self.test_dir = None
        self.processor = None
        
    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 创建临时测试目录
        self.test_dir = tempfile.mkdtemp(prefix="enhanced_processor_test_")
        print(f"测试目录: {self.test_dir}")
        
        # 创建测试文档结构
        self.create_test_documents()
        
        # 初始化处理器
        try:
            self.processor = EnhancedDocumentProcessor(DATABASE_CONFIG)
            print("✅ 增强文档处理器初始化成功")
        except Exception as e:
            print(f"❌ 处理器初始化失败: {e}")
            return False
            
        return True
    
    def create_test_documents(self):
        """创建测试文档"""
        print("📄 创建测试文档...")
        
        # 产品目录结构
        products = {
            'PRODUCT_001': {
                '用户手册': {
                    'manual.md': self.get_markdown_content(),
                    'manual.docx': 'Word文档内容（模拟）',
                    'manual.pdf': 'PDF文档内容（模拟）'
                },
                '技术规格': {
                    'specs.md': self.get_specs_content(),
                    'specs.pdf': 'PDF规格文档（模拟）'
                }
            },
            'PRODUCT_002': {
                '安装指南': {
                    'install.pdf': 'PDF安装指南（模拟）'
                },
                '维护手册': {
                    'maintenance.md': self.get_maintenance_content(),
                    'maintenance.docx': 'Word维护手册（模拟）'
                }
            }
        }
        
        # 创建文档文件
        for product_id, categories in products.items():
            product_dir = Path(self.test_dir) / product_id
            product_dir.mkdir(parents=True, exist_ok=True)
            
            for category, files in categories.items():
                category_dir = product_dir / category
                category_dir.mkdir(parents=True, exist_ok=True)
                
                for filename, content in files.items():
                    file_path = category_dir / filename
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
        
        print(f"✅ 测试文档创建完成，共创建 {len(products)} 个产品目录")
    
    def get_markdown_content(self):
        """获取Markdown测试内容"""
        return """
# 产品用户手册

## 概述
这是一个测试产品的用户手册，用于演示增强文档处理器的功能。

## 主要特性
- 高性能处理
- 智能识别
- 多格式支持

## 安装步骤
1. 下载安装包
2. 运行安装程序
3. 完成配置

![产品图片](images/product.jpg)

## 使用说明
详细的使用说明请参考技术文档。

## 故障排除
如遇问题请联系技术支持。
"""
    
    def get_specs_content(self):
        """获取技术规格内容"""
        return """
# 技术规格

## 硬件要求
- CPU: Intel i5 或更高
- 内存: 8GB RAM
- 存储: 100GB 可用空间

## 软件要求
- 操作系统: Windows 10/11
- Python: 3.8+
- 数据库: PostgreSQL 12+

## 性能指标
- 处理速度: 1000 文档/小时
- 准确率: 99.5%
- 支持格式: MD, DOCX, PDF
"""
    
    def get_maintenance_content(self):
        """获取维护手册内容"""
        return """
# 维护手册

## 日常维护
- 定期清理缓存
- 检查日志文件
- 更新配置

## 故障诊断
1. 检查系统状态
2. 查看错误日志
3. 重启服务

## 备份策略
- 每日增量备份
- 每周全量备份
- 异地存储
"""
    
    def test_config_validation(self):
        """测试配置验证"""
        print("\n🔍 测试配置验证...")
        
        try:
            config = get_config()
            is_valid, missing = validate_config(config)
            
            if is_valid:
                print("✅ 配置验证通过")
            else:
                print(f"⚠️ 配置验证失败，缺少: {missing}")
                print("请检查 .env 文件中的配置项")
                
            return is_valid
        except Exception as e:
            print(f"❌ 配置验证异常: {e}")
            return False
    
    def test_file_priority_selection(self):
        """测试文件格式优先级选择"""
        print("\n📋 测试文件格式优先级选择...")
        
        try:
            # 测试PRODUCT_001的用户手册（有MD、DOCX、PDF）
            product_path = Path(self.test_dir) / 'PRODUCT_001' / '用户手册'
            
            # 扫描文档
            documents = self.processor.scan_product_documents(
                str(product_path), 'PRODUCT_001', '用户手册'
            )
            
            if documents:
                # 选择最优文档
                best_doc = self.processor.select_best_document(documents)
                print(f"✅ 最优文档选择: {best_doc['file_path']}")
                print(f"   格式: {best_doc['format']}")
                print(f"   优先级: {best_doc['priority']}")
                
                # 验证是否选择了Markdown格式
                if best_doc['format'] == 'markdown':
                    print("✅ 正确选择了Markdown格式（最高优先级）")
                    return True
                else:
                    print(f"⚠️ 选择了 {best_doc['format']} 格式，期望 markdown")
                    return False
            else:
                print("❌ 未找到任何文档")
                return False
                
        except Exception as e:
            print(f"❌ 文件优先级选择测试失败: {e}")
            return False
    
    def test_document_processing(self):
        """测试文档处理"""
        print("\n📖 测试文档处理...")
        
        try:
            # 处理PRODUCT_001的用户手册
            success = self.processor.process_product_documents(
                product_id='PRODUCT_001',
                category='用户手册',
                base_path=str(Path(self.test_dir) / 'PRODUCT_001' / '用户手册')
            )
            
            if success:
                print("✅ 文档处理成功")
                return True
            else:
                print("❌ 文档处理失败")
                return False
                
        except Exception as e:
            print(f"❌ 文档处理测试失败: {e}")
            return False
    
    def test_batch_processing(self):
        """测试批量处理"""
        print("\n🔄 测试批量处理...")
        
        try:
            # 批量处理配置
            products_config = [
                {
                    'product_id': 'PRODUCT_001',
                    'category': '技术规格',
                    'base_path': str(Path(self.test_dir) / 'PRODUCT_001' / '技术规格')
                },
                {
                    'product_id': 'PRODUCT_002',
                    'category': '维护手册',
                    'base_path': str(Path(self.test_dir) / 'PRODUCT_002' / '维护手册')
                }
            ]
            
            # 执行批量处理
            results = self.processor.batch_process_products(products_config)
            
            print(f"✅ 批量处理完成")
            print(f"   成功: {results['success']}")
            print(f"   失败: {results['failed']}")
            print(f"   总计: {results['total']}")
            
            return results['success'] > 0
            
        except Exception as e:
            print(f"❌ 批量处理测试失败: {e}")
            return False
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        if self.test_dir and os.path.exists(self.test_dir):
            try:
                shutil.rmtree(self.test_dir)
                print("✅ 测试环境清理完成")
            except Exception as e:
                print(f"⚠️ 清理测试环境失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始增强文档处理器测试")
        print("=" * 50)
        
        # 设置测试环境
        if not self.setup_test_environment():
            print("❌ 测试环境设置失败，退出测试")
            return False
        
        test_results = []
        
        try:
            # 运行各项测试
            test_results.append(('配置验证', self.test_config_validation()))
            test_results.append(('文件优先级选择', self.test_file_priority_selection()))
            test_results.append(('文档处理', self.test_document_processing()))
            test_results.append(('批量处理', self.test_batch_processing()))
            
        finally:
            # 清理测试环境
            self.cleanup_test_environment()
        
        # 输出测试结果
        print("\n" + "=" * 50)
        print("📊 测试结果汇总")
        print("=" * 50)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！增强文档处理器工作正常")
            return True
        else:
            print("⚠️ 部分测试失败，请检查配置和依赖")
            return False


def main():
    """主函数"""
    tester = TestEnhancedProcessor()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎯 测试建议:")
        print("1. 配置 .env 文件中的API密钥以启用完整功能")
        print("2. 运行 python example_usage.py 查看更多示例")
        print("3. 启动 python admin_review_app.py 使用Web管理界面")
    else:
        print("\n🔧 故障排除建议:")
        print("1. 检查 requirements.txt 中的依赖是否已安装")
        print("2. 确保数据库连接配置正确")
        print("3. 检查 .env 文件配置")
    
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())