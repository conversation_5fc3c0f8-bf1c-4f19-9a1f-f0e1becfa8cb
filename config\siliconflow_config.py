# -*- coding: utf-8 -*-
"""
硅基流动最新模型配置文件

支持的模型：
- Qwen/Qwen2-VL-72B-Instruct: 最新多模态视觉模型
- Qwen/Qwen2.5-72B-Instruct: 最新文本生成模型
- deepseek-ai/DeepSeek-Coder-V2-Instruct: 代码生成模型
"""

import os
from typing import Dict, Any

class SiliconFlowConfig:
    """
    硅基流动最新模型配置类
    
    提供统一的API配置和模型参数管理
    """
    
    def __init__(self, api_key: str = None):
        """
        初始化硅基流动配置
        
        Args:
            api_key: API密钥，如果不提供则从环境变量获取
        """
        self.API_KEY = api_key or os.getenv('SILICONFLOW_API_KEY') or 'sk-kmaipghbqavpzfnhpuuybpgrimcroynvsqlfkbnhcjcdulxj'
        
        # API基础配置
        self.API_BASE_URL = "https://api.siliconflow.cn/v1"
        self.API_VERSION = "v1"
        
        # 模型配置
        self.MODELS = {
            # 多模态视觉模型（用于图片分析和相关性检测）
            'vision': {
                'model_name': 'Qwen/Qwen2-VL-72B-Instruct',
                'description': '最新的多模态视觉理解模型，支持图片分析和文本理解',
                'max_tokens': 4096,
                'temperature': 0.1,  # 较低温度确保分析结果稳定
                'top_p': 0.9,
                'supports_vision': True,
                'cost_per_1k_tokens': 0.002  # 示例价格，需要根据实际情况调整
            },
            
            # 文本生成模型（用于内容优化和结构化处理）
            'text': {
                'model_name': 'Qwen/Qwen2.5-72B-Instruct',
                'description': '最新的大语言模型，擅长文本理解、生成和优化',
                'max_tokens': 8192,
                'temperature': 0.3,  # 适中温度平衡创造性和准确性
                'top_p': 0.9,
                'supports_vision': False,
                'cost_per_1k_tokens': 0.001
            },
            
            # 代码生成模型（用于结构化数据处理）
            'code': {
                'model_name': 'deepseek-ai/DeepSeek-Coder-V2-Instruct',
                'description': '专业的代码生成模型，擅长结构化数据处理',
                'max_tokens': 4096,
                'temperature': 0.1,  # 低温度确保代码准确性
                'top_p': 0.95,
                'supports_vision': False,
                'cost_per_1k_tokens': 0.0015
            },
            
            # 轻量级模型（用于简单任务）
            'lightweight': {
                'model_name': 'Qwen/Qwen2.5-7B-Instruct',
                'description': '轻量级模型，适合简单的文本处理任务',
                'max_tokens': 2048,
                'temperature': 0.2,
                'top_p': 0.9,
                'supports_vision': False,
                'cost_per_1k_tokens': 0.0005
            }
        }
        
        # 请求配置
        self.REQUEST_CONFIG = {
            'timeout': 120,  # 请求超时时间（秒）
            'max_retries': 3,  # 最大重试次数
            'retry_delay': 2,  # 重试延迟（秒）
            'backoff_factor': 2,  # 退避因子
            'max_concurrent_requests': 5,  # 最大并发请求数
            'rate_limit_per_minute': 60  # 每分钟请求限制
        }
        
        # 任务配置映射
        self.TASK_MODEL_MAPPING = {
            'image_relevance': 'vision',
            'image_description': 'vision',
            'content_optimization': 'text',
            'keyword_extraction': 'text',
            'qa_generation': 'text',
            'structure_analysis': 'code',
            'simple_text_processing': 'lightweight'
        }
        
        # 质量控制配置
        self.QUALITY_CONFIG = {
            'min_confidence_score': 0.7,  # 最小置信度分数
            'image_relevance_threshold': 6.0,  # 图片相关性保留阈值
            'max_image_size_mb': 10,  # 最大图片大小（MB）
            'supported_image_formats': ['jpg', 'jpeg', 'png', 'webp', 'bmp'],
            'max_content_length': 50000  # 最大内容长度（字符）
        }
        
        # 缓存配置
        self.CACHE_CONFIG = {
            'enable_cache': True,
            'cache_ttl': 3600,  # 缓存生存时间（秒）
            'max_cache_size': 1000,  # 最大缓存条目数
            'cache_key_prefix': 'siliconflow_'
        }
        
        # 日志配置
        self.LOGGING_CONFIG = {
            'log_api_calls': True,
            'log_response_time': True,
            'log_token_usage': True,
            'log_errors': True,
            'sensitive_data_mask': True  # 是否屏蔽敏感数据
        }
    
    def get_model_config(self, model_type: str) -> Dict[str, Any]:
        """
        获取指定模型类型的配置
        
        Args:
            model_type: 模型类型 ('vision', 'text', 'code', 'lightweight')
            
        Returns:
            模型配置字典
            
        Raises:
            ValueError: 当模型类型不存在时
        """
        if model_type not in self.MODELS:
            raise ValueError(f"不支持的模型类型: {model_type}. 支持的类型: {list(self.MODELS.keys())}")
        
        return self.MODELS[model_type].copy()
    
    def get_task_model(self, task_name: str) -> str:
        """
        根据任务名称获取推荐的模型类型
        
        Args:
            task_name: 任务名称
            
        Returns:
            推荐的模型类型
        """
        return self.TASK_MODEL_MAPPING.get(task_name, 'text')
    
    def get_headers(self) -> Dict[str, str]:
        """
        获取API请求头
        
        Returns:
            请求头字典
        """
        return {
            'Authorization': f'Bearer {self.API_KEY}',
            'Content-Type': 'application/json',
            'User-Agent': 'FastGPT-SyncDB/1.0'
        }
    
    def validate_config(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            配置是否有效
        """
        # 检查API密钥
        if not self.API_KEY or self.API_KEY == 'your_siliconflow_api_key':
            print("警告: 请设置有效的硅基流动API密钥")
            return False
        
        # 检查模型配置
        for model_type, config in self.MODELS.items():
            required_fields = ['model_name', 'max_tokens', 'temperature', 'top_p']
            for field in required_fields:
                if field not in config:
                    print(f"错误: 模型 {model_type} 缺少必需字段 {field}")
                    return False
        
        return True
    
    def get_cost_estimate(self, model_type: str, token_count: int) -> float:
        """
        估算API调用成本
        
        Args:
            model_type: 模型类型
            token_count: 预估token数量
            
        Returns:
            预估成本（美元）
        """
        if model_type not in self.MODELS:
            return 0.0
        
        cost_per_1k = self.MODELS[model_type].get('cost_per_1k_tokens', 0.001)
        return (token_count / 1000) * cost_per_1k
    
    def __str__(self) -> str:
        """
        返回配置的字符串表示
        """
        return f"SiliconFlowConfig(models={len(self.MODELS)}, api_url={self.API_BASE_URL})"


# 全局配置实例
config = SiliconFlowConfig()

# 配置验证
if __name__ == "__main__":
    print("硅基流动配置验证:")
    print(f"配置有效性: {config.validate_config()}")
    print(f"支持的模型: {list(config.MODELS.keys())}")
    print(f"API基础URL: {config.API_BASE_URL}")
    
    # 显示模型信息
    for model_type, model_config in config.MODELS.items():
        print(f"\n{model_type.upper()} 模型:")
        print(f"  名称: {model_config['model_name']}")
        print(f"  描述: {model_config['description']}")
        print(f"  最大tokens: {model_config['max_tokens']}")
        print(f"  支持视觉: {model_config.get('supports_vision', False)}")
        print(f"  成本/1K tokens: ${model_config.get('cost_per_1k_tokens', 0):.4f}")