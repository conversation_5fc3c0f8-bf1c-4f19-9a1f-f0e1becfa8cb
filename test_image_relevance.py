#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片相关性检测测试脚本
测试增强文档处理器的图片与文档内容相关性检测功能
"""

import os
import sys
import json
import tempfile
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_document_processor import EnhancedDocumentProcessor, DocumentInfo
from config import DATABASE_CONFIG

def create_test_markdown_with_images():
    """
    创建包含相关和无关图片的测试Markdown文档
    
    Returns:
        str: 测试文档路径
    """
    test_content = """
# 产品技术规格文档

## 产品概述
本产品是一款高性能的智能设备，具有以下特点：
- 先进的处理器架构
- 高效的能耗管理
- 智能化的用户界面

## 技术参数

### 硬件规格
![产品外观图](./images/product_appearance.jpg)

处理器：ARM Cortex-A78
内存：8GB LPDDR5
存储：256GB UFS 3.1

### 软件特性
![系统架构图](./images/system_architecture.png)

操作系统：Android 13
用户界面：自定义UI 5.0

## 安装指南

### 安装步骤
![安装流程图](./images/installation_flow.png)

1. 准备安装环境
2. 连接设备
3. 运行安装程序

## 其他信息

![装饰性图片](./images/decoration.jpg)
![广告图片](./images/advertisement.png)
![个人照片](./images/personal_photo.jpg)

## 联系我们
如有问题，请联系技术支持。
"""
    
    # 创建临时目录和文件
    temp_dir = tempfile.mkdtemp()
    doc_path = os.path.join(temp_dir, "test_product_spec.md")
    
    with open(doc_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    # 创建图片目录（模拟图片文件）
    images_dir = os.path.join(temp_dir, "images")
    os.makedirs(images_dir, exist_ok=True)
    
    # 创建模拟图片文件
    image_files = [
        "product_appearance.jpg",
        "system_architecture.png", 
        "installation_flow.png",
        "decoration.jpg",
        "advertisement.png",
        "personal_photo.jpg"
    ]
    
    for img_file in image_files:
        img_path = os.path.join(images_dir, img_file)
        # 创建一个小的占位符文件
        with open(img_path, 'wb') as f:
            f.write(b'\x89PNG\r\n\x1a\n')  # PNG文件头
    
    print(f"测试文档已创建: {doc_path}")
    return doc_path

def test_image_relevance_detection():
    """
    测试图片相关性检测功能
    """
    print("=== 图片相关性检测测试 ===")
    
    try:
        # 创建处理器实例
        processor = EnhancedDocumentProcessor(DATABASE_CONFIG)
        
        # 创建测试文档
        test_doc_path = create_test_markdown_with_images()
        
        # 创建DocumentInfo对象
        doc_info = DocumentInfo(
            file_path=test_doc_path,
            file_format='markdown',
            priority=1,
            file_size=os.path.getsize(test_doc_path),
            file_hash=processor.get_file_hash(test_doc_path),
            last_modified=datetime.fromtimestamp(os.path.getmtime(test_doc_path)),
            product_id="TEST_PRODUCT_001",
            category="技术规格"
        )
        
        print(f"\n处理文档: {doc_info.file_path}")
        print(f"文档格式: {doc_info.file_format}")
        print(f"产品ID: {doc_info.product_id}")
        print(f"类别: {doc_info.category}")
        
        # 处理文档内容
        processed_doc = processor.process_document_content(doc_info)
        
        # 显示处理结果
        print("\n=== 处理结果 ===")
        print(f"文档内容长度: {len(processed_doc.content)} 字符")
        print(f"保留图片数量: {len(processed_doc.images or [])}")
        print(f"移除图片数量: {len(processed_doc.removed_images or [])}")
        
        # 显示保留的图片
        if processed_doc.images:
            print("\n=== 保留的相关图片 ===")
            for i, img in enumerate(processed_doc.images, 1):
                print(f"{i}. {os.path.basename(img['path'])}")
                print(f"   描述: {img.get('description', 'N/A')[:100]}...")
                print(f"   相关性分析: {img.get('relevance_analysis', 'N/A')[:100]}...")
                print()
        
        # 显示移除的图片
        if processed_doc.removed_images:
            print("\n=== 移除的无关图片 ===")
            for i, img in enumerate(processed_doc.removed_images, 1):
                print(f"{i}. {os.path.basename(img['path'])}")
                print(f"   移除原因: {img.get('relevance_analysis', 'N/A')[:100]}...")
                print()
        
        # 显示处理后的内容片段
        print("\n=== 处理后的文档内容片段 ===")
        content_lines = processed_doc.content.split('\n')
        for i, line in enumerate(content_lines[-10:], len(content_lines)-9):
            print(f"{i:3d}: {line}")
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_relevance_parsing():
    """
    测试相关性结果解析功能
    """
    print("\n=== 相关性结果解析测试 ===")
    
    processor = EnhancedDocumentProcessor(DATABASE_CONFIG)
    
    # 测试用例
    test_cases = [
        {
            'analysis': '相关性评分（0-10分，10分表示高度相关）：8\n相关性分析：图片显示了产品的外观设计\n是否保留：是',
            'expected': True
        },
        {
            'analysis': '相关性评分：3\n相关性分析：这是一张装饰性图片，与产品技术无关\n是否保留：否',
            'expected': False
        },
        {
            'analysis': '这张图片包含重要的技术信息，应该保留在文档中',
            'expected': True
        },
        {
            'analysis': '这是无关的广告图片，建议删除',
            'expected': False
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        result = processor._parse_relevance_result(case['analysis'])
        status = "✓" if result == case['expected'] else "✗"
        print(f"测试 {i}: {status} 预期: {case['expected']}, 实际: {result}")
        print(f"   分析文本: {case['analysis'][:50]}...")
        print()
    
    print("相关性结果解析测试完成")

def main():
    """
    主测试函数
    """
    print("图片相关性检测功能测试")
    print("=" * 50)
    
    # 检查配置
    print("检查配置...")
    if not hasattr(EnhancedDocumentProcessor, 'MULTIMODAL_CONFIG'):
        print("警告: 多模态配置未找到，请确保已正确配置API密钥")
    
    # 运行测试
    try:
        # 测试相关性结果解析
        test_relevance_parsing()
        
        # 测试图片相关性检测（需要API密钥）
        print("\n注意: 图片相关性检测测试需要配置有效的多模态AI API密钥")
        print("如果未配置API密钥，测试将使用默认行为（保留所有图片）")
        
        user_input = input("\n是否继续进行完整的图片相关性检测测试？(y/n): ")
        if user_input.lower() == 'y':
            success = test_image_relevance_detection()
            if success:
                print("\n所有测试通过！")
            else:
                print("\n测试失败，请检查配置和日志")
        else:
            print("跳过完整测试")
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()