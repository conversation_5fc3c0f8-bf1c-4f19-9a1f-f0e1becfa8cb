# -*- coding: utf-8 -*-
"""
配置文件
包含数据库连接、API配置等信息
请根据实际环境修改相应配置
"""

import os

# 获取脚本目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# 数据库配置
DATABASE_CONFIG = {
    'host': '**************',  # 数据库主机地址
    'user': 'username',   # 数据库用户名
    'password': 'password',  # 数据库密码 - 请修改为实际密码
    'dbname': 'product_knowledge_db',  # 数据库名称
    'port': 5433
}

# 云商API配置
API_CONFIG = {
    'base_url': 'https://zkmall.zktecoip.com',
    'username': '18929343717',  # 云商用户名
    'password': 'Zk@123456',  # 云商密码
    'download_dir': os.path.join(SCRIPT_DIR, 'downloads')  # 下载目录
}

# 文件路径配置
FILE_PATHS = {
    'allcollections': os.path.join(SCRIPT_DIR, 'allcollections.json'),
    'product_structure': os.path.join(SCRIPT_DIR, '国内产品结构细化表.json'),
    'local_files_base': os.path.join('E:', 'pyydemo', 'work', 'new_work', 'get_mongo_alldata', 'AI客服知识库')  # 更新后的路径
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': os.path.join(SCRIPT_DIR, 'logs', 'product_processor.log')
}

# 数据库表配置
TABLE_CONFIG = {
    'create_if_not_exists': True,
    'drop_existing': False  # 设置为True将删除现有表重新创建
}

# 处理配置
PROCESS_CONFIG = {
    'batch_size': 100,  # 批处理大小
    'max_retries': 3,   # 最大重试次数
    'timeout': 30,      # 请求超时时间（秒）
    'enable_local_scan': True,  # 是否启用本地文件扫描
    'enable_yunshang_api': True,  # 是否启用云商API
    'enable_legacy_import': True  # 是否导入历史数据
}