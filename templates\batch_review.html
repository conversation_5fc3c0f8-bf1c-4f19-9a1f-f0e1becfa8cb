<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量审核 - FastGPT知识库同步管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .filter-section {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .content-preview {
            max-height: 80px;
            overflow-y: auto;
            font-size: 0.85em;
            background-color: #f8f9fa;
            padding: 6px;
            border-radius: 4px;
        }
        .batch-actions {
            position: sticky;
            top: 0;
            background: white;
            z-index: 100;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 0;
            margin-bottom: 1rem;
        }
        .table-container {
            max-height: 70vh;
            overflow-y: auto;
        }
        .loading {
            display: none;
        }
        .progress-container {
            display: none;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-database"></i> FastGPT知识库同步管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="bi bi-house"></i> 首页
                </a>
                <a class="nav-link active" href="/batch_review">
                    <i class="bi bi-check2-square"></i> 批量审核
                </a>
                <a class="nav-link" href="/sync_logs">
                    <i class="bi bi-journal-text"></i> 同步日志
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">数据源类型</label>
                    <select class="form-select" id="source-type-filter">
                        <option value="">全部</option>
                        <option value="yunshang_api">云商API</option>
                        <option value="local_file">本地文件</option>
                        <option value="legacy_data">历史数据</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">变更类型</label>
                    <select class="form-select" id="change-type-filter">
                        <option value="">全部</option>
                        <option value="create">新增</option>
                        <option value="update">更新</option>
                        <option value="delete">删除</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">关键词搜索</label>
                    <input type="text" class="form-control" id="keyword-filter" placeholder="搜索标题或内容...">
                </div>
                <div class="col-md-3">
                    <label class="form-label">操作</label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="bi bi-search"></i> 筛选
                        </button>
                        <button class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="bi bi-x-circle"></i> 清除
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 批量操作区域 -->
        <div class="batch-actions">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center gap-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="select-all" onchange="toggleSelectAll()">
                            <label class="form-check-label" for="select-all">
                                全选
                            </label>
                        </div>
                        <span class="text-muted" id="selected-count">已选择 0 项</span>
                        <span class="text-muted">共 {{ changes|length }} 项</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end gap-2">
                        <button class="btn btn-success" onclick="batchApprove()" id="batch-approve-btn" disabled>
                            <i class="bi bi-check-all"></i> 批量批准
                            <span class="loading spinner-border spinner-border-sm ms-2" role="status"></span>
                        </button>
                        <button class="btn btn-info" onclick="batchSync()" id="batch-sync-btn">
                            <i class="bi bi-cloud-upload"></i> 同步已批准
                            <span class="loading spinner-border spinner-border-sm ms-2" role="status"></span>
                        </button>
                        <button class="btn btn-warning" onclick="detectChanges()">
                            <i class="bi bi-arrow-clockwise"></i> 检测变更
                            <span class="loading spinner-border spinner-border-sm ms-2" role="status"></span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 进度条 -->
            <div class="progress-container">
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: 0%" id="progress-bar">
                        <span id="progress-text">0%</span>
                    </div>
                </div>
                <small class="text-muted" id="progress-detail">准备中...</small>
            </div>
        </div>

        <!-- 变更列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-check"></i> 待审核变更列表
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-hover mb-0" id="changes-table">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th width="3%">
                                    <input type="checkbox" id="header-select-all" onchange="toggleSelectAll()">
                                </th>
                                <th width="8%">数据源</th>
                                <th width="25%">标题</th>
                                <th width="8%">变更类型</th>
                                <th width="20%">内容预览</th>
                                <th width="12%">创建时间</th>
                                <th width="8%">状态</th>
                                <th width="16%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for change in changes %}
                            <tr class="change-row" 
                                data-source-type="{{ change.source_type }}" 
                                data-change-type="{{ change.change_type }}"
                                data-title="{{ change.title|lower }}"
                                data-content="{{ change.content|lower }}">
                                <td>
                                    <input type="checkbox" class="change-checkbox" value="{{ change.id }}" onchange="updateSelectedCount()">
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ change.source_type }}</span>
                                </td>
                                <td>
                                    <a href="/change/{{ change.id }}" class="text-decoration-none" title="{{ change.title }}">
                                        {{ change.title[:40] }}{% if change.title|length > 40 %}...{% endif %}
                                    </a>
                                </td>
                                <td>
                                    {% if change.change_type == 'create' %}
                                        <span class="badge bg-success">新增</span>
                                    {% elif change.change_type == 'update' %}
                                        <span class="badge bg-warning">更新</span>
                                    {% elif change.change_type == 'delete' %}
                                        <span class="badge bg-danger">删除</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="content-preview" title="{{ change.content }}">
                                        {{ change.content[:80] }}{% if change.content|length > 80 %}...{% endif %}
                                    </div>
                                </td>
                                <td>
                                    <small>{{ change.created_at | datetime_format }}</small>
                                </td>
                                <td>
                                    {% if change.sync_status == 'pending_approval' %}
                                        <span class="badge bg-warning">待审核</span>
                                    {% elif change.sync_status == 'approved' %}
                                        <span class="badge bg-success">已批准</span>
                                    {% elif change.sync_status == 'synced' %}
                                        <span class="badge bg-info">已同步</span>
                                    {% elif change.sync_status == 'failed' %}
                                        <span class="badge bg-danger">失败</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        {% if change.sync_status == 'pending_approval' %}
                                        <button class="btn btn-outline-success" onclick="approveChange({{ change.id }})" title="批准">
                                            <i class="bi bi-check"></i>
                                        </button>
                                        {% endif %}
                                        <a href="/change/{{ change.id }}" class="btn btn-outline-info" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        {% if change.sync_status == 'failed' %}
                                        <button class="btn btn-outline-warning" onclick="retrySync({{ change.id }})" title="重试同步">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if not changes %}
                <div class="text-center py-5">
                    <i class="bi bi-check-circle text-success" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">暂无待处理变更</h5>
                    <p class="text-muted">所有变更都已处理完成</p>
                    <button class="btn btn-primary" onclick="detectChanges()">
                        <i class="bi bi-search"></i> 检测新变更
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notification-toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="bi bi-info-circle text-primary me-2"></i>
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toast-message">
                <!-- 消息内容 -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedChanges = new Set();
        let totalChanges = {{ changes|length }};

        // 显示通知
        function showNotification(message, type = 'info') {
            const toast = document.getElementById('notification-toast');
            const toastMessage = document.getElementById('toast-message');
            const toastHeader = toast.querySelector('.toast-header i');
            
            toastMessage.textContent = message;
            
            // 设置图标和颜色
            toastHeader.className = `bi me-2`;
            if (type === 'success') {
                toastHeader.classList.add('bi-check-circle', 'text-success');
            } else if (type === 'error') {
                toastHeader.classList.add('bi-exclamation-triangle', 'text-danger');
            } else {
                toastHeader.classList.add('bi-info-circle', 'text-primary');
            }
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        // 显示/隐藏加载状态
        function setLoading(button, loading) {
            const spinner = button.querySelector('.loading');
            if (loading) {
                button.disabled = true;
                spinner.style.display = 'inline-block';
            } else {
                button.disabled = false;
                spinner.style.display = 'none';
            }
        }

        // 显示/隐藏进度条
        function showProgress(show, progress = 0, text = '', detail = '') {
            const container = document.querySelector('.progress-container');
            const bar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const progressDetail = document.getElementById('progress-detail');
            
            if (show) {
                container.style.display = 'block';
                bar.style.width = progress + '%';
                progressText.textContent = Math.round(progress) + '%';
                progressDetail.textContent = detail || text;
            } else {
                container.style.display = 'none';
            }
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all') || document.getElementById('header-select-all');
            const checkboxes = document.querySelectorAll('.change-checkbox:not([disabled])');
            
            checkboxes.forEach(cb => {
                cb.checked = selectAll.checked;
                if (selectAll.checked) {
                    selectedChanges.add(parseInt(cb.value));
                } else {
                    selectedChanges.delete(parseInt(cb.value));
                }
            });
            
            updateSelectedCount();
        }

        // 更新选中数量
        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('.change-checkbox:checked');
            selectedChanges.clear();
            
            checkboxes.forEach(cb => {
                selectedChanges.add(parseInt(cb.value));
            });
            
            const count = selectedChanges.size;
            document.getElementById('selected-count').textContent = `已选择 ${count} 项`;
            document.getElementById('batch-approve-btn').disabled = count === 0;
            
            // 更新全选状态
            const visibleCheckboxes = document.querySelectorAll('.change-checkbox:not([disabled])');
            const selectAllCheckbox = document.getElementById('select-all');
            const headerSelectAllCheckbox = document.getElementById('header-select-all');
            
            if (visibleCheckboxes.length > 0) {
                const allChecked = Array.from(visibleCheckboxes).every(cb => cb.checked);
                if (selectAllCheckbox) selectAllCheckbox.checked = allChecked;
                if (headerSelectAllCheckbox) headerSelectAllCheckbox.checked = allChecked;
            }
        }

        // 应用筛选
        function applyFilters() {
            const sourceType = document.getElementById('source-type-filter').value;
            const changeType = document.getElementById('change-type-filter').value;
            const keyword = document.getElementById('keyword-filter').value.toLowerCase();
            
            const rows = document.querySelectorAll('.change-row');
            let visibleCount = 0;
            
            rows.forEach(row => {
                let show = true;
                
                // 数据源类型筛选
                if (sourceType && row.dataset.sourceType !== sourceType) {
                    show = false;
                }
                
                // 变更类型筛选
                if (changeType && row.dataset.changeType !== changeType) {
                    show = false;
                }
                
                // 关键词搜索
                if (keyword) {
                    const title = row.dataset.title || '';
                    const content = row.dataset.content || '';
                    if (!title.includes(keyword) && !content.includes(keyword)) {
                        show = false;
                    }
                }
                
                row.style.display = show ? '' : 'none';
                if (show) visibleCount++;
            });
            
            showNotification(`筛选完成，显示 ${visibleCount} 项结果`, 'info');
            updateSelectedCount();
        }

        // 清除筛选
        function clearFilters() {
            document.getElementById('source-type-filter').value = '';
            document.getElementById('change-type-filter').value = '';
            document.getElementById('keyword-filter').value = '';
            
            document.querySelectorAll('.change-row').forEach(row => {
                row.style.display = '';
            });
            
            showNotification('筛选已清除', 'info');
            updateSelectedCount();
        }

        // 批量批准
        async function batchApprove() {
            if (selectedChanges.size === 0) {
                showNotification('请选择要批准的变更', 'error');
                return;
            }
            
            const button = document.getElementById('batch-approve-btn');
            setLoading(button, true);
            showProgress(true, 0, '准备批准...', `准备批准 ${selectedChanges.size} 项变更`);
            
            try {
                const changeIds = Array.from(selectedChanges);
                let completed = 0;
                
                // 分批处理，每批10个
                const batchSize = 10;
                for (let i = 0; i < changeIds.length; i += batchSize) {
                    const batch = changeIds.slice(i, i + batchSize);
                    
                    const response = await fetch('/api/approve_changes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            change_ids: batch,
                            approved_by: 'admin'
                        })
                    });
                    
                    const result = await response.json();
                    completed += batch.length;
                    
                    const progress = (completed / changeIds.length) * 100;
                    showProgress(true, progress, `批准中...`, `已处理 ${completed}/${changeIds.length} 项`);
                    
                    if (!result.success) {
                        throw new Error(result.error);
                    }
                }
                
                showProgress(false);
                showNotification(`批准完成: 成功处理 ${changeIds.length} 项变更`, 'success');
                setTimeout(() => location.reload(), 2000);
                
            } catch (error) {
                showProgress(false);
                showNotification('批准失败: ' + error.message, 'error');
            } finally {
                setLoading(button, false);
            }
        }

        // 批量同步
        async function batchSync() {
            const button = document.getElementById('batch-sync-btn');
            setLoading(button, true);
            
            try {
                const response = await fetch('/api/sync_approved', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification(`同步完成: 成功${result.data.synced}项, 失败${result.data.failed}项`, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('同步失败: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('同步失败: ' + error.message, 'error');
            } finally {
                setLoading(button, false);
            }
        }

        // 检测变更
        async function detectChanges() {
            const button = event.target.closest('button');
            setLoading(button, true);
            
            try {
                const response = await fetch('/api/detect_changes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification(`检测完成: 新增${result.data.new_items}项, 更新${result.data.updated_items}项, 删除${result.data.deleted_items}项`, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('检测失败: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('检测失败: ' + error.message, 'error');
            } finally {
                setLoading(button, false);
            }
        }

        // 批准单个变更
        async function approveChange(changeId) {
            try {
                const response = await fetch('/api/approve_changes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        change_ids: [changeId],
                        approved_by: 'admin'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('批准成功', 'success');
                    // 更新行状态而不是重新加载页面
                    const row = document.querySelector(`input[value="${changeId}"]`).closest('tr');
                    const statusCell = row.cells[6];
                    statusCell.innerHTML = '<span class="badge bg-success">已批准</span>';
                    
                    // 移除批准按钮
                    const approveBtn = row.querySelector('button[onclick*="approveChange"]');
                    if (approveBtn) {
                        approveBtn.remove();
                    }
                } else {
                    showNotification('批准失败: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('批准失败: ' + error.message, 'error');
            }
        }

        // 重试同步
        async function retrySync(changeId) {
            try {
                const response = await fetch('/api/sync_approved', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        change_ids: [changeId]
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('重试同步成功', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('重试同步失败: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('重试同步失败: ' + error.message, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化选中数量
            updateSelectedCount();
            
            // 为关键词搜索添加回车事件
            document.getElementById('keyword-filter').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    applyFilters();
                }
            });
        });
    </script>
</body>
</html>