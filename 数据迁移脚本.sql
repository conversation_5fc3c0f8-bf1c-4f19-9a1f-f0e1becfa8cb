-- 产品分类数据插入脚本

INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (1, '考勤产品', NULL, 1, '产品分类', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (2, '考勤机', 1, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (3, 'X系列', 2, 3, '系列', 'X系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (4, 'ZK系列', 2, 3, '系列', 'ZK系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (5, 'S系列', 2, 3, '系列', 'S系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (6, '钉钉系列', 2, 3, '系列', '钉钉系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (7, 'TX系列', 2, 3, '系列', 'TX系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (8, 'U系列', 2, 3, '系列', 'U系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (9, 'iClock系列', 2, 3, '系列', 'iClock系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (10, 'WX系列', 2, 3, '系列', 'WX系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (11, 'ZQ系列', 2, 3, '系列', 'ZQ系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (12, 'FS系列', 2, 3, '系列', 'FS系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (13, 'nFace系列', 2, 3, '系列', 'nFace系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (14, 'UF系列', 2, 3, '系列', 'UF系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (15, 'iFace系列', 2, 3, '系列', 'iFace系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (16, 'xFace系列', 2, 3, '系列', 'xFace系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (17, 'HORUS系列', 2, 3, '系列', 'HORUS系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (18, 'BK系列', 2, 3, '系列', 'BK系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (19, 'ZKTeco+系列', 2, 3, '系列', 'ZKTeco+系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (20, 'M系列', 2, 3, '系列', 'M系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (21, 'FA系列', 2, 3, '系列', 'FA系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (22, '门禁产品', NULL, 1, '产品分类', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (23, '门禁一体机', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (24, 'SC系列', 23, 3, '系列', 'SC系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (25, 'MCR系列', 23, 3, '系列', 'MCR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (26, 'M系列', 23, 3, '系列', 'M系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (27, 'X系列', 23, 3, '系列', 'X系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (28, 'Smart系列', 23, 3, '系列', 'Smart系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (29, 'OF系列', 23, 3, '系列', 'OF系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (30, 'F系列', 23, 3, '系列', 'F系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (31, 'ZKTeco+系列', 23, 3, '系列', 'ZKTeco+系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (32, 'WX系列', 23, 3, '系列', 'WX系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (33, 'iClock系列', 23, 3, '系列', 'iClock系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (34, 'iFace系列', 23, 3, '系列', 'iFace系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (35, 'ZF系列', 23, 3, '系列', 'ZF系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (36, 'ZK系列', 23, 3, '系列', 'ZK系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (37, 'UF系列', 23, 3, '系列', 'UF系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (38, 'Xpalm系列', 23, 3, '系列', 'Xpalm系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (39, 'FJ系列', 23, 3, '系列', 'FJ系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (40, 'BioFace系列', 23, 3, '系列', 'BioFace系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (41, 'nFace系列', 23, 3, '系列', 'nFace系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (42, 'zFace系列', 23, 3, '系列', 'zFace系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (43, 'xFace系列', 23, 3, '系列', 'xFace系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (44, 'FS系列', 23, 3, '系列', 'FS系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (45, 'AI系列', 23, 3, '系列', 'AI系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (46, 'MateFace系列', 23, 3, '系列', 'MateFace系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (47, '无线门禁系列', 23, 3, '系列', '无线门禁系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (48, 'T系列非可见光', 23, 3, '系列', 'T系列非可见光', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (49, 'TDB系列', 23, 3, '系列', 'TDB系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (50, 'T系列可见光', 23, 3, '系列', 'T系列可见光', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (51, '机械锁', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (52, 'UX系列', 51, 3, '系列', 'UX系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (53, '采集器', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (54, 'ZK系列', 53, 3, '系列', 'ZK系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (55, 'CR系列', 53, 3, '系列', 'CR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (56, 'MC系列', 53, 3, '系列', 'MC系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (57, 'GM系列', 53, 3, '系列', 'GM系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (58, '发卡器', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (59, 'CR系列', 58, 3, '系列', 'CR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (60, '读卡器', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (61, 'KR系列', 60, 3, '系列', 'KR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (62, 'FR系列', 60, 3, '系列', 'FR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (63, 'KD系列', 60, 3, '系列', 'KD系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (64, 'QR系列', 60, 3, '系列', 'QR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (65, 'KQR系列', 60, 3, '系列', 'KQR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (66, 'ZR系列', 60, 3, '系列', 'ZR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (67, 'KM系列', 60, 3, '系列', 'KM系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (68, 'FI系列', 60, 3, '系列', 'FI系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (69, '防爆读头系列', 60, 3, '系列', '防爆读头系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (70, 'GR系列', 60, 3, '系列', 'GR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (71, '一维扫描枪', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (72, 'ZK系列', 71, 3, '系列', 'ZK系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (73, '二维扫描枪', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (74, 'ZK系列', 73, 3, '系列', 'ZK系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (75, '二维扫描平台', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (76, 'ZK系列', 75, 3, '系列', 'ZK系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (77, '条码扫描器', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (78, 'ZK系列', 77, 3, '系列', 'ZK系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (79, '门禁控制器', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (80, 'C3系列', 79, 3, '系列', 'C3系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (81, 'C4系列', 79, 3, '系列', 'C4系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (82, 'InBio系列', 79, 3, '系列', 'InBio系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (83, 'K2系列', 79, 3, '系列', 'K2系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (84, 'ZTHCAM系列', 79, 3, '系列', 'ZTHCAM系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (85, 'ZTHCAMPRO系列', 79, 3, '系列', 'ZTHCAMPRO系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (86, 'C5系列', 79, 3, '系列', 'C5系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (87, 'InBioP系列', 79, 3, '系列', 'InBioP系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (88, '控制器配件', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (89, 'ZKPSM系列', 88, 3, '系列', 'ZKPSM系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (90, 'TPM系列', 88, 3, '系列', 'TPM系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (91, 'CASE系列铁箱', 88, 3, '系列', 'CASE系列铁箱', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (92, '门禁线性电源', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (93, 'AP系列', 92, 3, '系列', 'AP系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (94, '电插锁', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (95, 'AL系列', 94, 3, '系列', 'AL系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (96, 'CL系列', 94, 3, '系列', 'CL系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (97, '电磁锁', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (98, 'AL系列', 97, 3, '系列', 'AL系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (99, 'ZL系列', 97, 3, '系列', 'ZL系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (100, 'CL系列', 97, 3, '系列', 'CL系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (101, '防爆电锁', 97, 3, '系列', '防爆电锁', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (102, '电锁配件', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (103, 'AL系列配件', 102, 3, '系列', 'AL系列配件', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (104, 'ZL系列配件', 102, 3, '系列', 'ZL系列配件', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (105, 'CL系列配件', 102, 3, '系列', 'CL系列配件', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (106, '电控锁', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (107, 'EL系列', 106, 3, '系列', 'EL系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (108, '出门开关', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (109, 'EX-802', 108, 3, '系列', 'EX-802', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (110, 'K1系列', 108, 3, '系列', 'K1系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (111, 'K2系列', 108, 3, '系列', 'K2系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (112, 'K5系列', 108, 3, '系列', 'K5系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (113, 'iB1', 108, 3, '系列', 'iB1', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (114, 'Fex119系列', 108, 3, '系列', 'Fex119系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (115, 'BUT系列', 108, 3, '系列', 'BUT系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (116, '防爆出门开关', 108, 3, '系列', '防爆出门开关', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (117, '配件', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (118, 'TDM95系列', 117, 3, '系列', 'TDM95系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (119, '立柱支架', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (120, 'MateFace20-L', 119, 3, '系列', 'MateFace20-L', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (121, 'PZ-11', 119, 3, '系列', 'PZ-11', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (122, '门禁梯控', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (123, 'EC系列', 122, 3, '系列', 'EC系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (124, '门禁控制器铁箱', 22, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (125, 'CASE系列铁箱', 124, 3, '系列', 'CASE系列铁箱', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (126, '软件', NULL, 1, '产品分类', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (127, '软件', 126, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (128, 'ZKTime微服务器', 127, 3, '系列', 'ZKTime微服务器', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (129, 'E-ZKEco Pro微服务器', 127, 3, '系列', 'E-ZKEco Pro微服务器', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (130, '熵基互联', 127, 3, '系列', '熵基互联', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (131, '熵基云商', 127, 3, '系列', '熵基云商', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (132, '万傲瑞达', 127, 3, '系列', '万傲瑞达', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (133, '车行产品', NULL, 1, '产品分类', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (134, '自动道闸', 133, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (135, 'PBL系列', 134, 3, '系列', 'PBL系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (136, 'PBH系列', 134, 3, '系列', 'PBH系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (137, 'ZK-BAR系列', 134, 3, '系列', 'ZK-BAR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (138, 'Z-C系列', 134, 3, '系列', 'Z-C系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (139, '广告道闸', 133, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (140, 'PBG系列', 139, 3, '系列', 'PBG系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (141, '空降闸', 133, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (142, 'PBK系列', 141, 3, '系列', 'PBK系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (143, '车牌识别
道闸一体机', 133, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (144, 'LPR-PB系列', 143, 3, '系列', 'LPR-PB系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (145, '车牌识别一体机', 133, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (146, 'LPR系列', 145, 3, '系列', 'LPR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (147, 'DPR系列', 145, 3, '系列', 'DPR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (148, 'Z-C系列', 145, 3, '系列', 'Z-C系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (149, '无人值守自助终端', 133, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (150, 'SH系列', 149, 3, '系列', 'SH系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (151, '充电桩', 133, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (152, 'CP7系列', 151, 3, '系列', 'CP7系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (153, 'CP8系列', 151, 3, '系列', 'CP8系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (154, '车位管理相机', 133, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (155, 'CP-IPC系列', 154, 3, '系列', 'CP-IPC系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (156, '车位锁 ', 133, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (157, 'CWS-RS系列', 156, 3, '系列', 'CWS-RS系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (158, '车位引导', 133, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (159, 'PG-VD系列', 158, 3, '系列', 'PG-VD系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (160, 'UD系列', 158, 3, '系列', 'UD系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (161, 'PG-UD系列', 158, 3, '系列', 'PG-UD系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (162, 'PG-LED系列', 158, 3, '系列', 'PG-LED系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (163, 'PG-MC系列', 158, 3, '系列', 'PG-MC系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (164, '周边配件', 133, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (165, 'PT系列', 164, 3, '系列', 'PT系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (166, 'ZK-RD系列', 164, 3, '系列', 'ZK-RD系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (167, 'ETC系列', 164, 3, '系列', 'ETC系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (168, '剩余车位显示屏系列', 164, 3, '系列', '剩余车位显示屏系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (169, '车辆检测器系列', 164, 3, '系列', '车辆检测器系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (170, '地感线圈系列', 164, 3, '系列', '地感线圈系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (171, '智能锁产品', NULL, 1, '产品分类', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (172, '智能家用办公锁', 171, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (173, 'ZM系列', 172, 3, '系列', 'ZM系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (174, 'P2系列', 172, 3, '系列', 'P2系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (175, 'K200系列', 172, 3, '系列', 'K200系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (176, '配件', 171, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (177, '配件', 176, 3, '系列', '配件', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (178, '集中管理锁', 171, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (179, 'P3系列', 178, 3, '系列', 'P3系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (180, '单机版酒店锁', 171, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (181, 'LH系列', 180, 3, '系列', 'LH系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (182, '发卡器', 171, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (183, 'CR系列', 182, 3, '系列', 'CR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (184, '玻璃门锁', 171, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (185, 'GDL系列', 184, 3, '系列', 'GDL系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (186, '人证产品', NULL, 1, '产品分类', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (187, '生物识别采集器', 186, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (188, '指纹系列', 187, 3, '系列', '指纹系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (189, '手掌系列', 187, 3, '系列', '手掌系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (190, '指静脉系列', 187, 3, '系列', '指静脉系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (191, '生物识别模组', 186, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (192, '指纹系列', 191, 3, '系列', '指纹系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (193, '手掌系列', 191, 3, '系列', '手掌系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (194, '面部系列', 191, 3, '系列', '面部系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (195, '指静脉系列', 191, 3, '系列', '指静脉系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (196, '人证核验产品', 186, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (197, '桌面式', 196, 3, '系列', '桌面式', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (198, '访客系列', 196, 3, '系列', '访客系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (199, '立式', 196, 3, '系列', '立式', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (200, '壁挂式', 196, 3, '系列', '壁挂式', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (201, '手持系列', 196, 3, '系列', '手持系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (202, '身份证阅读机具', 186, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (203, '台式ID系列', 202, 3, '系列', '台式ID系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (204, '内置式IDM系列', 202, 3, '系列', '内置式IDM系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (205, '多功能系列', 202, 3, '系列', '多功能系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (206, 'nan', NULL, 1, '产品分类', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (207, '人证核验产品', 206, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (208, '桌面式', 207, 3, '系列', '桌面式', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (209, '安检产品', NULL, 1, '产品分类', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (210, '安检机', 209, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (211, '5030系列', 210, 3, '系列', '5030系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (212, '6550系列', 210, 3, '系列', '6550系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (213, '10080系列', 210, 3, '系列', '10080系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (214, '安检门', 209, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (215, '金属安检门系列', 214, 3, '系列', '金属安检门系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (216, '分类安检门系列', 214, 3, '系列', '分类安检门系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (217, '通道安检门系列', 214, 3, '系列', '通道安检门系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (218, '安检棒', 209, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (219, '手持金属检测系列', 218, 3, '系列', '手持金属检测系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (220, '升降柱', 209, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (221, '液压升降柱系列', 220, 3, '系列', '液压升降柱系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (222, '固定柱系列', 220, 3, '系列', '固定柱系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (223, '路障机', 209, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (224, '液压路障机系列', 223, 3, '系列', '液压路障机系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (225, '车底检查系统', 209, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (226, '固定车底系列', 225, 3, '系列', '固定车底系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (227, '移动车底系列', 225, 3, '系列', '移动车底系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (228, '防爆产品', 209, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (229, '防爆毯系列', 228, 3, '系列', '防爆毯系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (230, '防爆罐系列', 228, 3, '系列', '防爆罐系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (231, '危险液体检查仪', 209, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (232, '手持系列', 231, 3, '系列', '手持系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (233, '台式系列', 231, 3, '系列', '台式系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (234, '磁性探测立柱', 209, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (235, '铁磁探测系列', 234, 3, '系列', '铁磁探测系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (236, '鞋底金属探测器', 209, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (237, '鞋底探测系列', 236, 3, '系列', '鞋底探测系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (238, '视频产品', NULL, 1, '产品分类', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (239, '网络摄像机', 238, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (240, 'BL系列', 239, 3, '系列', 'BL系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (241, 'BS系列', 239, 3, '系列', 'BS系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (242, 'ES系列', 239, 3, '系列', 'ES系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (243, '网络硬盘录像机', 238, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (244, '网络硬盘录像机', 243, 3, '系列', '网络硬盘录像机', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (245, '智能视频盒', 238, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (246, 'IVS系列', 245, 3, '系列', 'IVS系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (247, '消费产品', NULL, 1, '产品分类', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (248, '消费机', 247, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (249, 'CM离线消费系列', 248, 3, '系列', 'CM离线消费系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (250, 'ZTHP离线消费系列', 248, 3, '系列', 'ZTHP离线消费系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (251, 'CM在线消费系列', 248, 3, '系列', 'CM在线消费系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (252, 'ZTHP在线消费系列', 248, 3, '系列', 'ZTHP在线消费系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (253, '消费产品辅件', 247, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (254, '充值机', 253, 3, '系列', '充值机', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (255, '消费机支架', 253, 3, '系列', '消费机支架', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (256, '通道产品', NULL, 1, '产品分类', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (257, '三辊闸', 256, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (258, 'TS系列', 257, 3, '系列', 'TS系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (259, 'TSM系列', 257, 3, '系列', 'TSM系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (260, '翼闸', 256, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (261, 'FBL系列', 260, 3, '系列', 'FBL系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (262, 'FBM系列', 260, 3, '系列', 'FBM系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (263, '摆闸', 256, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (264, 'SBTL系列', 263, 3, '系列', 'SBTL系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (265, 'SBTM系列', 263, 3, '系列', 'SBTM系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (266, 'Mars系列', 263, 3, '系列', 'Mars系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (267, 'SBTH系列', 263, 3, '系列', 'SBTH系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (268, '转闸', 256, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (269, 'FHT系列', 268, 3, '系列', 'FHT系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (270, '广告门', 256, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (271, 'ADR系列', 270, 3, '系列', 'ADR系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (272, '开门机', 256, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (273, 'ASD100系列', 272, 3, '系列', 'ASD100系列', '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (274, '自动伸缩门', 256, 2, '类别', NULL, '进行中');


INSERT INTO ProductCategory (id, name, parent_id, level, category_type, series_name, collection_status) 
VALUES (275, '自动伸缩门系列', 274, 3, '系列', '自动伸缩门系列', '进行中');


-- 文档类型数据插入脚本

INSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) 
VALUES (1, '彩页', '产品宣传彩页', '彩页', True, 5, '.pdf,.doc,.docx', 10485760, TRUE);


INSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) 
VALUES (2, '入门指南', '产品入门指南', '入门指南', True, 4, '.pdf,.doc,.docx', 10485760, TRUE);


INSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) 
VALUES (3, '用户手册', '产品用户手册', '用户手册', True, 3, '.pdf,.doc,.docx', 52428800, TRUE);


INSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) 
VALUES (4, '技术文档', '技术规格文档', '技术文档', False, 2, '.pdf,.doc,.docx', 52428800, TRUE);


INSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) 
VALUES (5, '宣传资料', '产品宣传资料', '宣传资料', False, 1, '.pdf,.doc,.docx,.ppt,.pptx', 52428800, TRUE);


INSERT INTO DocumentType (id, name, description, document_category, is_required, priority, file_extensions, max_file_size, review_required) 
VALUES (6, '培训资料', '产品培训资料', '培训资料', False, 1, '.pdf,.doc,.docx,.ppt,.pptx', 104857600, TRUE);
