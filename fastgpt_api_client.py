#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastGPT API客户端
功能：
1. 连接FastGPT知识库
2. 写入索引数据
3. 管理数据集
4. 批量同步操作
"""

import aiohttp
import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import hashlib
import time

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class FastGPTDataItem:
    """FastGPT数据项"""
    q: str  # 问题/标题
    a: str  # 答案/内容
    indexes: List[Dict[str, Any]]  # 索引数据
    metadata: Dict[str, Any]  # 元数据
    tags: List[str]  # 标签
    dataId: str = None  # 数据ID
    parentId: str = None  # 父级ID
    
    def __post_init__(self):
        if self.dataId is None:
            # 生成唯一ID
            content_hash = hashlib.md5(
                f"{self.q}{self.a}{json.dumps(self.metadata, sort_keys=True)}".encode('utf-8')
            ).hexdigest()[:16]
            self.dataId = f"idx_{content_hash}"

@dataclass
class SyncResult:
    """同步结果"""
    success: bool
    data_id: str
    message: str
    response_data: Dict[str, Any] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

class FastGPTAPIClient:
    """FastGPT API客户端"""
    
    def __init__(self, api_url: str, api_key: str, dataset_id: str):
        """
        初始化FastGPT客户端
        
        Args:
            api_url: FastGPT API地址
            api_key: API密钥
            dataset_id: 数据集ID
        """
        self.api_url = api_url.rstrip('/')
        self.api_key = api_key
        self.dataset_id = dataset_id
        
        # 请求配置
        self.timeout = 30
        self.max_retries = 3
        self.retry_delay = 1
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_data_items': 0,
            'successful_syncs': 0,
            'failed_syncs': 0,
            'start_time': datetime.now()
        }
        
        # 会话对象
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'SiliconFlow-IndexGenerator/1.0'
        }
    
    async def test_connection(self) -> Tuple[bool, str]:
        """
        测试连接
        
        Returns:
            (是否成功, 消息)
        """
        try:
            url = f"{self.api_url}/core/dataset/detail"
            headers = self.get_headers()
            
            params = {'datasetId': self.dataset_id}
            
            logger.info(f"测试FastGPT连接: {url}")
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    dataset_name = data.get('data', {}).get('name', '未知')
                    logger.info(f"连接成功 - 数据集: {dataset_name}")
                    return True, f"连接成功，数据集: {dataset_name}"
                else:
                    error_text = await response.text()
                    logger.error(f"连接失败 - 状态码: {response.status}, 错误: {error_text}")
                    return False, f"连接失败: {response.status} - {error_text}"
                    
        except Exception as e:
            logger.error(f"连接测试异常: {str(e)}")
            return False, f"连接异常: {str(e)}"
    
    async def insert_data_item(self, data_item: FastGPTDataItem) -> SyncResult:
        """
        插入单个数据项
        
        Args:
            data_item: 数据项
            
        Returns:
            同步结果
        """
        try:
            url = f"{self.api_url}/core/dataset/data/insertData"
            headers = self.get_headers()
            
            # 构建请求数据
            payload = {
                'datasetId': self.dataset_id,
                'parentId': data_item.parentId,
                'q': data_item.q,
                'a': data_item.a,
                'indexes': data_item.indexes,
                'metadata': data_item.metadata,
                'tags': data_item.tags
            }
            
            logger.info(f"插入数据项: {data_item.q[:50]}...")
            
            # 执行请求（带重试）
            for attempt in range(self.max_retries):
                try:
                    self.stats['total_requests'] += 1
                    
                    async with self.session.post(url, headers=headers, json=payload) as response:
                        response_data = await response.json()
                        
                        if response.status == 200:
                            self.stats['successful_requests'] += 1
                            self.stats['successful_syncs'] += 1
                            
                            logger.info(f"数据项插入成功: {data_item.dataId}")
                            
                            return SyncResult(
                                success=True,
                                data_id=data_item.dataId,
                                message="插入成功",
                                response_data=response_data
                            )
                        else:
                            error_msg = response_data.get('message', '未知错误')
                            logger.warning(f"插入失败 (尝试 {attempt + 1}/{self.max_retries}): {error_msg}")
                            
                            if attempt == self.max_retries - 1:
                                self.stats['failed_requests'] += 1
                                self.stats['failed_syncs'] += 1
                                
                                return SyncResult(
                                    success=False,
                                    data_id=data_item.dataId,
                                    message=f"插入失败: {error_msg}",
                                    response_data=response_data
                                )
                            
                            # 等待后重试
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            
                except aiohttp.ClientError as e:
                    logger.warning(f"网络错误 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                    
                    if attempt == self.max_retries - 1:
                        self.stats['failed_requests'] += 1
                        self.stats['failed_syncs'] += 1
                        
                        return SyncResult(
                            success=False,
                            data_id=data_item.dataId,
                            message=f"网络错误: {str(e)}"
                        )
                    
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                    
        except Exception as e:
            logger.error(f"插入数据项异常: {str(e)}")
            self.stats['failed_syncs'] += 1
            
            return SyncResult(
                success=False,
                data_id=data_item.dataId,
                message=f"插入异常: {str(e)}"
            )
    
    async def batch_insert_data(self, data_items: List[FastGPTDataItem], 
                               batch_size: int = 5, 
                               delay_between_batches: float = 1.0) -> List[SyncResult]:
        """
        批量插入数据
        
        Args:
            data_items: 数据项列表
            batch_size: 批次大小
            delay_between_batches: 批次间延迟（秒）
            
        Returns:
            同步结果列表
        """
        logger.info(f"开始批量插入 {len(data_items)} 个数据项，批次大小: {batch_size}")
        
        results = []
        self.stats['total_data_items'] += len(data_items)
        
        # 分批处理
        for i in range(0, len(data_items), batch_size):
            batch = data_items[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(data_items) + batch_size - 1) // batch_size
            
            logger.info(f"处理批次 {batch_num}/{total_batches}，包含 {len(batch)} 个数据项")
            
            # 并发处理当前批次
            batch_tasks = [self.insert_data_item(item) for item in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # 处理结果
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    logger.error(f"批次处理异常: {str(result)}")
                    results.append(SyncResult(
                        success=False,
                        data_id=batch[j].dataId,
                        message=f"处理异常: {str(result)}"
                    ))
                else:
                    results.append(result)
            
            # 批次间延迟
            if i + batch_size < len(data_items):
                await asyncio.sleep(delay_between_batches)
        
        # 统计结果
        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful
        
        logger.info(f"批量插入完成 - 成功: {successful}, 失败: {failed}")
        
        return results
    
    async def update_data_item(self, data_id: str, data_item: FastGPTDataItem) -> SyncResult:
        """
        更新数据项
        
        Args:
            data_id: 数据ID
            data_item: 新的数据项
            
        Returns:
            同步结果
        """
        try:
            url = f"{self.api_url}/core/dataset/data/updateData"
            headers = self.get_headers()
            
            payload = {
                'dataId': data_id,
                'q': data_item.q,
                'a': data_item.a,
                'indexes': data_item.indexes,
                'metadata': data_item.metadata,
                'tags': data_item.tags
            }
            
            logger.info(f"更新数据项: {data_id}")
            
            async with self.session.put(url, headers=headers, json=payload) as response:
                response_data = await response.json()
                
                if response.status == 200:
                    logger.info(f"数据项更新成功: {data_id}")
                    return SyncResult(
                        success=True,
                        data_id=data_id,
                        message="更新成功",
                        response_data=response_data
                    )
                else:
                    error_msg = response_data.get('message', '未知错误')
                    logger.error(f"更新失败: {error_msg}")
                    return SyncResult(
                        success=False,
                        data_id=data_id,
                        message=f"更新失败: {error_msg}",
                        response_data=response_data
                    )
                    
        except Exception as e:
            logger.error(f"更新数据项异常: {str(e)}")
            return SyncResult(
                success=False,
                data_id=data_id,
                message=f"更新异常: {str(e)}"
            )
    
    async def delete_data_item(self, data_id: str) -> SyncResult:
        """
        删除数据项
        
        Args:
            data_id: 数据ID
            
        Returns:
            同步结果
        """
        try:
            url = f"{self.api_url}/core/dataset/data/delDataById"
            headers = self.get_headers()
            
            payload = {'dataId': data_id}
            
            logger.info(f"删除数据项: {data_id}")
            
            async with self.session.delete(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    logger.info(f"数据项删除成功: {data_id}")
                    return SyncResult(
                        success=True,
                        data_id=data_id,
                        message="删除成功"
                    )
                else:
                    error_text = await response.text()
                    logger.error(f"删除失败: {error_text}")
                    return SyncResult(
                        success=False,
                        data_id=data_id,
                        message=f"删除失败: {error_text}"
                    )
                    
        except Exception as e:
            logger.error(f"删除数据项异常: {str(e)}")
            return SyncResult(
                success=False,
                data_id=data_id,
                message=f"删除异常: {str(e)}"
            )
    
    async def search_data(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        搜索数据
        
        Args:
            query: 搜索查询
            limit: 结果限制
            
        Returns:
            搜索结果
        """
        try:
            url = f"{self.api_url}/core/dataset/searchTest"
            headers = self.get_headers()
            
            payload = {
                'datasetId': self.dataset_id,
                'text': query,
                'limit': limit,
                'similarity': 0.5
            }
            
            logger.info(f"搜索数据: {query}")
            
            async with self.session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"搜索成功，返回 {len(result.get('list', []))} 个结果")
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"搜索失败: {error_text}")
                    return {'error': error_text}
                    
        except Exception as e:
            logger.error(f"搜索异常: {str(e)}")
            return {'error': str(e)}
    
    async def get_dataset_info(self) -> Dict[str, Any]:
        """
        获取数据集信息
        
        Returns:
            数据集信息
        """
        try:
            url = f"{self.api_url}/core/dataset/detail"
            headers = self.get_headers()
            
            params = {'datasetId': self.dataset_id}
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('data', {})
                else:
                    error_text = await response.text()
                    logger.error(f"获取数据集信息失败: {error_text}")
                    return {'error': error_text}
                    
        except Exception as e:
            logger.error(f"获取数据集信息异常: {str(e)}")
            return {'error': str(e)}
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息
        """
        runtime = datetime.now() - self.stats['start_time']
        
        return {
            'runtime_seconds': runtime.total_seconds(),
            'total_requests': self.stats['total_requests'],
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'request_success_rate': (self.stats['successful_requests'] / max(self.stats['total_requests'], 1)) * 100,
            'total_data_items': self.stats['total_data_items'],
            'successful_syncs': self.stats['successful_syncs'],
            'failed_syncs': self.stats['failed_syncs'],
            'sync_success_rate': (self.stats['successful_syncs'] / max(self.stats['total_data_items'], 1)) * 100,
            'requests_per_minute': (self.stats['total_requests'] / max(runtime.total_seconds() / 60, 1))
        }
    
    def create_data_item_from_index(self, index_data: Dict[str, Any]) -> FastGPTDataItem:
        """
        从索引数据创建FastGPT数据项
        
        Args:
            index_data: 索引数据
            
        Returns:
            FastGPT数据项
        """
        # 构建索引列表
        indexes = [
            {
                'text': index_data.get('vector_content', index_data.get('content', '')),
                'dataId': index_data.get('id', '')
            }
        ]
        
        return FastGPTDataItem(
            q=index_data.get('title', ''),
            a=index_data.get('content', ''),
            indexes=indexes,
            metadata=index_data.get('metadata', {}),
            tags=index_data.get('tags', []),
            dataId=index_data.get('id')
        )

# 使用示例
async def main():
    """主函数示例"""
    # FastGPT配置
    fastgpt_config = {
        'api_url': 'https://api.fastgpt.in/api',
        'api_key': 'your-fastgpt-api-key',
        'dataset_id': 'your-dataset-id'
    }
    
    # 创建客户端
    async with FastGPTAPIClient(**fastgpt_config) as client:
        # 测试连接
        success, message = await client.test_connection()
        print(f"连接测试: {message}")
        
        if success:
            # 创建示例数据项
            sample_data = FastGPTDataItem(
                q="ZK-AC2000门禁控制器规格",
                a="双门门禁控制器，支持TCP/IP通讯，内置Web服务器...",
                indexes=[
                    {
                        'text': '门禁控制器 ZK-AC2000 双门 TCP/IP 规格参数',
                        'dataId': 'idx_sample_001'
                    }
                ],
                metadata={
                    'product_name': 'ZK-AC2000',
                    'business_block': '门禁系统',
                    'category': '门禁控制器'
                },
                tags=['门禁', '控制器', 'TCP/IP', '双门']
            )
            
            # 插入数据
            result = await client.insert_data_item(sample_data)
            print(f"插入结果: {result.message}")
            
            # 获取统计信息
            stats = client.get_statistics()
            print(f"统计信息: {stats}")

if __name__ == "__main__":
    asyncio.run(main())