<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>同步日志 - FastGPT知识库同步管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .filter-section {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .log-message {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .error-details {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 0.25rem;
            max-height: 200px;
            overflow-y: auto;
        }
        .table-container {
            max-height: 70vh;
            overflow-y: auto;
        }
        .status-success {
            color: #198754;
        }
        .status-error {
            color: #dc3545;
        }
        .status-warning {
            color: #fd7e14;
        }
        .pagination-container {
            position: sticky;
            bottom: 0;
            background: white;
            border-top: 1px solid #dee2e6;
            padding: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-database"></i> FastGPT知识库同步管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="bi bi-house"></i> 首页
                </a>
                <a class="nav-link" href="/batch_review">
                    <i class="bi bi-check2-square"></i> 批量审核
                </a>
                <a class="nav-link active" href="/sync_logs">
                    <i class="bi bi-journal-text"></i> 同步日志
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 筛选区域 -->
        <div class="filter-section">
            <form method="GET" action="/sync_logs">
                <div class="row">
                    <div class="col-md-2">
                        <label class="form-label">操作类型</label>
                        <select class="form-select" name="operation">
                            <option value="">全部</option>
                            <option value="detect_changes" {{ 'selected' if request.args.get('operation') == 'detect_changes' }}>检测变更</option>
                            <option value="approve_changes" {{ 'selected' if request.args.get('operation') == 'approve_changes' }}>批准变更</option>
                            <option value="sync_to_fastgpt" {{ 'selected' if request.args.get('operation') == 'sync_to_fastgpt' }}>同步到FastGPT</option>
                            <option value="create_data" {{ 'selected' if request.args.get('operation') == 'create_data' }}>创建数据</option>
                            <option value="update_data" {{ 'selected' if request.args.get('operation') == 'update_data' }}>更新数据</option>
                            <option value="delete_data" {{ 'selected' if request.args.get('operation') == 'delete_data' }}>删除数据</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status">
                            <option value="">全部</option>
                            <option value="success" {{ 'selected' if request.args.get('status') == 'success' }}>成功</option>
                            <option value="error" {{ 'selected' if request.args.get('status') == 'error' }}>失败</option>
                            <option value="warning" {{ 'selected' if request.args.get('status') == 'warning' }}>警告</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">开始日期</label>
                        <input type="date" class="form-control" name="start_date" value="{{ request.args.get('start_date', '') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">结束日期</label>
                        <input type="date" class="form-control" name="end_date" value="{{ request.args.get('end_date', '') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">关键词</label>
                        <input type="text" class="form-control" name="keyword" placeholder="搜索消息..." value="{{ request.args.get('keyword', '') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">操作</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> 筛选
                            </button>
                            <a href="/sync_logs" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> 清除
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-check-circle fs-1"></i>
                        <h4 class="mt-2">{{ stats.success_count or 0 }}</h4>
                        <p class="mb-0">成功</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-exclamation-triangle fs-1"></i>
                        <h4 class="mt-2">{{ stats.error_count or 0 }}</h4>
                        <p class="mb-0">失败</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <i class="bi bi-exclamation-circle fs-1"></i>
                        <h4 class="mt-2">{{ stats.warning_count or 0 }}</h4>
                        <p class="mb-0">警告</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-journal-text fs-1"></i>
                        <h4 class="mt-2">{{ stats.total_count or 0 }}</h4>
                        <p class="mb-0">总计</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="mb-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-journal-text"></i> 同步日志
                    <span class="badge bg-secondary">{{ logs|length }} 条记录</span>
                </h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-danger btn-sm" onclick="clearOldLogs()">
                        <i class="bi bi-trash"></i> 清理旧日志
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="exportLogs()">
                        <i class="bi bi-download"></i> 导出日志
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="refreshLogs()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
            </div>
        </div>

        <!-- 日志列表 -->
        <div class="card">
            <div class="card-body p-0">
                {% if logs %}
                <div class="table-container">
                    <table class="table table-hover mb-0">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th width="12%">时间</th>
                                <th width="10%">操作</th>
                                <th width="8%">状态</th>
                                <th width="25%">消息</th>
                                <th width="15%">变更ID</th>
                                <th width="15%">FastGPT ID</th>
                                <th width="15%">详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in logs %}
                            <tr>
                                <td>
                                    <small>{{ log.created_at | datetime_format }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ log.operation }}</span>
                                </td>
                                <td>
                                    {% if log.status == 'success' %}
                                        <i class="bi bi-check-circle status-success"></i>
                                        <span class="badge bg-success">成功</span>
                                    {% elif log.status == 'error' %}
                                        <i class="bi bi-exclamation-triangle status-error"></i>
                                        <span class="badge bg-danger">失败</span>
                                    {% elif log.status == 'warning' %}
                                        <i class="bi bi-exclamation-circle status-warning"></i>
                                        <span class="badge bg-warning">警告</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ log.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="log-message" title="{{ log.message }}">
                                        {{ log.message }}
                                    </div>
                                </td>
                                <td>
                                    {% if log.change_id %}
                                        <a href="/change/{{ log.change_id }}" class="text-decoration-none">
                                            <code>{{ log.change_id }}</code>
                                        </a>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if log.fastgpt_id %}
                                        <code class="small">{{ log.fastgpt_id }}</code>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if log.error_details %}
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="showErrorDetails('{{ log.id }}', `{{ log.error_details | replace('`', '\\`') | replace('\n', '\\n') }}`)">
                                            <i class="bi bi-exclamation-triangle"></i> 错误详情
                                        </button>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                {% if pagination %}
                <div class="pagination-container">
                    <nav>
                        <ul class="pagination justify-content-center mb-0">
                            {% if pagination.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('sync_logs', page=pagination.prev_num, **request.args) }}">
                                        <i class="bi bi-chevron-left"></i> 上一页
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('sync_logs', page=page_num, **request.args) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('sync_logs', page=pagination.next_num, **request.args) }}">
                                        下一页 <i class="bi bi-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - 
                            {{ min(pagination.page * pagination.per_page, pagination.total) }} 条，
                            共 {{ pagination.total }} 条记录
                        </small>
                    </div>
                </div>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-journal-x text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">暂无日志记录</h5>
                    <p class="text-muted">没有找到符合条件的同步日志</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 错误详情模态框 -->
    <div class="modal fade" id="errorDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle text-danger"></i> 错误详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="error-details" id="error-details-content">
                        <!-- 错误详情内容 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" onclick="copyErrorDetails()">
                        <i class="bi bi-clipboard"></i> 复制
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notification-toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="bi bi-info-circle text-primary me-2"></i>
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toast-message">
                <!-- 消息内容 -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentErrorDetails = '';

        // 显示通知
        function showNotification(message, type = 'info') {
            const toast = document.getElementById('notification-toast');
            const toastMessage = document.getElementById('toast-message');
            const toastHeader = toast.querySelector('.toast-header i');
            
            toastMessage.textContent = message;
            
            // 设置图标和颜色
            toastHeader.className = `bi me-2`;
            if (type === 'success') {
                toastHeader.classList.add('bi-check-circle', 'text-success');
            } else if (type === 'error') {
                toastHeader.classList.add('bi-exclamation-triangle', 'text-danger');
            } else {
                toastHeader.classList.add('bi-info-circle', 'text-primary');
            }
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        // 显示错误详情
        function showErrorDetails(logId, errorDetails) {
            currentErrorDetails = errorDetails;
            document.getElementById('error-details-content').textContent = errorDetails;
            
            const modal = new bootstrap.Modal(document.getElementById('errorDetailsModal'));
            modal.show();
        }

        // 复制错误详情
        function copyErrorDetails() {
            if (currentErrorDetails) {
                navigator.clipboard.writeText(currentErrorDetails).then(() => {
                    showNotification('错误详情已复制到剪贴板', 'success');
                }).catch(() => {
                    showNotification('复制失败', 'error');
                });
            }
        }

        // 清理旧日志
        async function clearOldLogs() {
            if (!confirm('确定要清理30天前的旧日志吗？此操作不可撤销。')) {
                return;
            }
            
            try {
                const response = await fetch('/api/clear_old_logs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification(`清理完成: 删除了 ${result.data.deleted_count} 条旧日志`, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('清理失败: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('清理失败: ' + error.message, 'error');
            }
        }

        // 导出日志
        function exportLogs() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'csv');
            
            const exportUrl = '/sync_logs?' + params.toString();
            window.open(exportUrl, '_blank');
            
            showNotification('正在导出日志...', 'info');
        }

        // 刷新日志
        function refreshLogs() {
            location.reload();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 自动设置默认日期范围（最近7天）
            const startDateInput = document.querySelector('input[name="start_date"]');
            const endDateInput = document.querySelector('input[name="end_date"]');
            
            if (!startDateInput.value && !endDateInput.value) {
                const today = new Date();
                const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                
                endDateInput.value = today.toISOString().split('T')[0];
                startDateInput.value = weekAgo.toISOString().split('T')[0];
            }
        });
    </script>
</body>
</html>