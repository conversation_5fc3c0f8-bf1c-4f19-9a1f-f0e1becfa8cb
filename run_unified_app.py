#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一应用启动脚本
检查环境、安装依赖、启动Streamlit应用
"""

import os
import sys
import subprocess
import logging
import time
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    
    logger.info(f"Python版本: {sys.version}")
    return True

def check_and_install_dependencies():
    """检查并安装依赖"""
    # 优先使用简化版本的requirements文件
    requirements_file = Path("requirements_simple.txt")

    if not requirements_file.exists():
        requirements_file = Path("requirements_unified.txt")
        if not requirements_file.exists():
            logger.error("requirements文件不存在")
            return False
    
    logger.info("检查依赖包...")
    
    try:
        # 检查是否有pip
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        
        # 安装依赖
        logger.info("安装依赖包...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"依赖安装失败: {result.stderr}")
            return False
        
        logger.info("依赖安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"依赖检查失败: {e}")
        return False

def check_configuration():
    """检查配置"""
    try:
        from unified_config import config_manager
        
        # 创建必要的目录
        config_manager.create_directories()
        
        # 验证配置
        validation_results = config_manager.validate_config()
        
        logger.info("配置验证结果:")
        for service, is_valid in validation_results.items():
            status = "✅" if is_valid else "❌"
            logger.info(f"  {status} {service}")
        
        return True
        
    except Exception as e:
        logger.error(f"配置检查失败: {e}")
        return False

def check_database_connection():
    """检查数据库连接"""
    try:
        from database_manager import db_manager
        
        if db_manager.test_connection():
            logger.info("✅ 数据库连接正常")
            return True
        else:
            logger.warning("❌ 数据库连接失败，请检查配置")
            return False
            
    except Exception as e:
        logger.error(f"数据库连接检查失败: {e}")
        return False

def initialize_database():
    """初始化数据库"""
    try:
        from database_manager import db_manager
        
        logger.info("初始化数据库...")
        if db_manager.initialize_database():
            logger.info("✅ 数据库初始化完成")
            return True
        else:
            logger.error("❌ 数据库初始化失败")
            return False
            
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False

def start_streamlit_app():
    """启动Streamlit应用"""
    try:
        logger.info("启动Streamlit应用...")
        
        # 设置环境变量
        env = os.environ.copy()
        env['STREAMLIT_SERVER_PORT'] = '8501'
        env['STREAMLIT_SERVER_ADDRESS'] = '0.0.0.0'
        env['STREAMLIT_BROWSER_GATHER_USAGE_STATS'] = 'false'
        
        # 启动Streamlit
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            "unified_streamlit_app.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0",
            "--browser.gatherUsageStats", "false",
            "--theme.base", "light"
        ]
        
        logger.info("Streamlit应用启动命令:")
        logger.info(" ".join(cmd))
        
        # 启动应用
        process = subprocess.Popen(cmd, env=env)
        
        # 等待一段时间让应用启动
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            logger.info("✅ Streamlit应用启动成功")
            logger.info("🌐 访问地址: http://localhost:8501")
            
            try:
                # 等待进程结束
                process.wait()
            except KeyboardInterrupt:
                logger.info("收到中断信号，正在关闭应用...")
                process.terminate()
                process.wait()
                logger.info("应用已关闭")
        else:
            logger.error("❌ Streamlit应用启动失败")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"启动Streamlit应用失败: {e}")
        return False

def show_startup_info():
    """显示启动信息"""
    print("=" * 60)
    print("🤖 产品知识库数据处理系统")
    print("基于硅基流动大模型的智能知识库索引生成与FastGPT同步系统")
    print("=" * 60)
    print()

def show_help():
    """显示帮助信息"""
    print("""
使用方法:
    python run_unified_app.py [选项]

选项:
    --help, -h          显示帮助信息
    --check-only        仅检查环境，不启动应用
    --skip-deps         跳过依赖安装
    --skip-db-init      跳过数据库初始化
    --port PORT         指定端口号（默认8501）

示例:
    python run_unified_app.py                    # 完整启动
    python run_unified_app.py --check-only       # 仅检查环境
    python run_unified_app.py --port 8502        # 指定端口
    """)

def main():
    """主函数"""
    # 解析命令行参数
    args = sys.argv[1:]
    
    if "--help" in args or "-h" in args:
        show_help()
        return
    
    show_startup_info()
    
    check_only = "--check-only" in args
    skip_deps = "--skip-deps" in args
    skip_db_init = "--skip-db-init" in args
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    if not skip_deps:
        if not check_and_install_dependencies():
            logger.error("依赖安装失败，请手动安装")
            sys.exit(1)
    
    # 检查配置
    if not check_configuration():
        logger.error("配置检查失败")
        sys.exit(1)
    
    # 检查数据库连接
    db_connected = check_database_connection()
    
    # 初始化数据库
    if db_connected and not skip_db_init:
        if not initialize_database():
            logger.warning("数据库初始化失败，但应用仍可启动")
    
    if check_only:
        logger.info("环境检查完成")
        return
    
    # 启动应用
    logger.info("所有检查完成，启动应用...")
    
    if not start_streamlit_app():
        logger.error("应用启动失败")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("用户中断，退出程序")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        sys.exit(1)
