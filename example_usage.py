#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强文档处理器使用示例
展示如何使用增强文档处理器进行智能文档处理和同步
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_document_processor import EnhancedDocumentProcessor
from enhanced_config import *
from config import DATABASE_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/example_usage.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def setup_example_environment():
    """
    设置示例环境
    """
    # 创建必要的目录
    directories = [
        'logs',
        'cache',
        'example_data/product001/manuals',
        'example_data/product001/specs',
        'example_data/product002/manuals',
        'example_data/product002/specs'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"创建目录: {directory}")
    
    # 创建示例文档
    create_example_documents()

def create_example_documents():
    """
    创建示例文档用于测试
    """
    # 示例Markdown文档
    markdown_content = """
# 产品001用户手册

## 产品概述

产品001是一款高性能的智能设备，具有以下特点：

- 高效能处理器
- 智能语音识别
- 多模态交互界面
- 云端数据同步

## 功能特性

### 1. 智能语音控制

用户可以通过语音命令控制设备的各项功能。支持的语音命令包括：

- "开启设备"
- "关闭设备"
- "调节音量"
- "切换模式"

![语音控制界面](images/voice_control.png)

### 2. 多模态交互

设备支持触摸、语音、手势等多种交互方式。

![交互界面](images/interaction_ui.png)

## 技术规格

| 参数 | 规格 |
|------|------|
| 处理器 | ARM Cortex-A78 |
| 内存 | 8GB LPDDR5 |
| 存储 | 256GB UFS 3.1 |
| 显示 | 6.8英寸 OLED |

## 安装指南

1. 打开包装盒
2. 取出设备和配件
3. 连接电源适配器
4. 按下电源键启动设备
5. 按照屏幕提示完成初始设置

## 故障排除

### 常见问题

**Q: 设备无法启动怎么办？**
A: 请检查电源连接，确保电源适配器正常工作。

**Q: 语音识别不准确怎么办？**
A: 请在安静环境中使用，并确保麦克风没有被遮挡。

## 联系支持

如需技术支持，请联系：
- 电话：400-123-4567
- 邮箱：<EMAIL>
- 在线客服：https://support.example.com

---

*本手册最后更新时间：2024年1月15日*
    """
    
    # 创建Markdown文件
    markdown_file = 'example_data/product001/manuals/user_manual.md'
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    logger.info(f"创建示例Markdown文档: {markdown_file}")
    
    # 创建示例图片目录和占位符
    images_dir = 'example_data/product001/manuals/images'
    os.makedirs(images_dir, exist_ok=True)
    
    # 创建占位符图片文件（实际使用时应该是真实图片）
    placeholder_images = ['voice_control.png', 'interaction_ui.png']
    for img in placeholder_images:
        img_path = os.path.join(images_dir, img)
        with open(img_path, 'w') as f:
            f.write(f"# 占位符图片: {img}")
    
    # 示例Word文档内容（模拟）
    word_content = """
产品001技术规格文档

处理器架构：ARM Cortex-A78
制程工艺：5nm
核心数量：8核心
主频：最高3.0GHz

内存规格：
- 类型：LPDDR5
- 容量：8GB
- 频率：6400MHz

存储规格：
- 类型：UFS 3.1
- 容量：256GB
- 读取速度：2100MB/s
- 写入速度：1200MB/s

显示规格：
- 尺寸：6.8英寸
- 类型：AMOLED
- 分辨率：3200x1440
- 刷新率：120Hz
- 色域：DCI-P3

电池规格：
- 容量：5000mAh
- 快充：67W有线快充
- 无线充电：50W无线快充

连接性：
- WiFi：WiFi 6E
- 蓝牙：Bluetooth 5.3
- 5G：支持SA/NSA双模
- NFC：支持

操作系统：
- 系统：Android 14
- 定制UI：CustomUI 5.0
- 安全：TEE安全芯片
    """
    
    # 创建Word文档（使用文本文件模拟）
    word_file = 'example_data/product001/specs/technical_specs.docx'
    with open(word_file, 'w', encoding='utf-8') as f:
        f.write(word_content)
    logger.info(f"创建示例Word文档: {word_file}")
    
    # 创建PDF文档（使用文本文件模拟）
    pdf_content = """
产品001安装指南 (PDF版本)

本文档提供详细的安装步骤和注意事项。

安装前准备：
1. 确保工作环境清洁
2. 准备必要的工具
3. 阅读安全注意事项

详细安装步骤：
[此处应包含详细的安装图解和步骤说明]

注意：实际使用时，这应该是一个真实的PDF文件，
会通过TextIn API进行解析。
    """
    
    pdf_file = 'example_data/product001/manuals/installation_guide.pdf'
    with open(pdf_file, 'w', encoding='utf-8') as f:
        f.write(pdf_content)
    logger.info(f"创建示例PDF文档: {pdf_file}")

def example_single_document_processing():
    """
    示例：单个文档处理
    """
    logger.info("=== 示例：单个文档处理 ===")
    
    # 创建处理器实例
    processor = EnhancedDocumentProcessor(DATABASE_CONFIG)
    
    # 处理单个产品文档
    success = processor.process_product_documents(
        product_id="PRODUCT_001",
        category="用户手册",
        base_path="example_data/product001/manuals"
    )
    
    if success:
        logger.info("✅ 单个文档处理成功")
    else:
        logger.error("❌ 单个文档处理失败")
    
    return success

def example_batch_processing():
    """
    示例：批量文档处理
    """
    logger.info("=== 示例：批量文档处理 ===")
    
    # 创建处理器实例
    processor = EnhancedDocumentProcessor(DATABASE_CONFIG)
    
    # 定义批量处理配置
    products_config = [
        {
            'product_id': 'PRODUCT_001',
            'category': '用户手册',
            'base_path': 'example_data/product001/manuals'
        },
        {
            'product_id': 'PRODUCT_001',
            'category': '技术规格',
            'base_path': 'example_data/product001/specs'
        },
        {
            'product_id': 'PRODUCT_002',
            'category': '用户手册',
            'base_path': 'example_data/product002/manuals'
        }
    ]
    
    # 执行批量处理
    results = processor.batch_process_products(products_config)
    
    # 打印结果
    logger.info(f"批量处理结果:")
    logger.info(f"总计: {results['total']}")
    logger.info(f"成功: {results['success']}")
    logger.info(f"失败: {results['failed']}")
    
    for detail in results['details']:
        status_emoji = "✅" if detail['status'] == 'success' else "❌"
        logger.info(f"{status_emoji} {detail['product_id']} - {detail['category']}: {detail['status']}")
        if 'error' in detail:
            logger.error(f"   错误: {detail['error']}")
    
    return results

def example_document_scanning():
    """
    示例：文档扫描和格式优先级选择
    """
    logger.info("=== 示例：文档扫描和格式优先级选择 ===")
    
    processor = EnhancedDocumentProcessor(DATABASE_CONFIG)
    
    # 扫描文档
    documents = processor.scan_product_documents(
        product_id="PRODUCT_001",
        category="用户手册",
        base_path="example_data/product001/manuals"
    )
    
    logger.info(f"扫描到 {len(documents)} 个文档:")
    for doc in documents:
        logger.info(f"  📄 {doc.file_path}")
        logger.info(f"     格式: {doc.file_format}, 优先级: {doc.priority}")
        logger.info(f"     大小: {doc.file_size} bytes")
        logger.info(f"     修改时间: {doc.last_modified}")
        logger.info(f"     哈希: {doc.file_hash[:8]}...")
    
    # 选择最优文档
    selected_doc = processor.select_optimal_document(documents)
    if selected_doc:
        logger.info(f"🎯 选择的最优文档: {selected_doc.file_path}")
        logger.info(f"   格式: {selected_doc.file_format} (优先级: {selected_doc.priority})")
    else:
        logger.warning("未找到合适的文档")
    
    return documents, selected_doc

def example_content_processing():
    """
    示例：内容处理和图片标注
    """
    logger.info("=== 示例：内容处理和图片标注 ===")
    
    processor = EnhancedDocumentProcessor(DATABASE_CONFIG)
    
    # 创建示例文档信息
    from enhanced_document_processor import DocumentInfo
    
    doc_info = DocumentInfo(
        file_path="example_data/product001/manuals/user_manual.md",
        file_format="markdown",
        priority=1,
        file_size=os.path.getsize("example_data/product001/manuals/user_manual.md"),
        file_hash=processor.get_file_hash("example_data/product001/manuals/user_manual.md"),
        last_modified=datetime.now(),
        product_id="PRODUCT_001",
        category="用户手册"
    )
    
    # 处理文档内容
    processed_doc = processor.process_document_content(doc_info)
    
    logger.info(f"📖 处理后的文档内容长度: {len(processed_doc.content) if processed_doc.content else 0} 字符")
    
    if processed_doc.content:
        # 显示内容预览
        preview = processed_doc.content[:200] + "..." if len(processed_doc.content) > 200 else processed_doc.content
        logger.info(f"📝 内容预览: {preview}")
    
    if processed_doc.images:
        logger.info(f"🖼️ 发现 {len(processed_doc.images)} 张图片:")
        for i, img in enumerate(processed_doc.images, 1):
            logger.info(f"   {i}. {img['path']}")
            logger.info(f"      Alt文本: {img['alt_text']}")
            if img['description']:
                logger.info(f"      AI描述: {img['description'][:100]}...")
    
    return processed_doc

def example_incremental_update():
    """
    示例：增量更新检测
    """
    logger.info("=== 示例：增量更新检测 ===")
    
    processor = EnhancedDocumentProcessor(DATABASE_CONFIG)
    
    # 检查现有内容
    existing_content = processor.check_fastgpt_existing_content(
        product_id="PRODUCT_001",
        category="用户手册"
    )
    
    if existing_content:
        logger.info(f"📋 发现现有内容:")
        logger.info(f"   FastGPT ID: {existing_content['fastgpt_id']}")
        logger.info(f"   内容哈希: {existing_content['content_hash']}")
        logger.info(f"   最后同步: {existing_content['last_sync_time']}")
    else:
        logger.info("📋 未发现现有内容，将创建新内容")
    
    # 模拟新内容
    new_content = "这是更新后的产品文档内容..."
    
    if existing_content:
        # 创建增量内容
        incremental_content = processor.create_incremental_content(
            new_content,
            existing_content['content_hash']
        )
        
        if incremental_content:
            logger.info(f"📈 生成增量内容，长度: {len(incremental_content)} 字符")
        else:
            logger.info(f"📈 内容无变化，无需更新")
    
    return existing_content

def example_configuration_validation():
    """
    示例：配置验证
    """
    logger.info("=== 示例：配置验证 ===")
    
    from enhanced_config import validate_config, get_config
    
    # 验证配置
    is_valid = validate_config()
    
    if is_valid:
        logger.info("✅ 配置验证通过")
    else:
        logger.warning("⚠️ 配置验证失败，请检查环境变量")
    
    # 显示关键配置
    configs_to_show = ['textin', 'multimodal', 'fastgpt']
    
    for config_name in configs_to_show:
        config = get_config(config_name)
        logger.info(f"🔧 {config_name.upper()} 配置:")
        
        # 隐藏敏感信息
        safe_config = {}
        for key, value in config.items():
            if 'key' in key.lower() or 'secret' in key.lower() or 'password' in key.lower():
                safe_config[key] = "***" if value and not value.startswith('your_') else value
            else:
                safe_config[key] = value
        
        logger.info(f"   {json.dumps(safe_config, indent=4, ensure_ascii=False)}")
    
    return is_valid

def main():
    """
    主函数 - 运行所有示例
    """
    logger.info("🚀 开始运行增强文档处理器示例")
    
    try:
        # 设置示例环境
        setup_example_environment()
        
        # 运行各种示例
        examples = [
            ("配置验证", example_configuration_validation),
            ("文档扫描", example_document_scanning),
            ("内容处理", example_content_processing),
            ("增量更新", example_incremental_update),
            ("单个文档处理", example_single_document_processing),
            ("批量处理", example_batch_processing)
        ]
        
        results = {}
        
        for name, func in examples:
            logger.info(f"\n{'='*50}")
            logger.info(f"开始执行示例: {name}")
            logger.info(f"{'='*50}")
            
            try:
                result = func()
                results[name] = {'status': 'success', 'result': result}
                logger.info(f"✅ 示例 '{name}' 执行成功")
            except Exception as e:
                results[name] = {'status': 'error', 'error': str(e)}
                logger.error(f"❌ 示例 '{name}' 执行失败: {e}")
        
        # 总结
        logger.info(f"\n{'='*50}")
        logger.info("📊 执行总结")
        logger.info(f"{'='*50}")
        
        success_count = sum(1 for r in results.values() if r['status'] == 'success')
        total_count = len(results)
        
        logger.info(f"总计: {total_count} 个示例")
        logger.info(f"成功: {success_count} 个")
        logger.info(f"失败: {total_count - success_count} 个")
        
        for name, result in results.items():
            status_emoji = "✅" if result['status'] == 'success' else "❌"
            logger.info(f"{status_emoji} {name}: {result['status']}")
            if result['status'] == 'error':
                logger.info(f"   错误: {result['error']}")
        
        logger.info("\n🎉 所有示例执行完成！")
        
    except Exception as e:
        logger.error(f"❌ 示例执行过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    main()