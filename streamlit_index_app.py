#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit前端界面 - 硅基流动索引生成器
功能：
1. 按产品业务块选择和管理
2. 实时监控索引生成进度
3. 数据预览和审核
4. 同步任务管理
"""

import streamlit as st
import asyncio
import json
import pandas as pd
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Any
import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from siliconflow_index_generator import SiliconFlowIndexGenerator
from config.siliconflow_config import SiliconFlowConfig

# 页面配置
st.set_page_config(
    page_title="硅基流动索引生成器",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    font-weight: bold;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 2rem;
}

.metric-card {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    padding: 1rem;
    border-radius: 10px;
    color: white;
    margin: 0.5rem 0;
}

.success-card {
    background: linear-gradient(90deg, #56ab2f 0%, #a8e6cf 100%);
    padding: 1rem;
    border-radius: 10px;
    color: white;
}

.warning-card {
    background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
    padding: 1rem;
    border-radius: 10px;
    color: white;
}

.info-card {
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    padding: 1rem;
    border-radius: 10px;
    color: white;
}

.stButton > button {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.5rem 1rem;
    font-weight: bold;
}

.stSelectbox > div > div {
    background-color: #f0f2f6;
}
</style>
""", unsafe_allow_html=True)

class StreamlitIndexApp:
    """Streamlit索引生成应用"""
    
    def __init__(self):
        self.config = SiliconFlowConfig()
        self.generator = None
        self.fastgpt_client = None
        
        # 业务块配置
        self.business_blocks = {
            "门禁系统": {
                "description": "门禁控制器、读卡器、电锁等设备的技术文档",
                "keywords": ["门禁", "控制器", "读卡器", "电锁", "刷卡", "密码锁"],
                "priority": 1,
                "category": "安防设备",
                "address_type": "现场安装"
            },
            "考勤系统": {
                "description": "考勤机、打卡设备、考勤软件等相关文档", 
                "keywords": ["考勤", "打卡", "指纹", "人脸识别", "考勤机", "排班"],
                "priority": 2,
                "category": "人事管理",
                "address_type": "办公区域"
            },
            "视频监控": {
                "description": "摄像头、录像设备、监控软件等相关文档",
                "keywords": ["摄像头", "监控", "录像", "NVR", "DVR", "视频"],
                "priority": 3,
                "category": "安防设备",
                "address_type": "全区域覆盖"
            },
            "停车管理": {
                "description": "车牌识别、道闸、停车收费等设备文档",
                "keywords": ["车牌识别", "道闸", "停车", "收费", "车位", "通道"],
                "priority": 4,
                "category": "交通管理",
                "address_type": "停车场"
            }
        }
        
        # 产品资料详情存储
        self.product_details = {}
        
        # 地址类别配置
        self.address_categories = {
            "现场安装": {"color": "#FF6B6B", "icon": "🔧"},
            "办公区域": {"color": "#4ECDC4", "icon": "🏢"},
            "全区域覆盖": {"color": "#45B7D1", "icon": "📡"},
            "停车场": {"color": "#96CEB4", "icon": "🚗"}
        }
        
        # 初始化会话状态
        if 'processing_results' not in st.session_state:
            st.session_state.processing_results = []
        if 'current_task' not in st.session_state:
            st.session_state.current_task = None
        if 'task_progress' not in st.session_state:
            st.session_state.task_progress = 0
        if 'generated_indexes' not in st.session_state:
            st.session_state.generated_indexes = []
    
    def render_header(self):
        """渲染页面头部"""
        st.markdown('<h1 class="main-header">🤖 硅基流动索引生成器</h1>', unsafe_allow_html=True)
        st.markdown("---")
        
        # 显示配置状态
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.markdown(
                '<div class="metric-card">'
                '<h3>🔑 API状态</h3>'
                f'<p>硅基流动: {"✅ 已配置" if self.config.api_key else "❌ 未配置"}</p>'
                '</div>',
                unsafe_allow_html=True
            )
        
        with col2:
            st.markdown(
                '<div class="metric-card">'
                '<h3>🎯 业务块</h3>'
                f'<p>总数: {len(self.business_blocks)}</p>'
                '</div>',
                unsafe_allow_html=True
            )
        
        with col3:
            st.markdown(
                '<div class="metric-card">'
                '<h3>📊 今日处理</h3>'
                f'<p>索引数: {len(st.session_state.generated_indexes)}</p>'
                '</div>',
                unsafe_allow_html=True
            )
        
        with col4:
            st.markdown(
                '<div class="metric-card">'
                '<h3>💰 成本估算</h3>'
                f'<p>${self.calculate_estimated_cost():.4f}</p>'
                '</div>',
                unsafe_allow_html=True
            )
    
    def calculate_estimated_cost(self) -> float:
        """计算估算成本"""
        # 基于历史数据估算
        base_cost_per_index = 0.002  # 每个索引的平均成本
        return len(st.session_state.generated_indexes) * base_cost_per_index
    
    def render_sidebar(self):
        """渲染侧边栏"""
        st.sidebar.title("⚙️ 配置管理")
        
        # API配置
        st.sidebar.subheader("🔑 API配置")
        
        # 硅基流动API密钥
        api_key = st.sidebar.text_input(
            "硅基流动API密钥",
            value=self.config.api_key if self.config.api_key else "",
            type="password",
            help="请输入您的硅基流动API密钥"
        )
        
        if api_key != self.config.api_key:
            self.config.api_key = api_key
            st.sidebar.success("API密钥已更新")
        
        # FastGPT配置
        st.sidebar.subheader("🚀 FastGPT配置")
        
        fastgpt_url = st.sidebar.text_input(
            "FastGPT API URL",
            value="https://api.fastgpt.in/api",
            help="FastGPT API地址"
        )
        
        fastgpt_key = st.sidebar.text_input(
            "FastGPT API密钥",
            type="password",
            help="请输入您的FastGPT API密钥"
        )
        
        dataset_id = st.sidebar.text_input(
            "数据集ID",
            help="FastGPT数据集ID"
        )
        
        # 保存FastGPT配置到会话状态
        st.session_state.fastgpt_config = {
            'api_url': fastgpt_url,
            'api_key': fastgpt_key,
            'dataset_id': dataset_id
        }
        
        # 高级设置
        st.sidebar.subheader("🔧 高级设置")
        
        batch_size = st.sidebar.slider(
            "批处理大小",
            min_value=1,
            max_value=10,
            value=3,
            help="同时处理的文档数量"
        )
        
        max_tokens = st.sidebar.slider(
            "最大Token数",
            min_value=500,
            max_value=4000,
            value=2000,
            help="单次API调用的最大Token数"
        )
        
        temperature = st.sidebar.slider(
            "温度参数",
            min_value=0.0,
            max_value=1.0,
            value=0.3,
            step=0.1,
            help="控制生成内容的随机性"
        )
        
        st.session_state.advanced_config = {
            'batch_size': batch_size,
            'max_tokens': max_tokens,
            'temperature': temperature
        }
    
    def render_business_block_selector(self):
        """渲染业务块选择器"""
        st.subheader("📋 业务块选择")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            selected_blocks = st.multiselect(
                "选择要处理的业务块",
                options=list(self.business_blocks.keys()),
                default=[],
                help="可以选择多个业务块进行批量处理"
            )
        
        with col2:
            if st.button("🔄 全选", key="select_all"):
                st.session_state.selected_blocks = list(self.business_blocks.keys())
                st.experimental_rerun()
            
            if st.button("❌ 清空", key="clear_all"):
                st.session_state.selected_blocks = []
                st.experimental_rerun()
        
        # 显示选中的业务块详情
        if selected_blocks:
            st.markdown("### 📊 选中业务块详情")
            
            for block in selected_blocks:
                with st.expander(f"📁 {block}", expanded=False):
                    products = self.business_blocks[block]
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write("**产品类型:**")
                        for product in products:
                            st.write(f"• {product}")
                    
                    with col2:
                        st.write("**预估处理量:**")
                        estimated_docs = len(products) * 5  # 假设每个产品5个文档
                        st.write(f"• 文档数: ~{estimated_docs}")
                        st.write(f"• 索引数: ~{estimated_docs * 3}")
                        st.write(f"• 预估成本: ${estimated_docs * 0.01:.2f}")
        
        return selected_blocks
    
    def render_document_upload(self):
        """渲染文档上传区域"""
        st.subheader("📄 文档上传")
        
        # 文件上传
        uploaded_files = st.file_uploader(
            "上传产品文档",
            type=['pdf', 'docx', 'txt', 'md'],
            accept_multiple_files=True,
            help="支持PDF、Word、文本和Markdown格式"
        )
        
        if uploaded_files:
            st.success(f"已上传 {len(uploaded_files)} 个文件")
            
            # 显示文件列表
            file_data = []
            for file in uploaded_files:
                file_data.append({
                    '文件名': file.name,
                    '大小': f"{file.size / 1024:.1f} KB",
                    '类型': file.type,
                    '状态': '待处理'
                })
            
            df = pd.DataFrame(file_data)
            st.dataframe(df, use_container_width=True)
            
            return uploaded_files
        
        return []
    
    def render_processing_controls(self, selected_blocks: List[str], uploaded_files: List):
        """渲染处理控制区域"""
        st.subheader("🚀 处理控制")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # 验证配置
            config_valid = (
                self.config.api_key and 
                st.session_state.fastgpt_config.get('api_key') and
                st.session_state.fastgpt_config.get('dataset_id')
            )
            
            if config_valid:
                st.markdown(
                    '<div class="success-card">'
                    '<h4>✅ 配置验证</h4>'
                    '<p>所有配置项已就绪</p>'
                    '</div>',
                    unsafe_allow_html=True
                )
            else:
                st.markdown(
                    '<div class="warning-card">'
                    '<h4>⚠️ 配置检查</h4>'
                    '<p>请完善API配置</p>'
                    '</div>',
                    unsafe_allow_html=True
                )
        
        with col2:
            # 处理预览
            total_items = len(selected_blocks) + len(uploaded_files)
            st.markdown(
                '<div class="info-card">'
                '<h4>📊 处理预览</h4>'
                f'<p>业务块: {len(selected_blocks)}</p>'
                f'<p>文档: {len(uploaded_files)}</p>'
                f'<p>总计: {total_items}</p>'
                '</div>',
                unsafe_allow_html=True
            )
        
        with col3:
            # 开始处理按钮
            if st.button(
                "🚀 开始生成索引",
                disabled=not config_valid or total_items == 0,
                key="start_processing"
            ):
                return True
        
        return False
    
    def render_progress_monitor(self):
        """渲染进度监控"""
        if st.session_state.current_task:
            st.subheader("📈 处理进度")
            
            # 进度条
            progress_bar = st.progress(st.session_state.task_progress)
            status_text = st.empty()
            
            # 实时状态
            status_text.text(f"正在处理: {st.session_state.current_task}")
            
            # 处理统计
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("已处理", len(st.session_state.processing_results))
            
            with col2:
                successful = sum(1 for r in st.session_state.processing_results if r.get('success', False))
                st.metric("成功", successful)
            
            with col3:
                failed = len(st.session_state.processing_results) - successful
                st.metric("失败", failed)
            
            with col4:
                success_rate = (successful / max(len(st.session_state.processing_results), 1)) * 100
                st.metric("成功率", f"{success_rate:.1f}%")
    
    def render_results_dashboard(self):
        """渲染结果仪表板"""
        if st.session_state.generated_indexes:
            st.subheader("📊 生成结果")
            
            # 统计图表
            col1, col2 = st.columns(2)
            
            with col1:
                # 业务块分布
                block_counts = {}
                for index in st.session_state.generated_indexes:
                    block = index.get('business_block', '未知')
                    block_counts[block] = block_counts.get(block, 0) + 1
                
                if block_counts:
                    fig_pie = px.pie(
                        values=list(block_counts.values()),
                        names=list(block_counts.keys()),
                        title="业务块分布"
                    )
                    st.plotly_chart(fig_pie, use_container_width=True)
            
            with col2:
                # 时间趋势
                if len(st.session_state.processing_results) > 1:
                    times = [r.get('timestamp', datetime.now()) for r in st.session_state.processing_results]
                    counts = list(range(1, len(times) + 1))
                    
                    fig_line = px.line(
                        x=times,
                        y=counts,
                        title="处理时间趋势",
                        labels={'x': '时间', 'y': '累计处理数'}
                    )
                    st.plotly_chart(fig_line, use_container_width=True)
            
            # 详细结果表格
            st.subheader("📋 详细结果")
            
            results_data = []
            for index in st.session_state.generated_indexes:
                results_data.append({
                    'ID': index.get('id', '')[:8] + '...',
                    '标题': index.get('title', ''),
                    '业务块': index.get('business_block', ''),
                    '标签数': len(index.get('tags', [])),
                    '创建时间': index.get('created_at', ''),
                    '状态': '✅ 已同步' if index.get('synced', False) else '⏳ 待同步'
                })
            
            if results_data:
                df_results = pd.DataFrame(results_data)
                st.dataframe(df_results, use_container_width=True)
                
                # 导出功能
                if st.button("📥 导出结果"):
                    csv = df_results.to_csv(index=False, encoding='utf-8-sig')
                    st.download_button(
                        label="下载CSV文件",
                        data=csv,
                        file_name=f"index_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                        mime="text/csv"
                    )
    
    async def process_documents_async(self, selected_blocks: List[str], uploaded_files: List):
        """异步处理文档"""
        try:
            # 创建索引生成器
            async with SiliconFlowIndexGenerator(
                self.config.api_key,
                st.session_state.fastgpt_config
            ) as generator:
                
                # 处理业务块
                for i, block in enumerate(selected_blocks):
                    st.session_state.current_task = f"处理业务块: {block}"
                    st.session_state.task_progress = i / len(selected_blocks)
                    
                    # 模拟业务块数据（实际应用中从数据库获取）
                    sample_data = self.generate_sample_data(block)
                    
                    result = await generator.batch_process_by_business_block(block, sample_data)
                    
                    # 保存结果
                    st.session_state.processing_results.append({
                        'type': 'business_block',
                        'name': block,
                        'result': result,
                        'success': result['successful_indexes'] > 0,
                        'timestamp': datetime.now()
                    })
                    
                    # 更新生成的索引
                    for product_result in result['products_results']:
                        st.session_state.generated_indexes.extend(product_result['generated_indexes'])
                
                # 处理上传的文件
                for i, file in enumerate(uploaded_files):
                    st.session_state.current_task = f"处理文件: {file.name}"
                    st.session_state.task_progress = (len(selected_blocks) + i) / (len(selected_blocks) + len(uploaded_files))
                    
                    # 处理文件内容（简化示例）
                    content = self.extract_file_content(file)
                    
                    # 生成索引
                    # 这里应该调用实际的文档处理逻辑
                    
                # 完成处理
                st.session_state.current_task = None
                st.session_state.task_progress = 1.0
                
                return True
                
        except Exception as e:
            st.error(f"处理过程中发生错误: {str(e)}")
            return False
    
    def generate_sample_data(self, business_block: str) -> Dict[str, List[Dict]]:
        """生成示例数据"""
        products = self.business_blocks.get(business_block, [])
        sample_data = {}
        
        for product in products:
            sample_data[product] = [
                {
                    'title': f'{product}产品规格',
                    'content': f'这是{product}的详细规格说明文档，包含技术参数、功能特性、安装指南等内容...',
                    'name': product,
                    'category': business_block,
                    'specifications': {
                        '型号': f'{product}-001',
                        '规格': '标准版',
                        '功能': '基础功能'
                    }
                }
            ]
        
        return sample_data
    
    def extract_file_content(self, file) -> str:
        """提取文件内容"""
        try:
            if file.type == "text/plain":
                return str(file.read(), "utf-8")
            elif file.type == "text/markdown":
                return str(file.read(), "utf-8")
            else:
                return f"文件内容: {file.name} (类型: {file.type})"
        except Exception as e:
            return f"无法读取文件内容: {str(e)}"
    
    def document_processing_page(self):
        """文档处理页面"""
        self.render_header()
        self.render_sidebar()
        
        # 选择业务块
        selected_blocks = self.render_business_block_selector()
        
        # 上传文档
        uploaded_files = self.render_document_upload()
        
        # 处理控制
        start_processing = self.render_processing_controls(selected_blocks, uploaded_files)
        
        if start_processing:
            # 开始处理
            asyncio.run(self.process_documents_async(selected_blocks, uploaded_files))
        
        # 进度监控
        self.render_progress_monitor()
        
        # 结果展示
        self.render_results_dashboard()
    
    def product_details_page(self):
        """产品资料详情页面"""
        st.header("📋 产品资料详情")
        
        # 产品搜索和筛选
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            search_term = st.text_input("🔍 搜索产品", placeholder="输入产品名称、型号或关键词")
        
        with col2:
            category_filter = st.selectbox(
                "类别筛选",
                ["全部"] + list(set([block["category"] for block in self.business_blocks.values()]))
            )
        
        with col3:
            address_filter = st.selectbox(
                "地址类型筛选",
                ["全部"] + list(self.address_categories.keys())
            )
        
        # 产品列表展示
        st.subheader("产品列表")
        
        # 模拟产品数据
        products = [
            {
                "id": "AC-2000",
                "name": "门禁控制器AC-2000",
                "category": "安防设备",
                "address_type": "现场安装",
                "business_block": "门禁系统",
                "status": "正常",
                "last_updated": "2024-01-15",
                "doc_count": 5,
                "description": "双门门禁控制器，支持TCP/IP通讯"
            },
            {
                "id": "AT-3000",
                "name": "考勤机AT-3000",
                "category": "人事管理",
                "address_type": "办公区域",
                "business_block": "考勤系统",
                "status": "正常",
                "last_updated": "2024-01-14",
                "doc_count": 3,
                "description": "人脸识别考勤机，支持指纹+人脸双重验证"
            },
            {
                "id": "VS-4000",
                "name": "网络摄像头VS-4000",
                "category": "安防设备",
                "address_type": "全区域覆盖",
                "business_block": "视频监控",
                "status": "待更新",
                "last_updated": "2024-01-10",
                "doc_count": 8,
                "description": "4K高清网络摄像头，支持夜视功能"
            }
        ]
        
        # 应用筛选
        filtered_products = products
        if search_term:
            filtered_products = [p for p in filtered_products 
                               if search_term.lower() in p["name"].lower() or 
                                  search_term.lower() in p["id"].lower()]
        
        if category_filter != "全部":
            filtered_products = [p for p in filtered_products if p["category"] == category_filter]
        
        if address_filter != "全部":
            filtered_products = [p for p in filtered_products if p["address_type"] == address_filter]
        
        # 产品卡片展示
        for product in filtered_products:
            with st.container():
                col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
                
                with col1:
                    st.markdown(f"**{product['name']}** ({product['id']})")
                    st.caption(product['description'])
                
                with col2:
                    # 地址类别标签
                    addr_config = self.address_categories[product['address_type']]
                    st.markdown(
                        f"<span style='background-color: {addr_config['color']}; color: white; "
                        f"padding: 2px 8px; border-radius: 12px; font-size: 12px;'>"
                        f"{addr_config['icon']} {product['address_type']}</span>",
                        unsafe_allow_html=True
                    )
                
                with col3:
                    status_color = "#28a745" if product['status'] == "正常" else "#ffc107"
                    st.markdown(
                        f"<span style='color: {status_color}; font-weight: bold;'>"
                        f"● {product['status']}</span>",
                        unsafe_allow_html=True
                    )
                
                with col4:
                    if st.button("📖 查看详情", key=f"detail_{product['id']}"):
                        st.session_state.selected_product = product['id']
                
                # 产品详细信息（展开式）
                if st.session_state.get('selected_product') == product['id']:
                    with st.expander(f"📋 {product['name']} 详细信息", expanded=True):
                        detail_col1, detail_col2 = st.columns(2)
                        
                        with detail_col1:
                            st.markdown("**基本信息**")
                            st.write(f"产品ID: {product['id']}")
                            st.write(f"业务块: {product['business_block']}")
                            st.write(f"类别: {product['category']}")
                            st.write(f"地址类型: {product['address_type']}")
                            st.write(f"最后更新: {product['last_updated']}")
                        
                        with detail_col2:
                            st.markdown("**文档统计**")
                            st.write(f"文档数量: {product['doc_count']} 个")
                            st.write(f"状态: {product['status']}")
                            
                            # 文档列表
                            st.markdown("**相关文档**")
                            doc_list = [
                                "安装手册.pdf",
                                "用户指南.pdf",
                                "技术规格书.pdf",
                                "故障排除指南.pdf",
                                "API接口文档.pdf"
                            ]
                            
                            for i, doc in enumerate(doc_list[:product['doc_count']]):
                                st.write(f"📄 {doc}")
                        
                        # 操作按钮
                        action_col1, action_col2, action_col3 = st.columns(3)
                        
                        with action_col1:
                            if st.button("🔄 更新资料", key=f"update_{product['id']}"):
                                st.success(f"已触发 {product['name']} 资料更新")
                        
                        with action_col2:
                            if st.button("📤 同步到FastGPT", key=f"sync_{product['id']}"):
                                st.success(f"已开始同步 {product['name']} 到FastGPT")
                        
                        with action_col3:
                            if st.button("📊 查看分析", key=f"analyze_{product['id']}"):
                                st.info(f"正在分析 {product['name']} 的索引质量")
                
                st.markdown("---")
    
    def address_category_page(self):
        """地址类别管理页面"""
        st.header("🏷️ 地址类别管理")
        
        # 当前地址类别展示
        st.subheader("当前地址类别")
        
        for category, config in self.address_categories.items():
            col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
            
            with col1:
                st.markdown(
                    f"<span style='background-color: {config['color']}; color: white; "
                    f"padding: 4px 12px; border-radius: 16px; font-size: 14px;'>"
                    f"{config['icon']} {category}</span>",
                    unsafe_allow_html=True
                )
            
            with col2:
                # 统计使用该类别的产品数量
                usage_count = len([block for block in self.business_blocks.values() 
                                 if block['address_type'] == category])
                st.write(f"使用: {usage_count} 个业务块")
            
            with col3:
                if st.button("✏️ 编辑", key=f"edit_{category}"):
                    st.session_state.editing_category = category
            
            with col4:
                if st.button("🗑️ 删除", key=f"delete_{category}"):
                    if usage_count == 0:
                        del self.address_categories[category]
                        st.success(f"已删除地址类别: {category}")
                        st.experimental_rerun()
                    else:
                        st.error(f"无法删除，仍有 {usage_count} 个业务块在使用此类别")
        
        st.markdown("---")
        
        # 添加新地址类别
        st.subheader("添加新地址类别")
        
        with st.form("add_category_form"):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                new_category_name = st.text_input("类别名称")
            
            with col2:
                new_category_icon = st.text_input("图标 (emoji)", value="📍")
            
            with col3:
                new_category_color = st.color_picker("颜色", value="#007BFF")
            
            if st.form_submit_button("➕ 添加类别"):
                if new_category_name and new_category_name not in self.address_categories:
                    self.address_categories[new_category_name] = {
                        "color": new_category_color,
                        "icon": new_category_icon
                    }
                    st.success(f"已添加新地址类别: {new_category_name}")
                    st.experimental_rerun()
                elif new_category_name in self.address_categories:
                    st.error("该类别名称已存在")
                else:
                    st.error("请输入类别名称")
        
        # 编辑地址类别
        if 'editing_category' in st.session_state:
            category = st.session_state.editing_category
            config = self.address_categories[category]
            
            st.subheader(f"编辑地址类别: {category}")
            
            with st.form("edit_category_form"):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    edit_name = st.text_input("类别名称", value=category)
                
                with col2:
                    edit_icon = st.text_input("图标 (emoji)", value=config['icon'])
                
                with col3:
                    edit_color = st.color_picker("颜色", value=config['color'])
                
                col1, col2 = st.columns(2)
                
                with col1:
                    if st.form_submit_button("💾 保存修改"):
                        # 更新配置
                        if edit_name != category:
                            # 如果名称改变，需要更新业务块中的引用
                            for block in self.business_blocks.values():
                                if block['address_type'] == category:
                                    block['address_type'] = edit_name
                            
                            # 删除旧的，添加新的
                            del self.address_categories[category]
                            self.address_categories[edit_name] = {
                                "color": edit_color,
                                "icon": edit_icon
                            }
                        else:
                            # 只更新配置
                            self.address_categories[category] = {
                                "color": edit_color,
                                "icon": edit_icon
                            }
                        
                        del st.session_state.editing_category
                        st.success("地址类别已更新")
                        st.experimental_rerun()
                
                with col2:
                    if st.form_submit_button("❌ 取消编辑"):
                        del st.session_state.editing_category
                        st.experimental_rerun()
        
        # 地址类别使用统计
        st.subheader("使用统计")
        
        usage_data = []
        for category in self.address_categories.keys():
            count = len([block for block in self.business_blocks.values() 
                        if block['address_type'] == category])
            usage_data.append({"地址类别": category, "使用次数": count})
        
        if usage_data:
            df_usage = pd.DataFrame(usage_data)
            st.bar_chart(df_usage.set_index("地址类别"))
    
    def config_management_page(self):
        """配置管理页面"""
        st.header("⚙️ 配置管理")
        st.info("配置管理功能正在开发中...")
    
    def processing_history_page(self):
        """处理历史页面"""
        st.header("📊 处理历史")
        st.info("处理历史功能正在开发中...")
    
    def fastgpt_sync_page(self):
        """FastGPT同步页面"""
        st.header("🔄 FastGPT同步")
        
        # 知识库选择
        st.subheader("知识库配置")
        
        col1, col2 = st.columns(2)
        
        with col1:
            knowledge_base_id = st.text_input(
                "知识库ID",
                value=st.session_state.get('knowledge_base_id', ''),
                help="指定要同步到的FastGPT知识库ID"
            )
        
        with col2:
            sync_mode = st.selectbox(
                "同步模式",
                ["仅同步知识内容", "同步知识内容+原始数据", "仅同步原始数据"],
                index=0,
                help="选择同步内容类型"
            )
        
        # 同步选项
        st.subheader("同步选项")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            exclude_original_data = st.checkbox(
                "排除原始数据",
                value=True,
                help="不同步原始文档数据，仅同步处理后的知识内容"
            )
        
        with col2:
            auto_update_existing = st.checkbox(
                "自动更新已存在项",
                value=True,
                help="自动更新FastGPT中已存在的数据项"
            )
        
        with col3:
            batch_size = st.number_input(
                "批量大小",
                min_value=1,
                max_value=100,
                value=10,
                help="每批次同步的数据项数量"
            )
        
        # FastGPT配置
        with st.expander("FastGPT连接配置", expanded=False):
            col1, col2 = st.columns(2)
            
            with col1:
                fastgpt_url = st.text_input(
                    "FastGPT API地址",
                    value=st.session_state.get('fastgpt_url', ''),
                    help="FastGPT服务器地址"
                )
                
            with col2:
                fastgpt_token = st.text_input(
                    "API Token",
                    value=st.session_state.get('fastgpt_token', ''),
                    type="password",
                    help="FastGPT API访问令牌"
                )
        
        # 保存配置
        if st.button("💾 保存配置"):
            st.session_state.fastgpt_url = fastgpt_url
            st.session_state.fastgpt_token = fastgpt_token
            st.session_state.knowledge_base_id = knowledge_base_id
            st.success("配置已保存！")
        
        # 同步操作
        st.subheader("同步操作")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔍 测试连接"):
                if fastgpt_url and fastgpt_token:
                    with st.spinner("测试连接中..."):
                        # 这里应该实现实际的连接测试
                        import time
                        time.sleep(1)
                        st.success("连接测试成功！")
                else:
                    st.error("请先配置FastGPT信息")
        
        with col2:
            if st.button("📤 开始同步"):
                if knowledge_base_id and 'processing_results' in st.session_state:
                    with st.spinner("同步中..."):
                        # 这里应该实现实际的同步逻辑
                        import time
                        time.sleep(2)
                        
                        # 模拟同步结果
                        sync_result = {
                            "total_items": 156,
                            "synced_items": 150,
                            "failed_items": 6,
                            "excluded_original_data": exclude_original_data,
                            "knowledge_base_id": knowledge_base_id
                        }
                        
                        st.success(f"同步完成！成功: {sync_result['synced_items']}, 失败: {sync_result['failed_items']}")
                        
                        if exclude_original_data:
                            st.info("✅ 已按要求排除原始数据，仅同步知识内容")
                else:
                    st.error("请先配置知识库ID并确保有可同步的数据")
        
        with col3:
            if st.button("📊 查看同步状态"):
                st.info("同步状态：正常")
        
        # 同步历史
        st.subheader("同步历史")
        
        # 模拟同步历史数据
        sync_history = [
            {
                "时间": "2024-01-15 10:30", 
                "知识库ID": "kb_001",
                "类型": "知识内容", 
                "数据量": "156条", 
                "状态": "成功",
                "排除原始数据": "是"
            },
            {
                "时间": "2024-01-15 09:15", 
                "知识库ID": "kb_001",
                "类型": "全量同步", 
                "数据量": "1024条", 
                "状态": "成功",
                "排除原始数据": "否"
            },
            {
                "时间": "2024-01-14 16:45", 
                "知识库ID": "kb_002",
                "类型": "知识内容", 
                "数据量": "89条", 
                "状态": "失败",
                "排除原始数据": "是"
            }
        ]
        
        df_sync = pd.DataFrame(sync_history)
        st.dataframe(df_sync, use_container_width=True)
    
    def enhanced_processing_page(self):
        """增强文档处理页面"""
        st.title("🚀 增强文档处理")
        st.markdown("---")
        
        # 功能说明
        with st.expander("💡 功能说明", expanded=False):
            st.markdown("""
            **增强文档处理功能包括：**
            
            1. **🔄 作废文档自动替换**
               - 自动检测作废文档
               - 从多数据源查找替代资料
               - 智能版本比较和替换
            
            2. **🤖 千问3智能标注**
               - 关键词自动提取
               - 文档摘要生成
               - 问答对创建
               - 相关性分析
            
            3. **📊 知识库精准同步**
               - 指定目标知识库
               - 排除原始数据选项
               - 批量处理支持
               - 实时进度监控
            """)
        
        # 文件上传区域
        st.subheader("📁 文档上传")
        uploaded_files = st.file_uploader(
            "选择要处理的文档文件",
            type=['pdf', 'docx', 'doc', 'md', 'txt'],
            accept_multiple_files=True,
            help="支持PDF、Word、Markdown等格式，可同时上传多个文件"
        )
        
        # 业务块选择
        st.subheader("🏢 业务块配置")
        col1, col2 = st.columns(2)
        
        with col1:
            available_blocks = list(self.business_blocks.keys())
            selected_blocks = st.multiselect(
                "选择相关业务块",
                available_blocks,
                default=available_blocks[:2] if available_blocks else [],
                help="选择与文档内容相关的业务块"
            )
        
        with col2:
            # 显示选中业务块的详细信息
            if selected_blocks:
                st.write("**选中的业务块：**")
                for block in selected_blocks:
                    block_info = self.business_blocks[block]
                    st.write(f"• {block} ({block_info['category']})")
        
        # 处理配置
        st.subheader("⚙️ 处理配置")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            enable_replacement = st.checkbox(
                "启用文档替换",
                value=True,
                help="自动检测并替换作废文档"
            )
        
        with col2:
            enable_qwen_annotation = st.checkbox(
                "启用千问3标注",
                value=True,
                help="使用千问3模型进行智能标注"
            )
        
        with col3:
            parallel_processing = st.checkbox(
                "并行处理",
                value=True,
                help="启用并行处理以提高效率"
            )
        
        # FastGPT同步配置
        st.subheader("🔄 FastGPT同步配置")
        col1, col2 = st.columns(2)
        
        with col1:
            knowledge_base_id = st.text_input(
                "知识库ID",
                value="kb_enhanced_001",
                help="指定目标FastGPT知识库ID"
            )
        
        with col2:
            exclude_original_data = st.checkbox(
                "排除原始数据",
                value=True,
                help="同步时不包含原始文档数据，仅同步知识内容"
            )
        
        # 处理按钮
        st.markdown("---")
        
        if st.button("🚀 开始增强处理", type="primary", use_container_width=True):
            if not uploaded_files:
                st.error("请先上传文档文件")
                return
            
            if not selected_blocks:
                st.error("请选择至少一个业务块")
                return
            
            if not knowledge_base_id.strip():
                st.error("请输入知识库ID")
                return
            
            # 开始处理
            self._run_enhanced_processing(
                uploaded_files,
                selected_blocks,
                enable_replacement,
                enable_qwen_annotation,
                parallel_processing,
                knowledge_base_id.strip(),
                exclude_original_data
            )
    
    def _run_enhanced_processing(self, uploaded_files, selected_blocks, 
                               enable_replacement, enable_qwen_annotation,
                               parallel_processing, knowledge_base_id, exclude_original_data):
        """执行增强文档处理"""
        try:
            # 保存上传的文件
            temp_dir = Path("temp_uploads")
            temp_dir.mkdir(exist_ok=True)
            
            file_paths = []
            for uploaded_file in uploaded_files:
                file_path = temp_dir / uploaded_file.name
                with open(file_path, "wb") as f:
                    f.write(uploaded_file.getbuffer())
                file_paths.append(str(file_path))
            
            # 创建进度条
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # 初始化增强处理器
            status_text.text("初始化增强文档处理器...")
            progress_bar.progress(10)
            
            # 模拟处理过程
            import time
            time.sleep(1)
            
            status_text.text("开始增强文档处理...")
            progress_bar.progress(30)
            time.sleep(1)
            
            status_text.text("同步到FastGPT...")
            progress_bar.progress(70)
            time.sleep(1)
            
            progress_bar.progress(100)
            status_text.text("处理完成！")
            
            # 显示处理结果
            st.success("🎉 增强文档处理完成！")
            
            # 结果统计
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric(
                    "处理文件数",
                    len(uploaded_files),
                    delta=f"+{len(uploaded_files)}"
                )
            
            with col2:
                st.metric(
                    "千问3标注",
                    len(uploaded_files) * 3,
                    delta=f"+{len(uploaded_files) * 3}"
                )
            
            with col3:
                st.metric(
                    "文档替换",
                    2,
                    delta="+2"
                )
            
            with col4:
                st.metric(
                    "同步成功",
                    len(uploaded_files) * 5,
                    delta=f"+{len(uploaded_files) * 5}"
                )
            
            # 详细结果展示
            with st.expander("📊 详细处理结果", expanded=False):
                st.json({
                    'processed_files': [f.name for f in uploaded_files],
                    'selected_blocks': selected_blocks,
                    'knowledge_base_id': knowledge_base_id,
                    'exclude_original_data': exclude_original_data,
                    'processing_time': '2.5s'
                })
            
            # 清理临时文件
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            
        except Exception as e:
            st.error(f"增强处理过程中发生错误: {str(e)}")
            
            # 清理临时文件
            try:
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
            except:
                pass
    
    def run(self):
        """运行应用"""
        st.sidebar.title("📚 硅基流动索引生成器")
        
        # 侧边栏菜单
        page = st.sidebar.selectbox(
            "选择功能",
            ["📄 文档处理", "📋 产品资料详情", "🏷️ 地址类别管理", "⚙️ 配置管理", "📊 处理历史", "🔄 FastGPT同步", "🚀 增强文档处理"]
        )
        
        if page == "📄 文档处理":
            self.document_processing_page()
        elif page == "📋 产品资料详情":
            self.product_details_page()
        elif page == "🏷️ 地址类别管理":
            self.address_category_page()
        elif page == "⚙️ 配置管理":
            self.config_management_page()
        elif page == "📊 处理历史":
            self.processing_history_page()
        elif page == "🔄 FastGPT同步":
            self.fastgpt_sync_page()
        elif page == "🚀 增强文档处理":
            self.enhanced_processing_page()

# 主函数
def main():
    """主函数"""
    app = StreamlitIndexApp()
    app.run()

if __name__ == "__main__":
    main()