api:
  base_url: https://zkmall.zktecoip.com
  download_dir: ./downloads
  max_retries: 3
  password: Zk@123456
  timeout: 30
  username: '18929343717'
database:
  dbname: product_knowledge_db
  host: **************
  password: password
  port: 5433
  user: username
fastgpt:
  api_key: ''
  api_url: https://api.fastgpt.in/api
  dataset_id: ''
  max_retries: 3
  timeout: 30
file_paths:
  allcollections: ./allcollections.json
  downloads_dir: ./downloads
  local_files_base: E:/pyydemo/work/new_work/get_mongo_alldata/AI客服知识库
  logs_dir: ./logs
  product_structure: ./国内产品结构细化表.json
  results_dir: ./results
  temp_dir: ./temp
  uploads_dir: ./uploads
log:
  backup_count: 5
  file: ./logs/unified_app.log
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: INFO
  max_file_size: 10485760
process:
  batch_size: 100
  cache_enabled: true
  cache_ttl: 3600
  enable_legacy_import: true
  enable_local_scan: true
  enable_yunshang_api: true
  max_concurrent_requests: 5
  max_retries: 3
  timeout: 30
siliconflow:
  api_base_url: https://api.siliconflow.cn/v1
  api_key: sk-kmaipghbqavpzfnhpuuybpgrimcroynvsqlfkbnhcjcdulxj
  models:
    code: deepseek-ai/DeepSeek-Coder-V2-Instruct
    lightweight: Qwen/Qwen2.5-7B-Instruct
    text: Qwen/Qwen2.5-72B-Instruct
    vision: Qwen/Qwen2-VL-72B-Instruct
