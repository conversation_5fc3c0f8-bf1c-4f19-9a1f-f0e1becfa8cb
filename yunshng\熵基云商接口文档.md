# 熵基云商接口文档 v1.0

## 文档修订记录

| 日期 | 修订版本 | 修改章节 | 修改描述 | 作者 |
|------|----------|----------|----------|------|
| 2024.12.19 | V1.0 | 1 | 初始文档 | 付兴强，赵文超，陈继野 |

## 测试环境地址

**基础URL**: `https://zkmall.zktecoip.com`

---

## 1. 登录接口

### 接口说明
PC端系统管理员登录接口，返回token信息。

> **注意**: 欧洲网站账号需要更换成对应欧洲站点管理员的账号密码

### 请求信息
- **请求路径**: `https://{zkmall}/api/loginPlatform`
- **请求方式**: `POST`
- **请求头**: 无特殊要求

### 请求参数
```json
{
  "username": "18929343717",
  "password": "123456"
}
```

| 参数名 | 字段类型 | 是否必填 | 说明 |
|--------|----------|----------|------|
| username | String | 是 | 用户名 |
| password | String | 是 | 密码 |

### 返回结果
```json
{
  "msg": "操作成功",
  "code": 200,
  "token": "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVhOWJmMTQzLTkwOGUtNGMwYy05OWVlLTc2NjNhOGU5N2Q2ZCJ9.OdlVBQOMUiD4ugxRBHL0vwPfY3cNJaRgxq2MjF929iNjwr0BsR7ON9hnhbqjPOSZAcc_Fbz2fFo5tFZKhZw_Ow"
}
```

### 返回参数说明
| 参数名 | 说明 |
|--------|------|
| token | 获取到的客户端授权凭证 |

---

## 2. 产品列表接口

### 接口说明
根据当前用户携带token，查询产品列表接口。

### 请求信息
- **请求路径**: `https://{zkmall}/api/business/product/list`
- **请求方式**: `GET`
- **请求头**: `Authorization: {token}`

### 请求参数
| 参数名 | 字段类型 | 说明 |
|--------|----------|------|
| isApp | int | 移动端传1 |
| category | String | 产品分类id |
| labelListForSelect | 数组 | 根据标签id搜索产品，可传多个 |
| pageSize | String | 条数 |
| current | String | 页数 |
| orderByColumn | String | 排序（watch按照浏览量排序） |
| isAsc | String | 正序倒序（desc倒序） |

### 返回结果示例
```json
{
  "msg": "操作成功",
  "code": 200,
  "data": {
    "total": 1,
    "rows": [
      {
        "id": 31,
        "status": "0",
        "name": "指纹考勤机",
        "smallImg": "https://example.com/image.png",
        "banner": "https://example.com/banner.png",
        "attribute": "0",
        "category": "110",
        "label": "17",
        "spec": "ZK3969",
        "introduction": "双识别模式，自由选择考勤方式。",
        "showFor": "一级经销商,二级经销商,智慧号,熵基分公司,工程商/安装商,游客",
        "useTo": "写字楼、连锁商超、学校、工厂、工地、酒店",
        "likeCount": 0,
        "favoriteCount": 1,
        "price": 0.0,
        "categoryName": "指纹识别考勤机",
        "labelName": "渠道",
        "count": 770,
        "productId": 31,
        "siteId": 999
      }
    ],
    "code": 200,
    "msg": "查询成功"
  }
}
```

### 返回参数说明
| 参数名 | 说明 |
|--------|------|
| id | 主键 |
| status | 状态 0正常 1停用 |
| name | 产品名 |
| details | 详情富文本 |
| other | 附件地址 |
| smallImg | 小图 |
| isSuggest | 是否推荐 0是 1否 |
| labelName | 渠道名字 |
| categoryName | 分类名称 |
| count | 点击量 |
| guide | 操作指南 |
| showFor | 指定可见 |
| isLike | 是否点赞 0否 1是 |
| isFavorite | 是否收藏 0否 1是 |

---

## 3. 产品关联案例接口

### 接口说明
根据当前用户携带token，查询所属站点的共享案例列表接口。

### 请求信息
- **请求路径**: `https://{zkmall}/api/companycase/pcList?productId={productId}`
- **请求方式**: `GET`
- **请求头**: `Authorization: {token}`

### 请求参数
| 参数名 | 字段类型 | 说明 |
|--------|----------|------|
| productId | String | 产品ID |

### 返回参数说明
| 参数名 | 说明 |
|--------|------|
| id | 主键 |
| status | 状态 0正常 1停用 |
| name | 名字 |
| details | 详情富文本 |
| smallImg | 小图 |
| isSuggest | 是否推荐 0是 1否 |
| img | 活动小图 |
| keywords | 关键字 |
| companyName | 公司名称 |
| banner | 轮播图 |
| introduction | 简介 |
| publishName | 发布人 |
| isLike | 是否点赞 0否 1是 |
| isFavorite | 是否收藏 0否 1是 |

---

## 4. 产品关联方案接口

### 接口说明
查询方案列表。

### 请求信息
- **请求路径**: `https://zkmall.zktecoiot.com/api/programme/relatedList?productId={productId}`
- **请求方式**: `GET`
- **请求头**: `Authorization: {token}`

### 请求参数
| 参数名 | 字段类型 | 说明 |
|--------|----------|------|
| isApp | String | 1 |
| status | String | 0 |
| category | int | 分类id，通过分类查询时使用，-1或不传值查询全部 |

### 返回参数说明
| 参数名 | 说明 |
|--------|------|
| banner | 轮播图 |
| name | 名字 |
| count | 浏览数量 |
| isSuggest | 是否推荐 0否 1是 |
| introduction | 案例简介 |
| content | 案例内容 |
| createName | 发布人 |
| smallImg | 小图 |
| likeCount | 点赞数量 |
| favoriteCount | 收藏数量 |
| categorySceneName | 场景分类 |
| categoryName | 方案名称 |
| list | 产品明细 |
| softList | 产品明细软件 |
| hardList | 产品明细硬件 |
| programmeDetails | 方案明细 |
| other | 附件 |
| video | 视频 |
| businessList | 事业部列表 |
| distributionOrderPlatformList | 关联配单 |

---

## 5. 产品关联资讯接口

### 接口说明
根据当前用户携带token，查询所属站点的咨询列表接口。

### 请求信息
- **请求路径**: `http://{zkmall}/api/system/information/list?productId={productId}`
- **请求方式**: `GET`
- **请求头**: `Authorization: {token}`

### 请求参数
| 参数名 | 字段类型 | 说明 |
|--------|----------|------|
| pageSize | String | 条数 |
| current | String | 页数 |
| orderByColumn | String | 排序（watch按照浏览量排序） |
| isAsc | String | 正序倒序（desc倒序） |

### 返回参数说明
| 参数名 | 说明 |
|--------|------|
| id | 主键 |
| status | 状态 0正常 1停用 |
| title | 标题 |
| details | 详情富文本 |
| otherUrl | 附件地址 |
| videoUrl | 视频地址 |
| picVideo | 图片 |
| smallImg | 小图 |
| isSuggest | 是否推荐 0是 1否 |
| watch | 浏览量 |
| categoryName | 分类名称 |
| likeCount | 点赞数量 |
| isLike | 是否点赞 0未点赞 1已点赞 |
| favoriteCount | 收藏数量 |
| isFavorite | 是否收藏 0否 1是 |

---

## 6. 产品关联配单接口

### 接口说明
根据当前用户携带token，查询我的配单列表接口。

### 请求信息
- **请求路径**: `http://{zkmall}/api/distributionOrderPlatform/list?productId={productId}`
- **请求方式**: `GET`
- **请求头**: `Authorization: {token}`

### 返回参数说明
| 参数名 | 说明 |
|--------|------|
| id | 配单id |
| status | 状态 |
| name | 配单名字 |
| company_id | 公司id |
| user_id | 用户id |
| hide_price | 0是 1否 |
| source_type | 0产品配单 1方案配单 |
| from_user_id | 来源id，-1为总平台 |
| like_count | 点赞数量 |
| favorite_count | 收藏数量 |
| firCategoryName | 一级分类名 |
| secCategoryName | 二级分类名 |
| hasExpire | 是否有下架的产品 0否 1是 |

---

## 通用说明

### 认证方式
所有需要认证的接口都需要在请求头中携带Authorization字段，值为登录接口返回的token。

### 响应格式
所有接口都遵循统一的响应格式：
```json
{
  "msg": "操作成功",
  "code": 200,
  "data": {}
}
```

### 状态码说明
- `200`: 操作成功
- 其他状态码表示不同的错误情况

### 分页参数
对于支持分页的接口，通常使用以下参数：
- `pageSize`: 每页条数
- `current`: 当前页数
- `orderByColumn`: 排序字段
- `isAsc`: 排序方式（desc为倒序）