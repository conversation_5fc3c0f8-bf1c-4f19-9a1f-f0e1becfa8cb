# 🤖 硅基流动索引生成系统

基于硅基流动大模型的智能知识库索引生成与FastGPT同步系统

## 系统概述

本系统旨在解决多数据源到知识库的自动化同步问题，确保知识库内容的高质量和实时性。系统支持：

- **多数据源支持**：云商API、本地文件、历史数据
- **增量更新**：智能检测数据变更，避免重复处理
- **管理员审核**：确保数据质量，支持批量审核
- **FastGPT集成**：无缝对接FastGPT知识库接口
- **完整日志**：详细的操作记录和错误追踪

## 核心功能模块

### 1. 数据处理核心 (`product_data_processor.py`)
- 初始化PostgreSQL数据库表结构
- 导入`allcollections.json`数据并标记为作废附件
- 从熵基云商API获取产品数据并下载附件
- 根据产品结构数据扫描本地文件系统，自动识别产品资料
- 处理各种格式的附件文件（Word、Excel、PDF、PPT）
- 自动识别文件类型并标记文档类型
- 区分不同数据源（历史数据 vs 云商数据 vs 本地文件）

### 2. 增强文档处理器 🆕
- **智能格式优先级**: Markdown > Word > PDF 的智能选择策略
- **PDF智能解析**: 集成TextIn API，将PDF转换为高质量Markdown
- **多模态图片标注**: 使用AI模型自动标注图片内容
- **增量内容补充**: 智能检测并补充知识库内容
- **高质量输出**: 确保最终知识内容的准确性和完整性

### 3. 同步管理器 (`fastgpt_sync_manager.py`)
- 变更检测和增量更新机制
- 待审核数据管理
- FastGPT接口集成
- 同步状态跟踪和日志记录

### 4. Web管理界面 (`admin_review_app.py`)
- 管理员审核界面
- 批量审核功能
- 同步日志查看
- 统计信息展示

## 环境要求

- Python 3.8+
- PostgreSQL 12+
- 足够的磁盘空间用于存储处理后的数据

## 安装依赖

```bash
pip install -r requirements.txt
```

## 环境变量配置

1. 复制环境变量示例文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置必要的API密钥：
```bash
# FastGPT配置
FASTGPT_API_KEY=your_fastgpt_api_key
FASTGPT_KB_ID=your_knowledge_base_id

# TextIn API配置（PDF解析）
TEXTIN_APP_ID=your_textin_app_id
TEXTIN_SECRET_CODE=your_textin_secret_code

# OpenAI配置（图片标注）
OPENAI_API_KEY=your_openai_api_key

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=sync_fastgpt
DB_USER=postgres
DB_PASSWORD=your_database_password
```

## 配置说明

### 数据库配置

在`product_data_processor.py`的`main()`函数中修改数据库配置：

```python
db_config = {
    'host': '***********',      # 数据库主机
    'user': 'username',         # 数据库用户名
    'password': '123456',       # 数据库密码
    'dbname': 'product',        # 数据库名
    'port': 5432               # 数据库端口
}
```

### API配置

在`product_data_processor.py`的`main()`函数中修改API配置：

```python
api_config = {
    'base_url': 'https://zkmall.zktecoip.com',  # API基础URL
    'username': '18929343717',                   # API用户名
    'password': 'Zk@123456'                     # API密码
}
```

### 文件路径配置

确保相关文件路径正确：

```python
allcollections_path = 'd:\\sync-db-fastgpt\\allcollections.json'
product_structure_path = 'd:\\sync-db-fastgpt\\国内产品结构细化表.json'
local_files_base_path = 'E:\\18.AI客服知识库\\18.AI客服知识库'
```

## 数据库表结构

脚本会自动创建以下表：

### ProductCategory（产品分类表）
- `category_id`: 分类ID（主键）
- `category_name`: 分类名称
- `parent_id`: 父分类ID
- `category_level`: 分类层级
- `sort_order`: 排序
- `description`: 描述
- `is_active`: 是否激活
- `created_at`: 创建时间
- `updated_at`: 更新时间

### Product（产品主表）
- `product_id`: 产品ID（主键）
- `product_category`: 产品分类
- `product_type`: 产品类型
- `product_series`: 产品系列
- `product_model`: 产品型号
- `product_name`: 产品名称
- `description`: 描述
- `introduction`: 介绍
- `small_img`: 小图片
- `banner`: 横幅图片
- `attribute`: 属性
- `label`: 标签
- `show_for`: 展示对象
- `use_to`: 用途
- `price`: 价格
- `like_count`: 点赞数
- `favorite_count`: 收藏数
- `view_count`: 浏览数
- `is_suggest`: 是否推荐
- `guide`: 指南
- `details`: 详情
- `other_attachments`: 其他附件
- `site_id`: 站点ID
- `fastgpt_id`: FastGPT ID
- `document_name`: 文档名称
- `material_usage`: 资料用途
- `business_scope`: 业务范围
- `launch_time`: 上市时间
- `supported_software`: 支持的软件
- `collection_status`: 收集状态
- `external_id`: 外部ID
- `sync_status`: 同步状态
- `last_sync_time`: 最后同步时间
- `data_source`: 数据来源
- `status`: 状态
- `created_at`: 创建时间
- `updated_at`: 更新时间

### DocumentType（资料类型表）
- `type_id`: 类型ID（主键）
- `type_name`: 类型名称
- `type_code`: 类型代码
- `description`: 描述
- `is_required`: 是否必需
- `sort_order`: 排序
- `document_category`: 文档分类
- `priority`: 优先级
- `template_path`: 模板路径
- `file_extensions`: 文件扩展名
- `max_file_size`: 最大文件大小
- `quality_requirements`: 质量要求
- `review_required`: 是否需要审核
- `created_at`: 创建时间
- `updated_at`: 更新时间

### ProductDocument（产品资料表）
- `document_id`: 文档ID（主键）
- `product_id`: 产品ID（外键）
- `type_id`: 类型ID（外键）
- `document_name`: 文档名称
- `file_path`: 文件路径
- `file_size`: 文件大小
- `file_format`: 文件格式
- `file_extension`: 文件扩展名
- `file_type`: 文件类型描述
- `mime_type`: MIME类型
- `local_file_path`: 本地文件路径
- `relative_path`: 相对路径
- `file_hash`: 文件哈希
- `version`: 版本
- `language`: 语言
- `business_unit`: 业务单元
- `consultation_type`: 咨询类型
- `upload_date`: 上传日期
- `fastgpt_id`: FastGPT ID
- `original_filename`: 原始文件名
- `material_usage`: 资料用途
- `business_scope`: 业务范围
- `collection_status`: 收集状态
- `collector`: 收集人
- `collection_date`: 收集日期
- `review_status`: 审核状态
- `reviewer`: 审核人
- `review_date`: 审核日期
- `review_comments`: 审核意见
- `data_source`: 数据来源
- `is_deprecated`: 是否作废
- `metadata`: 元数据（JSONB）
- `status`: 状态
- `created_at`: 创建时间
- `updated_at`: 更新时间

### ProductSoftware（软件关联表）
- `id`: ID（主键）
- `product_id`: 产品ID（外键）
- `software_name`: 软件名称
- `software_version`: 软件版本
- `compatibility_level`: 兼容性级别
- `notes`: 备注
- `created_at`: 创建时间

## 使用方法

### 方法一：一键执行（推荐）

1. **配置系统**
   - 编辑 `config.py` 文件，修改数据库和API配置
   - 详细配置说明请参考 `配置说明.md`

2. **运行批处理脚本**
   ```bash
   run_all.bat
   ```
   或直接双击 `run_all.bat` 文件

### 方法二：手动执行

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **配置系统**
   编辑 `config.py` 中的配置信息：
   ```python
   # 数据库配置
   DATABASE_CONFIG = {
       'host': 'localhost',
       'user': 'postgres',
       'password': 'your_password',
       'dbname': 'product_knowledge_db',
       'port': 5432
   }
   
   # API配置
   API_CONFIG = {
       'base_url': 'https://zkmall.zktecoip.com',
       'username': 'your_username',
       'password': 'your_password'
   }
   ```

3. **运行脚本**
   ```bash
   python product_data_processor.py
   ```

### 方法三：分步执行

1. **初始化数据库**
   ```bash
   python product_data_processor.py --init-db
   ```

2. **导入历史数据**
   ```bash
   python product_data_processor.py --import-history
   ```

3. **获取云商数据**
   ```bash
   python product_data_processor.py --fetch-cloud
   ```

4. **扫描本地文件**
   ```bash
   python product_data_processor.py --scan-local
   ```

5. **使用增强文档处理器** 🆕

   #### 单个产品文档处理
   ```python
   from enhanced_document_processor import EnhancedDocumentProcessor
   from config import DATABASE_CONFIG
   
   # 创建处理器实例
   processor = EnhancedDocumentProcessor(DATABASE_CONFIG)
   
   # 处理单个产品文档（包含图片相关性检测）
   success = processor.process_product_documents(
       product_id="PRODUCT_001",
       category="用户手册",
       base_path="/path/to/product/documents"
   )
   ```

   #### 批量文档处理
   ```python
   # 批量处理配置
   products_config = [
       {
           'product_id': 'PRODUCT_001',
           'category': '用户手册',
           'base_path': '/path/to/product001/manuals'
       },
       {
           'product_id': 'PRODUCT_002',
           'category': '技术规格',
           'base_path': '/path/to/product002/specs'
       }
   ]
   
   # 执行批量处理
   results = processor.batch_process_products(products_config)
   print(f"处理结果: 成功 {results['success']}, 失败 {results['failed']}")
   
   # 单独测试图片相关性检测
   is_relevant, analysis = processor.check_image_relevance(
       image_path="/path/to/image.jpg",
       context="产品技术规格文档内容..."
   )
   print(f"图片相关性: {is_relevant}")
   print(f"分析结果: {analysis}")
   ```

   #### 运行示例
   ```bash
   # 运行完整示例
   python example_usage.py
   ```

6. **启动Web管理界面**
   ```bash
   python admin_review_app.py
   ```

   然后访问 http://localhost:5000 进行管理员审核操作。

## 增强文档处理器特性 🆕

### 智能文件格式优先级
- **Markdown优先**: 优先选择.md/.markdown格式文件
- **Word次之**: 其次选择.docx/.doc格式文件  
- **PDF最后**: 最后选择.pdf格式文件
- **智能选择**: 同一产品同一类别自动选择最优格式

### PDF智能解析
- **TextIn API集成**: 使用专业PDF解析服务
- **高质量转换**: 将PDF转换为结构化Markdown
- **保留格式**: 保持原文档的布局和结构
- **图片提取**: 自动提取PDF中的图片资源

### 多模态图片标注与智能过滤
- **集成GPT-4V**: 使用OpenAI GPT-4 Vision进行AI自动标注
- **智能相关性检测**: 自动分析图片与文档内容的相关性
- **无关图片清理**: 自动移除装饰性图片、广告图片等无关内容
- **相关性评分**: 对图片进行0-10分的相关性评分
- **多重判断标准**: 结合评分、关键词和明确指令进行综合判断
- **上下文理解**: 结合文档内容提供准确的图片描述
- **内容绑定**: 将图片描述直接嵌入到文档内容中
- **处理统计报告**: 记录图片处理详情，包括保留和移除的图片信息

### 增量内容补充
- **智能检测**: 检测FastGPT中的现有内容
- **增量更新**: 只更新变化的部分，避免重复
- **版本管理**: 跟踪内容变更历史
- **质量保证**: 确保更新内容的准确性

## 处理流程

### 传统数据处理流程
1. **数据库连接**: 连接到PostgreSQL数据库
2. **表结构初始化**: 创建所有必要的表和索引
3. **初始数据插入**: 插入文档类型等基础数据
4. **导入历史数据**: 从`allcollections.json`导入数据并标记为作废
5. **API登录**: 登录熵基云商API获取访问令牌
6. **获取产品数据**: 分页获取所有产品信息
7. **下载附件**: 下载产品相关的附件文件
8. **本地文件扫描**: 根据产品结构数据扫描本地文件系统
9. **文件类型识别**: 自动识别文件类型和文档类型
10. **元数据提取**: 提取文件的元数据信息
11. **数据入库**: 将产品和附件信息保存到数据库

### 增强文档处理流程 🆕
1. **文档扫描**: 扫描指定路径下的所有支持格式文档
2. **格式优先级排序**: 按Markdown > Word > PDF优先级排序
3. **最优文档选择**: 为每个产品类别选择最优格式文档
4. **内容解析处理**:
   - Markdown: 直接读取内容
   - Word: 解析文档结构和内容
   - PDF: 使用TextIn API转换为Markdown
5. **图片提取标注**: 提取文档中的图片并使用AI进行标注
6. **现有内容检查**: 检查FastGPT中是否已存在相关内容
7. **增量内容生成**: 生成增量更新内容或创建新内容
8. **质量检查**: 验证内容质量和完整性
9. **待审核记录创建**: 创建待管理员审核的同步记录
10. **管理员审核**: 通过Web界面进行人工审核
11. **FastGPT同步**: 将审核通过的内容同步到知识库

## API接口说明

### TextIn PDF解析API
```python
# 配置信息
TEXTIN_CONFIG = {
    'app_id': 'your_app_id',
    'secret_code': 'your_secret_code',
    'base_url': 'https://api.textin.com/ai/service/v1/pdf_to_markdown'
}

# 请求参数
params = {
    'pdf_pwd': '',          # PDF密码（可选）
    'char_details': True,   # 字符详情
    'page_details': True,   # 页面详情
    'catalog_details': True, # 目录详情
    'dpi': 200,            # 图片DPI
    'page_start': 1        # 起始页码
}
```

### FastGPT知识库API
```python
# 知识库操作接口
FASTGPT_CONFIG = {
    'base_url': 'https://your-fastgpt-domain.com',
    'api_key': 'your_api_key',
    'dataset_id': 'your_dataset_id'
}

# 主要接口
- GET /api/core/dataset/data/list    # 获取知识库内容
- POST /api/core/dataset/data/push   # 推送新内容
- PUT /api/core/dataset/data/update  # 更新现有内容
- DELETE /api/core/dataset/data/delete # 删除内容
```

### 多模态AI接口
```python
# OpenAI GPT-4V配置
OPENAI_CONFIG = {
    'api_key': 'your_openai_api_key',
    'model': 'gpt-4-vision-preview',
    'max_tokens': 1000
}

# Claude配置
CLAUDE_CONFIG = {
    'api_key': 'your_claude_api_key',
    'model': 'claude-3-opus-20240229'
}
```

## 数据来源标记

- `legacy`: 来自`allcollections.json`的历史数据（标记为作废）
- `yunshang`: 来自熵基云商API的数据
- `local`: 来自本地文件系统扫描的数据
- `enhanced_processing`: 来自增强文档处理器的数据 🆕

## 文件类型识别

系统支持以下文件类型的自动识别和处理：

### 文档类型
- **Markdown文件** (.md, .markdown) - 🆕 优先级最高
- **Word文档** (.doc, .docx) - 🆕 优先级中等
- **PDF文件** (.pdf) - 🆕 优先级最低，支持AI解析
- Excel表格 (.xls, .xlsx)
- PowerPoint演示文稿 (.ppt, .pptx)
- 文本文件 (.txt)

### 图片类型
- JPEG (.jpg, .jpeg) - 🆕 支持AI标注
- PNG (.png) - 🆕 支持AI标注
- GIF (.gif)
- BMP (.bmp)
- TIFF (.tif, .tiff)

### 压缩文件
- ZIP (.zip)
- RAR (.rar)
- 7Z (.7z)

### 其他类型
- 视频文件 (.mp4, .avi, .mov等)
- 音频文件 (.mp3, .wav, .flac等)
- CAD文件 (.dwg, .dxf)
- 3D模型文件 (.step, .iges等)

## 文档类型推断

根据文件名关键词自动推断文档类型：
- **产品彩页**：包含"彩页"、"brochure"、"宣传"等关键词
- **快速入门**：包含"入门"、"quick"、"start"、"快速"等关键词
- **用户手册**：包含"用户手册"、"user"、"manual"、"使用说明"等关键词
- **技术规格**：包含"技术"、"tech"、"spec"、"规格"等关键词
- **安装指南**：包含"安装"、"install"、"setup"等关键词
- **故障排除**：包含"故障"、"trouble"、"problem"、"问题"等关键词

## 故障排除

### 常见问题

#### 1. PDF解析失败
```bash
# 检查TextIn API配置
ERROR: TextIn API authentication failed
解决方案: 检查.env文件中的TEXTIN_APP_ID和TEXTIN_SECRET_CODE
```

#### 2. 多模态AI标注失败
```bash
# OpenAI API错误
ERROR: OpenAI API rate limit exceeded
解决方案: 检查API配额或切换到其他模型
```

#### 图片相关性检测问题
- **误删重要图片**: 调整相关性评分阈值（默认6分以上保留）
- **保留无关图片**: 检查文档上下文是否准确传递
- **检测结果不准确**: 优化提示词或增加更多上下文信息
- **处理速度慢**: 考虑批量处理或缓存机制
- **API调用失败**: 检查多模态AI服务的可用性和配额

#### 3. FastGPT同步失败
```bash
# 知识库连接错误
ERROR: FastGPT API connection timeout
解决方案: 检查网络连接和API密钥配置
```

#### 4. 文件格式不支持
```bash
# 文件格式错误
WARNING: Unsupported file format detected
解决方案: 确保文件格式在支持列表中
```

### 性能优化

#### 1. 批量处理优化
```python
# 调整批量处理大小
BATCH_SIZE = 10  # 根据系统性能调整
MAX_WORKERS = 4  # 并发处理数量
```

#### 2. 内存管理
```python
# 大文件处理
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
CHUNK_SIZE = 1024 * 1024  # 1MB chunks
```

#### 3. 缓存配置
```python
# Redis缓存配置
CACHE_CONFIG = {
    'host': 'localhost',
    'port': 6379,
    'db': 0,
    'expire_time': 3600  # 1小时
}
```

## 最佳实践

### 1. 文档组织结构
```
product_documents/
├── PRODUCT_001/
│   ├── 用户手册/
│   │   ├── manual.md        # 优先选择
│   │   ├── manual.docx      # 次选
│   │   └── manual.pdf       # 最后选择
│   ├── 技术规格/
│   └── 安装指南/
└── PRODUCT_002/
    ├── 用户手册/
    └── 维护手册/
```

### 2. 内容质量控制
- **文档标准化**: 使用统一的文档模板和格式
- **图片优化**: 确保图片清晰度和相关性
- **内容审核**: 建立多级审核机制
- **版本管理**: 跟踪文档版本变更

### 3. 监控和日志
```python
# 启用详细日志
LOG_LEVEL = 'DEBUG'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# 监控关键指标
MONITOR_METRICS = [
    'processing_time',
    'success_rate',
    'error_count',
    'api_usage'
]
```

### 4. 安全建议
- **API密钥管理**: 使用环境变量存储敏感信息
- **访问控制**: 限制Web界面访问权限
- **数据备份**: 定期备份数据库和配置文件
- **日志审计**: 记录所有操作日志用于审计

## 附件存储

附件文件会下载到`attachments/`目录下，按产品ID分组存储：

```
attachments/
├── yunshang_123/
│   ├── attachment_1.pdf
│   └── attachment_2.docx
└── yunshang_456/
    └── attachment_1.xlsx
```

## 日志记录

脚本会生成详细的日志记录，保存在`product_data_processor.log`文件中，同时在控制台输出。

## 错误处理

脚本包含完善的错误处理机制：
- 数据库连接失败会自动重试
- API请求失败会记录错误并继续处理其他数据
- 文件下载失败不会影响其他附件的处理
- 所有错误都会记录到日志文件中

## 性能优化

- 使用批量插入减少数据库操作
- 添加适当的数据库索引
- 控制API请求频率避免被限制
- 使用流式下载处理大文件

## 文件说明

- `run_all.bat`: 一键执行批处理脚本
- `config.py`: 系统配置文件
- `product_data_processor.py`: 主处理程序
- `配置说明.md`: 详细配置说明文档
- `requirements.txt`: Python依赖包列表
- `allcollections.json`: 历史数据文件
- `国内产品结构细化表.json`: 产品结构数据

## 重要说明

- 请确保PostgreSQL数据库已安装并运行
- 运行前请先修改 `config.py` 中的配置信息
- 请确保有足够的磁盘空间存储下载的附件
- 建议在测试环境中先运行脚本验证功能
- 系统会自动创建 `logs` 和 `downloads` 目录

## 注意事项

1. 确保数据库用户有足够的权限创建表和索引
2. 确保有足够的磁盘空间存储下载的附件
3. 网络连接要稳定，避免下载中断
4. 定期清理日志文件避免占用过多空间
5. 建议在测试环境先运行验证无误后再在生产环境使用
6. **🆕 TextIn API**: 确保有足够的PDF解析配额
7. **🆕 多模态AI**: 注意API调用频率限制和成本控制
8. **🆕 文件格式**: 优先使用Markdown格式以获得最佳处理效果

## 版本历史

### v2.0.0 (最新) 🆕
- ✨ 新增增强文档处理器
- ✨ 支持智能文件格式优先级选择
- ✨ 集成TextIn PDF解析API
- ✨ 支持多模态AI图片标注
- ✨ 实现增量内容补充功能
- ✨ 新增Web管理界面
- ✨ 完善的日志和监控系统
- 🔧 优化性能和错误处理
- 📚 完善文档和示例

### v1.0.0
- 🎉 初始版本发布
- ✨ 基础数据处理功能
- ✨ 云商API集成
- ✨ 本地文件扫描
- ✨ 数据库存储
- ✨ 文件类型识别

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请通过以下方式联系：

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 文档: [在线文档](https://docs.example.com)
- 💬 讨论: [GitHub Discussions](https://github.com/your-repo/discussions)

## 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 开发环境设置

```bash
# 克隆仓库
git clone https://github.com/your-repo/sync-db-fastgpt.git
cd sync-db-fastgpt

# 安装开发依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt

# 运行测试
pytest tests/

# 代码格式化
black .
flake8 .
```

---

**FastGPT知识库数据同步系统** - 让知识管理更智能 🚀

## 传统故障排除

### 数据库连接失败
- 检查数据库服务是否运行
- 验证连接参数是否正确
- 确认网络连接是否正常

### API登录失败
- 检查用户名和密码是否正确
- 验证API地址是否可访问
- 确认账号是否被锁定

### 附件下载失败
- 检查网络连接
- 验证附件URL是否有效
- 确认磁盘空间是否充足

## 维护建议

1. 定期备份数据库
2. 监控日志文件大小
3. 定期清理过期的附件文件
4. 更新API凭据（如有变更）
5. 定期检查数据完整性