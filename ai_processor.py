# -*- coding: utf-8 -*-
"""
AI处理模块
整合硅基流动AI模型，提供文档处理、图片分析等功能
"""

import asyncio
import aiohttp
import logging
import json
import time
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import base64
from PIL import Image
import io

from unified_config import config_manager

logger = logging.getLogger(__name__)

@dataclass
class AIProcessingResult:
    """AI处理结果"""
    success: bool
    content: str = ""
    metadata: Dict[str, Any] = None
    processing_time: float = 0.0
    token_usage: int = 0
    cost: float = 0.0
    error_message: str = ""

@dataclass
class ImageAnalysisResult:
    """图片分析结果"""
    is_relevant: bool
    relevance_score: float
    description: str
    keywords: List[str]
    confidence: float
    processing_time: float

class AIProcessor:
    """AI处理器"""
    
    def __init__(self):
        self.config = config_manager.siliconflow
        self.session = None
        self.processing_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_tokens': 0,
            'total_cost': 0.0
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=120),
            headers=self._get_headers()
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'Authorization': f'Bearer {self.config.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'FastGPT-SyncDB/1.0'
        }
    
    async def _call_api(self, model_type: str, messages: List[Dict], 
                       task_description: str = "") -> AIProcessingResult:
        """调用AI API"""
        start_time = time.time()
        
        try:
            # 获取模型配置
            model_name = self.config.models.get(model_type)
            if not model_name:
                raise ValueError(f"不支持的模型类型: {model_type}")
            
            # 构建请求数据
            request_data = {
                "model": model_name,
                "messages": messages,
                "temperature": 0.1 if model_type == 'vision' else 0.3,
                "max_tokens": 4096,
                "stream": False
            }
            
            # 发送请求
            async with self.session.post(
                f"{self.config.api_base_url}/chat/completions",
                json=request_data
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    # 解析响应
                    content = result['choices'][0]['message']['content']
                    token_usage = result.get('usage', {}).get('total_tokens', 0)
                    
                    # 计算成本（示例价格）
                    cost = self._calculate_cost(model_type, token_usage)
                    
                    # 更新统计
                    self._update_stats(True, token_usage, cost)
                    
                    processing_time = time.time() - start_time
                    
                    return AIProcessingResult(
                        success=True,
                        content=content,
                        processing_time=processing_time,
                        token_usage=token_usage,
                        cost=cost,
                        metadata={
                            'model': model_name,
                            'task': task_description
                        }
                    )
                else:
                    error_text = await response.text()
                    raise Exception(f"API调用失败: {response.status} - {error_text}")
                    
        except Exception as e:
            self._update_stats(False, 0, 0)
            processing_time = time.time() - start_time
            
            logger.error(f"AI API调用失败: {e}")
            return AIProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=processing_time
            )
    
    def _calculate_cost(self, model_type: str, tokens: int) -> float:
        """计算API调用成本"""
        # 示例价格（需要根据实际情况调整）
        price_per_1k = {
            'vision': 0.002,
            'text': 0.001,
            'code': 0.0015,
            'lightweight': 0.0005
        }
        
        return (tokens / 1000) * price_per_1k.get(model_type, 0.001)
    
    def _update_stats(self, success: bool, tokens: int, cost: float):
        """更新统计信息"""
        self.processing_stats['total_requests'] += 1
        if success:
            self.processing_stats['successful_requests'] += 1
        else:
            self.processing_stats['failed_requests'] += 1
        
        self.processing_stats['total_tokens'] += tokens
        self.processing_stats['total_cost'] += cost
    
    async def analyze_image_relevance(self, image_path: str, 
                                    context: str) -> ImageAnalysisResult:
        """分析图片与内容的相关性"""
        try:
            # 读取和处理图片
            image_base64 = self._encode_image(image_path)
            
            # 构建提示词
            prompt = f"""
            请分析这张图片与以下文档内容的相关性：

            文档内容：
            {context[:1000]}...

            请从以下几个方面进行分析：
            1. 图片内容描述
            2. 与文档的相关性评分（0-10分）
            3. 相关关键词
            4. 是否建议保留这张图片

            请以JSON格式回复：
            {{
                "description": "图片内容描述",
                "relevance_score": 评分,
                "keywords": ["关键词1", "关键词2"],
                "is_relevant": true/false,
                "confidence": 置信度(0-1)
            }}
            """
            
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
            
            # 调用AI API
            result = await self._call_api('vision', messages, "图片相关性分析")
            
            if result.success:
                try:
                    # 解析JSON响应
                    analysis = json.loads(result.content)
                    
                    return ImageAnalysisResult(
                        is_relevant=analysis.get('is_relevant', False),
                        relevance_score=analysis.get('relevance_score', 0.0),
                        description=analysis.get('description', ''),
                        keywords=analysis.get('keywords', []),
                        confidence=analysis.get('confidence', 0.0),
                        processing_time=result.processing_time
                    )
                except json.JSONDecodeError:
                    # 如果JSON解析失败，尝试从文本中提取信息
                    return self._parse_text_analysis(result.content, result.processing_time)
            else:
                raise Exception(result.error_message)
                
        except Exception as e:
            logger.error(f"图片相关性分析失败: {e}")
            return ImageAnalysisResult(
                is_relevant=False,
                relevance_score=0.0,
                description=f"分析失败: {e}",
                keywords=[],
                confidence=0.0,
                processing_time=0.0
            )
    
    def _encode_image(self, image_path: str) -> str:
        """编码图片为base64"""
        try:
            with Image.open(image_path) as img:
                # 压缩图片以减少API调用成本
                if img.size[0] > 1024 or img.size[1] > 1024:
                    img.thumbnail((1024, 1024), Image.Resampling.LANCZOS)
                
                # 转换为RGB（如果是RGBA）
                if img.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                
                # 保存为字节流
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85)
                
                # 编码为base64
                return base64.b64encode(buffer.getvalue()).decode('utf-8')
                
        except Exception as e:
            logger.error(f"图片编码失败: {e}")
            raise
    
    def _parse_text_analysis(self, text: str, processing_time: float) -> ImageAnalysisResult:
        """从文本中解析分析结果"""
        # 简单的文本解析逻辑
        is_relevant = "相关" in text or "relevant" in text.lower()
        
        # 尝试提取评分
        import re
        score_match = re.search(r'(\d+(?:\.\d+)?)\s*分', text)
        relevance_score = float(score_match.group(1)) if score_match else 5.0
        
        return ImageAnalysisResult(
            is_relevant=is_relevant,
            relevance_score=relevance_score,
            description=text[:200],
            keywords=[],
            confidence=0.5,
            processing_time=processing_time
        )
    
    async def optimize_content_structure(self, content: str, 
                                       content_type: str = "markdown") -> AIProcessingResult:
        """优化内容结构"""
        prompt = f"""
        请优化以下{content_type}内容的结构和格式，使其更适合知识库存储：

        原始内容：
        {content}

        优化要求：
        1. 保持原有信息完整性
        2. 改善结构层次
        3. 统一格式规范
        4. 增强可读性
        5. 适合搜索和检索

        请直接返回优化后的内容，不需要额外说明。
        """
        
        messages = [{"role": "user", "content": prompt}]
        
        return await self._call_api('text', messages, "内容结构优化")
    
    async def generate_qa_pairs(self, content: str, 
                              max_pairs: int = 5) -> AIProcessingResult:
        """生成问答对"""
        prompt = f"""
        基于以下内容生成{max_pairs}个高质量的问答对，用于知识库问答系统：

        内容：
        {content}

        要求：
        1. 问题要具体、实用
        2. 答案要准确、完整
        3. 覆盖内容的关键信息
        4. 适合用户常见询问

        请以JSON格式返回：
        {{
            "qa_pairs": [
                {{"question": "问题1", "answer": "答案1"}},
                {{"question": "问题2", "answer": "答案2"}}
            ]
        }}
        """
        
        messages = [{"role": "user", "content": prompt}]
        
        return await self._call_api('text', messages, "问答对生成")
    
    async def extract_keywords(self, content: str, 
                             max_keywords: int = 10) -> AIProcessingResult:
        """提取关键词"""
        prompt = f"""
        从以下内容中提取{max_keywords}个最重要的关键词：

        内容：
        {content}

        要求：
        1. 关键词要准确反映内容主题
        2. 优先选择专业术语和核心概念
        3. 避免过于通用的词汇
        4. 适合用于搜索和分类

        请以JSON格式返回：
        {{"keywords": ["关键词1", "关键词2", ...]}}
        """
        
        messages = [{"role": "user", "content": prompt}]
        
        return await self._call_api('lightweight', messages, "关键词提取")
    
    async def batch_process_documents(self, documents: List[Dict[str, Any]], 
                                    processing_options: Dict[str, bool]) -> Dict[str, Any]:
        """批量处理文档"""
        results = {
            'processed': 0,
            'failed': 0,
            'total_cost': 0.0,
            'processing_time': 0.0,
            'details': []
        }
        
        start_time = time.time()
        
        for doc in documents:
            try:
                doc_result = {
                    'document_id': doc.get('id'),
                    'success': True,
                    'operations': {}
                }
                
                content = doc.get('content', '')
                
                # 内容优化
                if processing_options.get('optimize_content', False):
                    result = await self.optimize_content_structure(content)
                    doc_result['operations']['content_optimization'] = {
                        'success': result.success,
                        'cost': result.cost
                    }
                    if result.success:
                        content = result.content
                
                # 关键词提取
                if processing_options.get('extract_keywords', False):
                    result = await self.extract_keywords(content)
                    doc_result['operations']['keyword_extraction'] = {
                        'success': result.success,
                        'cost': result.cost
                    }
                
                # 问答对生成
                if processing_options.get('generate_qa', False):
                    result = await self.generate_qa_pairs(content)
                    doc_result['operations']['qa_generation'] = {
                        'success': result.success,
                        'cost': result.cost
                    }
                
                results['processed'] += 1
                results['details'].append(doc_result)
                
            except Exception as e:
                logger.error(f"文档处理失败 {doc.get('id')}: {e}")
                results['failed'] += 1
                results['details'].append({
                    'document_id': doc.get('id'),
                    'success': False,
                    'error': str(e)
                })
        
        results['processing_time'] = time.time() - start_time
        results['total_cost'] = self.processing_stats['total_cost']
        
        return results
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        stats = self.processing_stats.copy()
        
        if stats['total_requests'] > 0:
            stats['success_rate'] = (stats['successful_requests'] / stats['total_requests']) * 100
            stats['avg_cost_per_request'] = stats['total_cost'] / stats['total_requests']
        else:
            stats['success_rate'] = 0
            stats['avg_cost_per_request'] = 0
        
        return stats

# 全局AI处理器实例
ai_processor = AIProcessor()
